{"version": 3, "sources": ["utils/throwError.js", "../../src/utils/throwError.ts"], "names": ["Object", "defineProperty", "exports", "value", "throwIteratorAccessError", "RangeError"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,2BAA2BA;;ACD7B,SAAUA;IACd,MAAM,IAAIC,WAAW;ADCvB", "file": "throwError.js", "sourcesContent": ["/**\n * @description Throw an iterator access error.\n * @internal\n */\nexport function throwIteratorAccessError() {\n    throw new RangeError('Iterator access denied!');\n}\n", "/**\n * @description Throw an iterator access error.\n * @internal\n */\nexport function throwIteratorAccessError() {\n  throw new RangeError('Iterator access denied!');\n}\n"]}