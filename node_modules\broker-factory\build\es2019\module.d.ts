import { IWorkerDefinition } from 'worker-factory';
import { IBrokerDefinition, IDefaultBrokerDefinition } from './interfaces';
import { TBrokerImplementation } from './types';
export * from './interfaces/index';
export * from './types/index';
export declare const createBroker: <T extends IBrokerDefinition, U extends IWorkerDefinition>(brokerImplementation: TBrokerImplementation<T, U>) => ((sender: MessagePort | Worker) => T & IDefaultBrokerDefinition);
//# sourceMappingURL=module.d.ts.map