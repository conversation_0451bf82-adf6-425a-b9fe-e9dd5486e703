{"version": 3, "sources": ["container/HashContainer/Base/index.js", "../../src/container/HashContainer/Base/index.ts"], "names": ["Object", "defineProperty", "exports", "value", "HashContainerIterator", "HashC<PERSON>r", "_ContainerBase", "require", "_checkObject", "_interopRequireDefault", "_throwError", "obj", "__esModule", "default", "ContainerIterator", "constructor", "node", "header", "iteratorType", "super", "this", "_node", "_header", "pre", "_pre", "throwIteratorAccessError", "next", "_next", "Container", "_objMap", "_originMap", "HASH_TAG", "Symbol", "setPrototypeOf", "_head", "_tail", "_eraseNode", "L", "B", "_length", "_set", "key", "isObject", "undefined", "checkObject", "newTail", "index", "_value", "length", "configurable", "_key", "push", "_findElementNode", "clear", "for<PERSON>ach", "el", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eraseElementByIterator", "iter", "eraseElementByPos", "pos", "RangeError"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,wBAAwBF,QAAQG,qBAAqB;;ACL7D,IAAAC,iBAAAC,QAAA;;AACA,IAAAC,eAAAC,uBAAAF,QAAA;;AAEA,IAAAG,cAAAH,QAAA;;AAA8D,SAAAE,uBAAAE;IAAA,OAAAA,KAAAA,EAAAC,IAAAD,IAAA;QAAAE,SAAAF;;AAAA;;AASxD,MAAgBP,8BAAoCU,eAAAA;IAaxDC,YACEC,GACAC,GACAC;QAEAC,MAAMD;QACNE,KAAKC,IAAQL;QACbI,KAAKE,IAAUL;QACf,IAAIG,KAAKF,iBAAY,GAA0B;YAC7CE,KAAKG,MAAM;gBACT,IAAIH,KAAKC,EAAMG,MAASJ,KAAKE,GAAS;qBACpC,GAAAG,YAAAA;ADpBM;gBCsBRL,KAAKC,IAAQD,KAAKC,EAAMG;gBACxB,OAAOJ;ADpBH;YCsBNA,KAAKM,OAAO;gBACV,IAAIN,KAAKC,MAAUD,KAAKE,GAAS;qBAC/B,GAAAG,YAAAA;ADpBM;gBCsBRL,KAAKC,IAAQD,KAAKC,EAAMM;gBACxB,OAAOP;ADpBH;AACJ,eCqBG;YACLA,KAAKG,MAAM;gBACT,IAAIH,KAAKC,EAAMM,MAAUP,KAAKE,GAAS;qBACrC,GAAAG,YAAAA;ADnBM;gBCqBRL,KAAKC,IAAQD,KAAKC,EAAMM;gBACxB,OAAOP;ADnBH;YCqBNA,KAAKM,OAAO;gBACV,IAAIN,KAAKC,MAAUD,KAAKE,GAAS;qBAC/B,GAAAG,YAAAA;ADnBM;gBCqBRL,KAAKC,IAAQD,KAAKC,EAAMG;gBACxB,OAAOJ;ADnBH;AACJ;AACJ;;;ACyBHlB,QAAAE,wBAAAA;;AAEK,MAAgBC,sBAA4BuB,eAAAA;IA4BhDb;QACEI;QAzBQC,KAAAS,IAAgC;QAIhCT,KAAAU,IAAiD,CAAA;QAgBlDV,KAAAW,WAAWC,OAAO;QAMzBhC,OAAOiC,eAAeb,KAAKU,GAAY;QACvCV,KAAKE,IAA8B,CAAA;QACnCF,KAAKE,EAAQE,IAAOJ,KAAKE,EAAQK,IAAQP,KAAKc,IAAQd,KAAKe,IAAQf,KAAKE;ADpCxE;ICyCQc,EAAWpB;QACnB,OAAMqB,GAAEb,GAAIc,GAAEX,KAAUX;QACxBQ,EAAKG,IAAQA;QACbA,EAAMH,IAAOA;QACb,IAAIR,MAASI,KAAKc,GAAO;YACvBd,KAAKc,IAAQP;ADpCX;QCsCJ,IAAIX,MAASI,KAAKe,GAAO;YACvBf,KAAKe,IAAQX;ADpCX;QCsCJJ,KAAKmB,KAAW;ADpChB;ICyCQC,EAAKC,GAAQtC,GAAWuC;QAChC,IAAIA,MAAaC,WAAWD,KAAW,GAAAE,aAAAA,SAAYH;QACnD,IAAII;QACJ,IAAIH,GAAU;YACZ,MAAMI,IAA0CL,EAAKrB,KAAKW;YAC1D,IAAIe,MAAUH,WAAW;gBACvBvB,KAAKS,EAAgBiB,GAAOC,IAAY5C;gBACxC,OAAOiB,KAAKmB;ADnCR;YCqCNvC,OAAOC,eAAewC,GAAKrB,KAAKW,UAAU;gBACxC5B,OAAOiB,KAAKS,EAAQmB;gBACpBC,cAAc;;YAEhBJ,IAAU;gBACRK,GAAMT;gBACNM,GAAW5C;gBACXqB,GAAMJ,KAAKe;gBACXR,GAAOP,KAAKE;;YAEdF,KAAKS,EAAQsB,KAAKN;ADnChB,eCoCG;YACL,MAAM7B,IAAOI,KAAKU,EAA4BW;YAC9C,IAAIzB,GAAM;gBACRA,EAAK+B,IAAY5C;gBACjB,OAAOiB,KAAKmB;ADlCR;YCoCNM,IAAU;gBACRK,GAAMT;gBACNM,GAAW5C;gBACXqB,GAAMJ,KAAKe;gBACXR,GAAOP,KAAKE;;YAEdF,KAAKU,EAA4BW,KAAOI;ADlCtC;QCoCJ,IAAIzB,KAAKmB,MAAY,GAAG;YACtBnB,KAAKc,IAAQW;YACbzB,KAAKE,EAAQK,IAAQkB;ADlCnB,eCmCG;YACLzB,KAAKe,EAAMR,IAAQkB;ADjCjB;QCmCJzB,KAAKe,IAAQU;QACbzB,KAAKE,EAAQE,IAAOqB;QACpB,SAASzB,KAAKmB;ADjCd;ICsCQa,EAAiBX,GAAQC;QACjC,IAAIA,MAAaC,WAAWD,KAAW,GAAAE,aAAAA,SAAYH;QACnD,IAAIC,GAAU;YACZ,MAAMI,IAA0CL,EAAKrB,KAAKW;YAC1D,IAAIe,MAAUH,WAAW,OAAOvB,KAAKE;YACrC,OAAOF,KAAKS,EAAQiB;AD/BlB,eCgCG;YACL,OAAO1B,KAAKU,EAA4BW,MAAQrB,KAAKE;AD9BnD;AACJ;ICgCF+B;QACE,MAAMtB,IAAWX,KAAKW;QACtBX,KAAKS,EAAQyB,SAAQ,SAAUC;mBACYA,EAAGL,EAAMnB;AD9BhD;QCgCJX,KAAKS,IAAU;QACfT,KAAKU,IAAa,CAAA;QAClB9B,OAAOiC,eAAeb,KAAKU,GAAY;QACvCV,KAAKmB,IAAU;QACfnB,KAAKc,IAAQd,KAAKe,IAAQf,KAAKE,EAAQE,IAAOJ,KAAKE,EAAQK,IAAQP,KAAKE;AD9BxE;ICuCFkC,kBAAkBf,GAAQC;QACxB,IAAI1B;QACJ,IAAI0B,MAAaC,WAAWD,KAAW,GAAAE,aAAAA,SAAYH;QACnD,IAAIC,GAAU;YACZ,MAAMI,IAA0CL,EAAKrB,KAAKW;YAC1D,IAAIe,MAAUH,WAAW,OAAO;mBACSF,EAAKrB,KAAKW;YACnDf,IAAOI,KAAKS,EAAQiB;mBACb1B,KAAKS,EAAQiB;AD5BlB,eC6BG;YACL9B,IAAOI,KAAKU,EAA4BW;YACxC,IAAIzB,MAAS2B,WAAW,OAAO;mBACxBvB,KAAKU,EAA4BW;AD1BtC;QC4BJrB,KAAKgB,EAAWpB;QAChB,OAAO;AD1BP;IC4BFyC,uBAAuBC;QACrB,MAAM1C,IAAO0C,EAAKrC;QAClB,IAAIL,MAASI,KAAKE,GAAS;aACzB,GAAAG,YAAAA;AD1BE;QC4BJL,KAAKgB,EAAWpB;QAChB,OAAO0C,EAAKhC;AD1BZ;IC4BFiC,kBAAkBC;QD1BZ,IC2BsBA,IAAG,KAAHA,IAAQxC,KAAKmB,IAAO,GAvN5C;YAAG,MAAS,IAAIsB;AD8Ld;QC0BJ,IAAI7C,IAAOI,KAAKc;QAChB,OAAO0B,KAAO;YACZ5C,IAAOA,EAAKW;ADxBV;QC0BJP,KAAKgB,EAAWpB;QAChB,OAAOI,KAAKmB;ADxBZ;;;AC0BHrC,QAAAG,gBAAAA", "file": "index.js", "sourcesContent": ["import { Container, ContainerIterator } from \"../../ContainerBase\";\nimport checkObject from \"../../../utils/checkObject\";\nimport $checkWithinAccessParams from \"../../../utils/checkParams.macro\";\nimport { throwIteratorAccessError } from \"../../../utils/throwError\";\nexport class HashContainerIterator extends ContainerIterator {\n    /**\n     * @internal\n     */\n    constructor(node, header, iteratorType) {\n        super(iteratorType);\n        this._node = node;\n        this._header = header;\n        if (this.iteratorType === 0 /* IteratorType.NORMAL */) {\n            this.pre = function () {\n                if (this._node._pre === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._pre;\n                return this;\n            };\n            this.next = function () {\n                if (this._node === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._next;\n                return this;\n            };\n        }\n        else {\n            this.pre = function () {\n                if (this._node._next === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._next;\n                return this;\n            };\n            this.next = function () {\n                if (this._node === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._pre;\n                return this;\n            };\n        }\n    }\n}\nexport class HashContainer extends Container {\n    /**\n     * @internal\n     */\n    constructor() {\n        super();\n        /**\n         * @internal\n         */\n        this._objMap = [];\n        /**\n         * @internal\n         */\n        this._originMap = {};\n        /**\n         * @description Unique symbol used to tag object.\n         */\n        this.HASH_TAG = Symbol('@@HASH_TAG');\n        Object.setPrototypeOf(this._originMap, null);\n        this._header = {};\n        this._header._pre = this._header._next = this._head = this._tail = this._header;\n    }\n    /**\n     * @internal\n     */\n    _eraseNode(node) {\n        const { _pre, _next } = node;\n        _pre._next = _next;\n        _next._pre = _pre;\n        if (node === this._head) {\n            this._head = _next;\n        }\n        if (node === this._tail) {\n            this._tail = _pre;\n        }\n        this._length -= 1;\n    }\n    /**\n     * @internal\n     */\n    _set(key, value, isObject) {\n        if (isObject === undefined)\n            isObject = checkObject(key);\n        let newTail;\n        if (isObject) {\n            const index = key[this.HASH_TAG];\n            if (index !== undefined) {\n                this._objMap[index]._value = value;\n                return this._length;\n            }\n            Object.defineProperty(key, this.HASH_TAG, {\n                value: this._objMap.length,\n                configurable: true\n            });\n            newTail = {\n                _key: key,\n                _value: value,\n                _pre: this._tail,\n                _next: this._header\n            };\n            this._objMap.push(newTail);\n        }\n        else {\n            const node = this._originMap[key];\n            if (node) {\n                node._value = value;\n                return this._length;\n            }\n            newTail = {\n                _key: key,\n                _value: value,\n                _pre: this._tail,\n                _next: this._header\n            };\n            this._originMap[key] = newTail;\n        }\n        if (this._length === 0) {\n            this._head = newTail;\n            this._header._next = newTail;\n        }\n        else {\n            this._tail._next = newTail;\n        }\n        this._tail = newTail;\n        this._header._pre = newTail;\n        return ++this._length;\n    }\n    /**\n     * @internal\n     */\n    _findElementNode(key, isObject) {\n        if (isObject === undefined)\n            isObject = checkObject(key);\n        if (isObject) {\n            const index = key[this.HASH_TAG];\n            if (index === undefined)\n                return this._header;\n            return this._objMap[index];\n        }\n        else {\n            return this._originMap[key] || this._header;\n        }\n    }\n    clear() {\n        const HASH_TAG = this.HASH_TAG;\n        this._objMap.forEach(function (el) {\n            delete el._key[HASH_TAG];\n        });\n        this._objMap = [];\n        this._originMap = {};\n        Object.setPrototypeOf(this._originMap, null);\n        this._length = 0;\n        this._head = this._tail = this._header._pre = this._header._next = this._header;\n    }\n    /**\n     * @description Remove the element of the specified key.\n     * @param key - The key you want to remove.\n     * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n     *                   If a `undefined` value is passed in, the type will be automatically judged.\n     * @returns Whether erase successfully.\n     */\n    eraseElementByKey(key, isObject) {\n        let node;\n        if (isObject === undefined)\n            isObject = checkObject(key);\n        if (isObject) {\n            const index = key[this.HASH_TAG];\n            if (index === undefined)\n                return false;\n            delete key[this.HASH_TAG];\n            node = this._objMap[index];\n            delete this._objMap[index];\n        }\n        else {\n            node = this._originMap[key];\n            if (node === undefined)\n                return false;\n            delete this._originMap[key];\n        }\n        this._eraseNode(node);\n        return true;\n    }\n    eraseElementByIterator(iter) {\n        const node = iter._node;\n        if (node === this._header) {\n            throwIteratorAccessError();\n        }\n        this._eraseNode(node);\n        return iter.next();\n    }\n    eraseElementByPos(pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        let node = this._head;\n        while (pos--) {\n            node = node._next;\n        }\n        this._eraseNode(node);\n        return this._length;\n    }\n}\n", "import { Container, ContainerIterator, IteratorType } from '@/container/ContainerBase';\nimport checkObject from '@/utils/checkObject';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nexport type HashLinkNode<K, V> = {\n  _key: K,\n  _value: V,\n  _pre: HashLinkNode<K, V>,\n  _next: HashLinkNode<K, V>\n}\n\nexport abstract class HashContainerIterator<K, V> extends ContainerIterator<K | [K, V]> {\n  abstract readonly container: HashContainer<K, V>;\n  /**\n   * @internal\n   */\n  _node: HashLinkNode<K, V>;\n  /**\n   * @internal\n   */\n  protected readonly _header: HashLinkNode<K, V>;\n  /**\n   * @internal\n   */\n  protected constructor(\n    node: HashLinkNode<K, V>,\n    header: HashLinkNode<K, V>,\n    iteratorType?: IteratorType\n  ) {\n    super(iteratorType);\n    this._node = node;\n    this._header = header;\n    if (this.iteratorType === IteratorType.NORMAL) {\n      this.pre = function () {\n        if (this._node._pre === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre;\n        return this;\n      };\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next;\n        return this;\n      };\n    } else {\n      this.pre = function () {\n        if (this._node._next === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next;\n        return this;\n      };\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre;\n        return this;\n      };\n    }\n  }\n  // @ts-ignore\n  pre(): this;\n  // @ts-ignore\n  next(): this;\n}\n\nexport abstract class HashContainer<K, V> extends Container<K | [K, V]> {\n  /**\n   * @internal\n   */\n  protected _objMap: HashLinkNode<K, V>[] = [];\n  /**\n   * @internal\n   */\n  protected _originMap: Record<string, HashLinkNode<K, V>> = {};\n  /**\n   * @internal\n   */\n  protected _head: HashLinkNode<K, V>;\n  /**\n   * @internal\n   */\n  protected _tail: HashLinkNode<K, V>;\n  /**\n   * @internal\n   */\n  protected readonly _header: HashLinkNode<K, V>;\n  /**\n   * @description Unique symbol used to tag object.\n   */\n  readonly HASH_TAG = Symbol('@@HASH_TAG');\n  /**\n   * @internal\n   */\n  protected constructor() {\n    super();\n    Object.setPrototypeOf(this._originMap, null);\n    this._header = <HashLinkNode<K, V>>{};\n    this._header._pre = this._header._next = this._head = this._tail = this._header;\n  }\n  /**\n   * @internal\n   */\n  protected _eraseNode(node: HashLinkNode<K, V>) {\n    const { _pre, _next } = node;\n    _pre._next = _next;\n    _next._pre = _pre;\n    if (node === this._head) {\n      this._head = _next;\n    }\n    if (node === this._tail) {\n      this._tail = _pre;\n    }\n    this._length -= 1;\n  }\n  /**\n   * @internal\n   */\n  protected _set(key: K, value?: V, isObject?: boolean) {\n    if (isObject === undefined) isObject = checkObject(key);\n    let newTail;\n    if (isObject) {\n      const index = (<Record<symbol, number>><unknown>key)[this.HASH_TAG];\n      if (index !== undefined) {\n        this._objMap[<number>index]._value = <V>value;\n        return this._length;\n      }\n      Object.defineProperty(key, this.HASH_TAG, {\n        value: this._objMap.length,\n        configurable: true\n      });\n      newTail = {\n        _key: key,\n        _value: <V>value,\n        _pre: this._tail,\n        _next: this._header\n      };\n      this._objMap.push(newTail);\n    } else {\n      const node = this._originMap[<string><unknown>key];\n      if (node) {\n        node._value = <V>value;\n        return this._length;\n      }\n      newTail = {\n        _key: key,\n        _value: <V>value,\n        _pre: this._tail,\n        _next: this._header\n      };\n      this._originMap[<string><unknown>key] = newTail;\n    }\n    if (this._length === 0) {\n      this._head = newTail;\n      this._header._next = newTail;\n    } else {\n      this._tail._next = newTail;\n    }\n    this._tail = newTail;\n    this._header._pre = newTail;\n    return ++this._length;\n  }\n  /**\n   * @internal\n   */\n  protected _findElementNode(key: K, isObject?: boolean) {\n    if (isObject === undefined) isObject = checkObject(key);\n    if (isObject) {\n      const index = (<Record<symbol, number>><unknown>key)[this.HASH_TAG];\n      if (index === undefined) return this._header;\n      return this._objMap[index];\n    } else {\n      return this._originMap[<string><unknown>key] || this._header;\n    }\n  }\n  clear() {\n    const HASH_TAG = this.HASH_TAG;\n    this._objMap.forEach(function (el) {\n      delete (<Record<symbol, number>><unknown>el._key)[HASH_TAG];\n    });\n    this._objMap = [];\n    this._originMap = {};\n    Object.setPrototypeOf(this._originMap, null);\n    this._length = 0;\n    this._head = this._tail = this._header._pre = this._header._next = this._header;\n  }\n  /**\n   * @description Remove the element of the specified key.\n   * @param key - The key you want to remove.\n   * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n   *                   If a `undefined` value is passed in, the type will be automatically judged.\n   * @returns Whether erase successfully.\n   */\n  eraseElementByKey(key: K, isObject?: boolean) {\n    let node;\n    if (isObject === undefined) isObject = checkObject(key);\n    if (isObject) {\n      const index = (<Record<symbol, number>><unknown>key)[this.HASH_TAG];\n      if (index === undefined) return false;\n      delete (<Record<symbol, number>><unknown>key)[this.HASH_TAG];\n      node = this._objMap[index];\n      delete this._objMap[index];\n    } else {\n      node = this._originMap[<string><unknown>key];\n      if (node === undefined) return false;\n      delete this._originMap[<string><unknown>key];\n    }\n    this._eraseNode(node);\n    return true;\n  }\n  eraseElementByIterator(iter: HashContainerIterator<K, V>) {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    this._eraseNode(node);\n    return iter.next();\n  }\n  eraseElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let node = this._head;\n    while (pos--) {\n      node = node._next;\n    }\n    this._eraseNode(node);\n    return this._length;\n  }\n}\n"]}