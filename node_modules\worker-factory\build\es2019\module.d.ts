import { isSupportingTransferables } from './helpers/is-supporting-transferables';
import { IR<PERSON>eiver, IWorkerDefinition } from './interfaces';
import { TDestroyWorkerFunction, TWorkerImplementation } from './types';
export * from './interfaces/index';
export * from './types/index';
export declare const createWorker: <WorkerDefinition extends IWorkerDefinition>(receiver: IReceiver, workerImplementation: TWorkerImplementation<WorkerDefinition>, isSupportedFunction?: () => boolean | Promise<boolean>) => TDestroyWorkerFunction;
export { isSupportingTransferables as isSupported };
//# sourceMappingURL=module.d.ts.map