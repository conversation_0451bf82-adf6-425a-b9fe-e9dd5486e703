{"author": "<PERSON>", "bugs": {"url": "https://github.com/chrisguttandin/worker-factory/issues"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "dependencies": {"@babel/runtime": "^7.28.4", "fast-unique-numbers": "^9.0.24", "tslib": "^2.8.1"}, "description": "A little factory function to create a JSON-RPC based Web Worker implementation.", "devDependencies": {"@babel/core": "^7.28.4", "@babel/plugin-external-helpers": "^7.27.1", "@babel/plugin-transform-runtime": "^7.28.3", "@babel/preset-env": "^7.28.3", "@commitlint/cli": "^19.8.1", "@commitlint/config-angular": "^19.8.1", "@rollup/plugin-babel": "^6.0.4", "chai": "^4.3.10", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-holy-grail": "^61.0.1", "husky": "^9.1.7", "karma": "^6.4.4", "karma-browserstack-launcher": "^1.6.0", "karma-chrome-launcher": "^3.2.0", "karma-cli": "^2.0.0", "karma-firefox-launcher": "^2.1.3", "karma-mocha": "^2.0.1", "karma-mocha-webworker": "^1.3.0", "karma-sinon-chai": "^2.0.2", "karma-webkit-launcher": "^2.6.0", "karma-webpack": "^5.0.1", "lint-staged": "^16.1.6", "memory-fs": "^0.5.0", "mocha": "^11.7.2", "prettier": "^3.6.2", "rimraf": "^6.0.1", "rollup": "^4.52.0", "sinon": "^17.0.2", "sinon-chai": "^3.7.0", "ts-loader": "^9.5.4", "tsconfig-holy-grail": "^15.0.2", "tslint": "^6.1.3", "tslint-config-holy-grail": "^56.0.6", "typescript": "^5.9.2", "webpack": "^5.101.3"}, "files": ["build/es2019/", "build/es5/", "src/"], "homepage": "https://github.com/chrisguttandin/worker-factory", "license": "MIT", "main": "build/es5/bundle.js", "module": "build/es2019/module.js", "name": "worker-factory", "repository": {"type": "git", "url": "https://github.com/chrisguttandin/worker-factory.git"}, "scripts": {"build": "rimraf build/* && tsc --project src/tsconfig.json && rollup --config config/rollup/bundle.mjs", "lint": "npm run lint:config && npm run lint:src && npm run lint:test", "lint:config": "eslint --config config/eslint/config.json --ext .cjs --ext .js --ext .mjs --report-unused-disable-directives config/", "lint:src": "tslint --config config/tslint/src.json --project src/tsconfig.json src/*.ts src/**/*.ts", "lint:test": "eslint --config config/eslint/test.json --ext .js --report-unused-disable-directives test/", "prepare": "husky", "prepublishOnly": "npm run build", "test": "npm run lint && npm run build && npm run test:integration && npm run test:unit", "test:integration": "if [ \"$TYPE\" = \"\" -o \"$TYPE\" = \"integration\" ]; then karma start config/karma/config-integration.js --single-run; fi", "test:unit": "if [ \"$TYPE\" = \"\" -o \"$TYPE\" = \"unit\" ]; then karma start config/karma/config-unit.js --single-run; fi"}, "types": "build/es2019/module.d.ts", "version": "7.0.46"}