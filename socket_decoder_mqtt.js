const net = require('net');
const mqtt = require('mqtt');
const EventEmitter = require('events');

class SocketDecoderMQTT extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // Socket 配置
        this.host = options.host || '*************';
        this.port = options.port || 1333;
        this.sampleRate = options.sampleRate || 10000;
        
        // MQTT 配置
        this.mqttBroker = options.mqttBroker || 'mqtt://127.0.0.1:1883';
        this.mqttTopic = options.mqttTopic || 'testtopic/sensor/data';
        
        // 計算參數
        this.samplePoint1 = Math.ceil(this.sampleRate / 12.5);
        this.sampleN = Math.ceil(this.samplePoint1 * 1.005);
        this.sampleByte = parseInt(this.sampleN * 16);
        
        // 狀態控制
        this.running = false;
        this.paused = false;
        this.socket = null;
        this.mqttClient = null;
        
        // 數據緩存
        this.tempData = '';
        this.dataQueue = [];
        
        // Tare 值
        this.tareBX = 0;
        this.tareBY = 0;
        this.tareBZ = 0;
        this.tareBT = 0;
        
        // 校準參數
        this.parmNX = 1.0;
        this.parmNY = 1.0;
        this.parmNZ = 1.0;
        this.parmNT = 1.0;
        
        // MQTT 發送定時器
        this.mqttTimer = null;
        this.lastDecodedData = null;
    }

    async start() {
        try {
            await this.connectMQTT();
            await this.connectSocket();
            this.startMQTTTimer();
            this.running = true;
            console.log('SocketDecoderMQTT started successfully');
        } catch (error) {
            console.error('Failed to start:', error);
            throw error;
        }
    }

    async connectMQTT() {
        return new Promise((resolve, reject) => {
            this.mqttClient = mqtt.connect(this.mqttBroker);
            
            this.mqttClient.on('connect', () => {
                console.log('MQTT connected');
                resolve();
            });
            
            this.mqttClient.on('error', (error) => {
                console.error('MQTT error:', error);
                reject(error);
            });
        });
    }

    async connectSocket() {
        return new Promise((resolve, reject) => {
            this.socket = new net.Socket();
            
            this.socket.connect(this.port, this.host, () => {
                console.log(`Socket connected to ${this.host}:${this.port}`);
                this.setupSocketHandlers();
                resolve();
            });
            
            this.socket.on('error', (error) => {
                console.error('Socket error:', error);
                reject(error);
            });
        });
    }

    setupSocketHandlers() {
        this.socket.on('data', (data) => {
            if (this.paused) {
                this.tempData = '';
                return;
            }
            
            const hexData = data.toString('hex');
            this.processRawData(hexData);
        });

        this.socket.on('close', () => {
            console.log('Socket connection closed');
            this.emit('disconnected');
        });

        this.socket.on('error', (error) => {
            console.error('Socket error:', error);
            this.emit('error', error);
        });
    }

    processRawData(hexData) {
        try {
            const fullData = this.tempData + hexData;
            const chunks = this.splitIntoChunks(fullData, 32);
            
            if (chunks.remainder) {
                this.tempData = chunks.remainder;
            } else {
                this.tempData = '';
            }
            
            if (chunks.complete.length > 0) {
                const decodedData = this.decodeData(chunks.complete.join(''));
                this.lastDecodedData = decodedData;
                this.emit('dataDecoded', decodedData);
            }
        } catch (error) {
            console.error('Error processing raw data:', error);
        }
    }

    splitIntoChunks(hexString, chunkSize) {
        const chunks = [];
        let i = 0;
        
        while (i + chunkSize <= hexString.length) {
            chunks.push(hexString.substr(i, chunkSize));
            i += chunkSize;
        }
        
        const remainder = i < hexString.length ? hexString.substr(i) : '';
        
        return {
            complete: chunks,
            remainder: remainder
        };
    }

    decodeData(hexData) {
        
        const bendingX = [];
        const bendingY = [];
        const bendingXY = [];
        const tension = [];
        const torsion = [];
        
        // 解析十六進制數據
        for (let i = 0; i < this.samplePoint1; i++) {
            const offset = i * 32;
            
            // 提取各通道數據 (假設每個數據點4字節)
            const dataX = this.hexToFloat(hexData.substr(offset, 8));
            const dataY = this.hexToFloat(hexData.substr(offset + 8, 8));
            const dataZ = this.hexToFloat(hexData.substr(offset + 16, 8));
            const dataT = this.hexToFloat(hexData.substr(offset + 24, 8));
            
            // 應用 Tare 和校準
            const txtBx = (dataX - this.tareBX) * this.parmNX;
            const txtBy = (dataY - this.tareBY) * this.parmNY;
            const txtTen = (dataZ - this.tareBZ) * this.parmNZ;
            const txtTor = (dataT - this.tareBT) * this.parmNT;
            
            bendingX.push(parseFloat(txtBx.toFixed(3)));
            bendingY.push(parseFloat(txtBy.toFixed(3)));
            bendingXY.push(parseFloat(Math.sqrt(txtBx * txtBx + txtBy * txtBy).toFixed(3)));
            tension.push(parseFloat(txtTen.toFixed(3)));
            torsion.push(parseFloat(txtTor.toFixed(3)));
        }
        
        return {
            timestamp: Date.now(),
            bendingX: bendingX,
            bendingY: bendingY,
            bendingXY: bendingXY,
            tension: tension,
            torsion: torsion,
            avgBendingXY: this.calculateAverage(bendingXY),
            avgTension: this.calculateAverage(tension),
            avgTorsion: this.calculateAverage(torsion)
        };
    }

    hexToFloat(hexString) {
        // 將十六進制轉換為浮點數 (根據原始 Python 代碼邏輯)
        const intValue = parseInt(hexString, 16);
        return intValue / 6553.5; // _HEX16_TO_FLOAT_DIVISOR
    }

    calculateAverage(array) {
        if (array.length === 0) return 0;
        const sum = array.reduce((a, b) => a + b, 0);
        return parseFloat((sum / array.length).toFixed(3));
    }

    startMQTTTimer() {
        this.mqttTimer = setInterval(() => {
            if (this.lastDecodedData && this.mqttClient && this.mqttClient.connected) {
                this.publishToMQTT(this.lastDecodedData);
            }
        }, 5000); // 每秒發送一次
    }

    publishToMQTT(data) {
        try {
            const payload = JSON.stringify(data);
            this.mqttClient.publish(this.mqttTopic, payload, (error) => {
                if (error) {
                    console.error('MQTT publish error:', error);
                } else {
                    console.log('Data published to MQTT:', data.timestamp);
                }
            });
        } catch (error) {
            console.error('Error publishing to MQTT:', error);
        }
    }

    pause() {
        this.paused = true;
        this.tempData = '';
        console.log('Data processing paused');
    }

    resume() {
        this.paused = false;
        console.log('Data processing resumed');
    }

    stop() {
        this.running = false;
        
        if (this.mqttTimer) {
            clearInterval(this.mqttTimer);
            this.mqttTimer = null;
        }
        
        if (this.socket) {
            this.socket.destroy();
            this.socket = null;
        }
        
        if (this.mqttClient) {
            this.mqttClient.end();
            this.mqttClient = null;
        }
        
        console.log('SocketDecoderMQTT stopped');
    }

    setTareValues(tareBX, tareBY, tareBZ, tareBT) {
        this.tareBX = tareBX || 0;
        this.tareBY = tareBY || 0;
        this.tareBZ = tareBZ || 0;
        this.tareBT = tareBT || 0;
        console.log('Tare values updated');
    }

    setCalibrationParams(parmNX, parmNY, parmNZ, parmNT) {
        this.parmNX = parmNX || 1.0;
        this.parmNY = parmNY || 1.0;
        this.parmNZ = parmNZ || 1.0;
        this.parmNT = parmNT || 1.0;
        console.log('Calibration parameters updated');
    }
}

// 使用範例
async function main() {
    const socketDecoder = new SocketDecoderMQTT({
        host: '*************',
        port: 1333,
        mqttBroker: 'mqtt://localhost:1883',
        mqttTopic: 'sensor/toolholder'
    });

    socketDecoder.on('dataDecoded', (data) => {
        console.log('Decoded data:', {
            timestamp: data.timestamp,
            avgBendingXY: data.avgBendingXY,
            avgTension: data.avgTension,
            avgTorsion: data.avgTorsion
        });
    });

    socketDecoder.on('disconnected', () => {
        console.log('Connection lost, attempting to reconnect...');
        setTimeout(() => {
            socketDecoder.start().catch(console.error);
        }, 5000);
    });

    try {
        await socketDecoder.start();
    } catch (error) {
        console.error('Failed to start:', error);
    }
}

// 如果直接執行此文件
if (require.main === module) {
    main().catch(console.error);
}

module.exports = SocketDecoderMQTT;