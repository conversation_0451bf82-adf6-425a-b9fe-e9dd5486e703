{"version": 3, "sources": ["container/HashContainer/Base/index.js", "../../src/container/HashContainer/Base/index.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "Container", "ContainerIterator", "checkObject", "throwIteratorAccessError", "HashContainerIterator", "_super", "node", "header", "iteratorType", "_this", "_node", "_header", "pre", "_pre", "next", "_next", "HashC<PERSON>r", "_objMap", "_originMap", "HASH_TAG", "Symbol", "_head", "_tail", "_eraseNode", "_length", "_set", "key", "value", "isObject", "undefined", "newTail", "index", "_value", "defineProperty", "length", "configurable", "_key", "push", "_findElementNode", "clear", "for<PERSON>ach", "el", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eraseElementByIterator", "iter", "eraseElementByPos", "pos", "RangeError"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;SCApCG,WAAWC,yBAAiC;;OAC9CC,iBAAW;;SAETC,gCAA0B;;AASnC,IAAAC,wBAAA,SAAAC;IAA0DvB,UAAAsB,uBAAAC;IAaxD,SAAAD,sBACEE,GACAC,GACAC;QAHF,IAAAC,IAKEJ,EAAAX,KAAAX,MAAMyB,MAAazB;QACnB0B,EAAKC,IAAQJ;QACbG,EAAKE,IAAUJ;QACf,IAAIE,EAAKD,iBAAY,GAA0B;YAC7CC,EAAKG,MAAM;gBACT,IAAI7B,KAAK2B,EAAMG,MAAS9B,KAAK4B,GAAS;oBACpCR;ADJM;gBCMRpB,KAAK2B,IAAQ3B,KAAK2B,EAAMG;gBACxB,OAAO9B;ADJH;YCMN0B,EAAKK,OAAO;gBACV,IAAI/B,KAAK2B,MAAU3B,KAAK4B,GAAS;oBAC/BR;ADJM;gBCMRpB,KAAK2B,IAAQ3B,KAAK2B,EAAMK;gBACxB,OAAOhC;ADJH;AACJ,eCKG;YACL0B,EAAKG,MAAM;gBACT,IAAI7B,KAAK2B,EAAMK,MAAUhC,KAAK4B,GAAS;oBACrCR;ADHM;gBCKRpB,KAAK2B,IAAQ3B,KAAK2B,EAAMK;gBACxB,OAAOhC;ADHH;YCKN0B,EAAKK,OAAO;gBACV,IAAI/B,KAAK2B,MAAU3B,KAAK4B,GAAS;oBAC/BR;ADHM;gBCKRpB,KAAK2B,IAAQ3B,KAAK2B,EAAMG;gBACxB,OAAO9B;ADHH;AACJ;QACA,OAAO0B;AACX;ICQJ,OAAAL;AAAA,CAzDA,CAA0DH;;SDoDjDG;;ACOT,IAAAY,gBAAA,SAAAX;IAAkDvB,UAAAkC,eAAAX;IA4BhD,SAAAW;QAAA,IAAAP,IACEJ,EAAAX,KAAAX,SAAOA;QAzBC0B,EAAAQ,IAAgC;QAIhCR,EAAAS,IAAiD,CAAA;QAgBlDT,EAAAU,WAAWC,OAAO;QAMzBjC,OAAOC,eAAeqB,EAAKS,GAAY;QACvCT,EAAKE,IAA8B,CAAA;QACnCF,EAAKE,EAAQE,IAAOJ,EAAKE,EAAQI,IAAQN,EAAKY,IAAQZ,EAAKa,IAAQb,EAAKE;QDhBpE,OAAOF;AACX;ICoBQO,cAAAxB,UAAA+B,IAAV,SAAqBjB;QACX,IAAAO,IAAgBP,EAAIO,GAAdE,IAAUT,EAAIS;QAC5BF,EAAKE,IAAQA;QACbA,EAAMF,IAAOA;QACb,IAAIP,MAASvB,KAAKsC,GAAO;YACvBtC,KAAKsC,IAAQN;ADfX;QCiBJ,IAAIT,MAASvB,KAAKuC,GAAO;YACvBvC,KAAKuC,IAAQT;ADfX;QCiBJ9B,KAAKyC,KAAW;ADfhB;ICoBQR,cAAAxB,UAAAiC,IAAV,SAAeC,GAAQC,GAAWC;QAChC,IAAIA,MAAaC,WAAWD,IAAW1B,YAAYwB;QACnD,IAAII;QACJ,IAAIF,GAAU;YACZ,IAAMG,IAA0CL,EAAK3C,KAAKoC;YAC1D,IAAIY,MAAUF,WAAW;gBACvB9C,KAAKkC,EAAgBc,GAAOC,IAAYL;gBACxC,OAAO5C,KAAKyC;ADdR;YCgBNrC,OAAO8C,eAAeP,GAAK3C,KAAKoC,UAAU;gBACxCQ,OAAO5C,KAAKkC,EAAQiB;gBACpBC,cAAc;;YAEhBL,IAAU;gBACRM,GAAMV;gBACNM,GAAWL;gBACXd,GAAM9B,KAAKuC;gBACXP,GAAOhC,KAAK4B;;YAEd5B,KAAKkC,EAAQoB,KAAKP;ADdhB,eCeG;YACL,IAAMxB,IAAOvB,KAAKmC,EAA4BQ;YAC9C,IAAIpB,GAAM;gBACRA,EAAK0B,IAAYL;gBACjB,OAAO5C,KAAKyC;ADbR;YCeNM,IAAU;gBACRM,GAAMV;gBACNM,GAAWL;gBACXd,GAAM9B,KAAKuC;gBACXP,GAAOhC,KAAK4B;;YAEd5B,KAAKmC,EAA4BQ,KAAOI;ADbtC;QCeJ,IAAI/C,KAAKyC,MAAY,GAAG;YACtBzC,KAAKsC,IAAQS;YACb/C,KAAK4B,EAAQI,IAAQe;ADbnB,eCcG;YACL/C,KAAKuC,EAAMP,IAAQe;ADZjB;QCcJ/C,KAAKuC,IAAQQ;QACb/C,KAAK4B,EAAQE,IAAOiB;QACpB,SAAS/C,KAAKyC;ADZd;ICiBQR,cAAAxB,UAAA8C,IAAV,SAA2BZ,GAAQE;QACjC,IAAIA,MAAaC,WAAWD,IAAW1B,YAAYwB;QACnD,IAAIE,GAAU;YACZ,IAAMG,IAA0CL,EAAK3C,KAAKoC;YAC1D,IAAIY,MAAUF,WAAW,OAAO9C,KAAK4B;YACrC,OAAO5B,KAAKkC,EAAQc;ADVlB,eCWG;YACL,OAAOhD,KAAKmC,EAA4BQ,MAAQ3C,KAAK4B;ADTnD;AACJ;ICWFK,cAAAxB,UAAA+C,QAAA;QACE,IAAMpB,IAAWpC,KAAKoC;QACtBpC,KAAKkC,EAAQuB,SAAQ,SAAUC;mBACYA,EAAGL,EAAMjB;ADThD;QCWJpC,KAAKkC,IAAU;QACflC,KAAKmC,IAAa,CAAA;QAClB/B,OAAOC,eAAeL,KAAKmC,GAAY;QACvCnC,KAAKyC,IAAU;QACfzC,KAAKsC,IAAQtC,KAAKuC,IAAQvC,KAAK4B,EAAQE,IAAO9B,KAAK4B,EAAQI,IAAQhC,KAAK4B;ADTxE;ICkBFK,cAAAxB,UAAAkD,oBAAA,SAAkBhB,GAAQE;QACxB,IAAItB;QACJ,IAAIsB,MAAaC,WAAWD,IAAW1B,YAAYwB;QACnD,IAAIE,GAAU;YACZ,IAAMG,IAA0CL,EAAK3C,KAAKoC;YAC1D,IAAIY,MAAUF,WAAW,OAAO;mBACSH,EAAK3C,KAAKoC;YACnDb,IAAOvB,KAAKkC,EAAQc;mBACbhD,KAAKkC,EAAQc;ADPlB,eCQG;YACLzB,IAAOvB,KAAKmC,EAA4BQ;YACxC,IAAIpB,MAASuB,WAAW,OAAO;mBACxB9C,KAAKmC,EAA4BQ;ADLtC;QCOJ3C,KAAKwC,EAAWjB;QAChB,OAAO;ADLP;ICOFU,cAAAxB,UAAAmD,yBAAA,SAAuBC;QACrB,IAAMtC,IAAOsC,EAAKlC;QAClB,IAAIJ,MAASvB,KAAK4B,GAAS;YACzBR;ADLE;QCOJpB,KAAKwC,EAAWjB;QAChB,OAAOsC,EAAK9B;ADLZ;ICOFE,cAAAxB,UAAAqD,oBAAA,SAAkBC;QDLZ,ICMsBA,IAAG,KAAHA,IAAQ/D,KAAKyC,IAAO,GAvN5C;YAAG,MAAS,IAAIuB;ADmNd;QCKJ,IAAIzC,IAAOvB,KAAKsC;QAChB,OAAOyB,KAAO;YACZxC,IAAOA,EAAKS;ADHV;QCKJhC,KAAKwC,EAAWjB;QAChB,OAAOvB,KAAKyC;ADHZ;ICKJ,OAAAR;AAAA,CAjKA,CAAkDhB;;SD+JzCgB", "file": "index.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { Container, ContainerIterator } from \"../../ContainerBase\";\nimport checkObject from \"../../../utils/checkObject\";\nimport $checkWithinAccessParams from \"../../../utils/checkParams.macro\";\nimport { throwIteratorAccessError } from \"../../../utils/throwError\";\nvar HashContainerIterator = /** @class */ (function (_super) {\n    __extends(HashContainerIterator, _super);\n    /**\n     * @internal\n     */\n    function HashContainerIterator(node, header, iteratorType) {\n        var _this = _super.call(this, iteratorType) || this;\n        _this._node = node;\n        _this._header = header;\n        if (_this.iteratorType === 0 /* IteratorType.NORMAL */) {\n            _this.pre = function () {\n                if (this._node._pre === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._pre;\n                return this;\n            };\n            _this.next = function () {\n                if (this._node === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._next;\n                return this;\n            };\n        }\n        else {\n            _this.pre = function () {\n                if (this._node._next === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._next;\n                return this;\n            };\n            _this.next = function () {\n                if (this._node === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._pre;\n                return this;\n            };\n        }\n        return _this;\n    }\n    return HashContainerIterator;\n}(ContainerIterator));\nexport { HashContainerIterator };\nvar HashContainer = /** @class */ (function (_super) {\n    __extends(HashContainer, _super);\n    /**\n     * @internal\n     */\n    function HashContainer() {\n        var _this = _super.call(this) || this;\n        /**\n         * @internal\n         */\n        _this._objMap = [];\n        /**\n         * @internal\n         */\n        _this._originMap = {};\n        /**\n         * @description Unique symbol used to tag object.\n         */\n        _this.HASH_TAG = Symbol('@@HASH_TAG');\n        Object.setPrototypeOf(_this._originMap, null);\n        _this._header = {};\n        _this._header._pre = _this._header._next = _this._head = _this._tail = _this._header;\n        return _this;\n    }\n    /**\n     * @internal\n     */\n    HashContainer.prototype._eraseNode = function (node) {\n        var _pre = node._pre, _next = node._next;\n        _pre._next = _next;\n        _next._pre = _pre;\n        if (node === this._head) {\n            this._head = _next;\n        }\n        if (node === this._tail) {\n            this._tail = _pre;\n        }\n        this._length -= 1;\n    };\n    /**\n     * @internal\n     */\n    HashContainer.prototype._set = function (key, value, isObject) {\n        if (isObject === undefined)\n            isObject = checkObject(key);\n        var newTail;\n        if (isObject) {\n            var index = key[this.HASH_TAG];\n            if (index !== undefined) {\n                this._objMap[index]._value = value;\n                return this._length;\n            }\n            Object.defineProperty(key, this.HASH_TAG, {\n                value: this._objMap.length,\n                configurable: true\n            });\n            newTail = {\n                _key: key,\n                _value: value,\n                _pre: this._tail,\n                _next: this._header\n            };\n            this._objMap.push(newTail);\n        }\n        else {\n            var node = this._originMap[key];\n            if (node) {\n                node._value = value;\n                return this._length;\n            }\n            newTail = {\n                _key: key,\n                _value: value,\n                _pre: this._tail,\n                _next: this._header\n            };\n            this._originMap[key] = newTail;\n        }\n        if (this._length === 0) {\n            this._head = newTail;\n            this._header._next = newTail;\n        }\n        else {\n            this._tail._next = newTail;\n        }\n        this._tail = newTail;\n        this._header._pre = newTail;\n        return ++this._length;\n    };\n    /**\n     * @internal\n     */\n    HashContainer.prototype._findElementNode = function (key, isObject) {\n        if (isObject === undefined)\n            isObject = checkObject(key);\n        if (isObject) {\n            var index = key[this.HASH_TAG];\n            if (index === undefined)\n                return this._header;\n            return this._objMap[index];\n        }\n        else {\n            return this._originMap[key] || this._header;\n        }\n    };\n    HashContainer.prototype.clear = function () {\n        var HASH_TAG = this.HASH_TAG;\n        this._objMap.forEach(function (el) {\n            delete el._key[HASH_TAG];\n        });\n        this._objMap = [];\n        this._originMap = {};\n        Object.setPrototypeOf(this._originMap, null);\n        this._length = 0;\n        this._head = this._tail = this._header._pre = this._header._next = this._header;\n    };\n    /**\n     * @description Remove the element of the specified key.\n     * @param key - The key you want to remove.\n     * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n     *                   If a `undefined` value is passed in, the type will be automatically judged.\n     * @returns Whether erase successfully.\n     */\n    HashContainer.prototype.eraseElementByKey = function (key, isObject) {\n        var node;\n        if (isObject === undefined)\n            isObject = checkObject(key);\n        if (isObject) {\n            var index = key[this.HASH_TAG];\n            if (index === undefined)\n                return false;\n            delete key[this.HASH_TAG];\n            node = this._objMap[index];\n            delete this._objMap[index];\n        }\n        else {\n            node = this._originMap[key];\n            if (node === undefined)\n                return false;\n            delete this._originMap[key];\n        }\n        this._eraseNode(node);\n        return true;\n    };\n    HashContainer.prototype.eraseElementByIterator = function (iter) {\n        var node = iter._node;\n        if (node === this._header) {\n            throwIteratorAccessError();\n        }\n        this._eraseNode(node);\n        return iter.next();\n    };\n    HashContainer.prototype.eraseElementByPos = function (pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        var node = this._head;\n        while (pos--) {\n            node = node._next;\n        }\n        this._eraseNode(node);\n        return this._length;\n    };\n    return HashContainer;\n}(Container));\nexport { HashContainer };\n", "import { Container, ContainerIterator, IteratorType } from '@/container/ContainerBase';\nimport checkObject from '@/utils/checkObject';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nexport type HashLinkNode<K, V> = {\n  _key: K,\n  _value: V,\n  _pre: HashLinkNode<K, V>,\n  _next: HashLinkNode<K, V>\n}\n\nexport abstract class HashContainerIterator<K, V> extends ContainerIterator<K | [K, V]> {\n  abstract readonly container: HashContainer<K, V>;\n  /**\n   * @internal\n   */\n  _node: HashLinkNode<K, V>;\n  /**\n   * @internal\n   */\n  protected readonly _header: HashLinkNode<K, V>;\n  /**\n   * @internal\n   */\n  protected constructor(\n    node: HashLinkNode<K, V>,\n    header: HashLinkNode<K, V>,\n    iteratorType?: IteratorType\n  ) {\n    super(iteratorType);\n    this._node = node;\n    this._header = header;\n    if (this.iteratorType === IteratorType.NORMAL) {\n      this.pre = function () {\n        if (this._node._pre === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre;\n        return this;\n      };\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next;\n        return this;\n      };\n    } else {\n      this.pre = function () {\n        if (this._node._next === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next;\n        return this;\n      };\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre;\n        return this;\n      };\n    }\n  }\n  // @ts-ignore\n  pre(): this;\n  // @ts-ignore\n  next(): this;\n}\n\nexport abstract class HashContainer<K, V> extends Container<K | [K, V]> {\n  /**\n   * @internal\n   */\n  protected _objMap: HashLinkNode<K, V>[] = [];\n  /**\n   * @internal\n   */\n  protected _originMap: Record<string, HashLinkNode<K, V>> = {};\n  /**\n   * @internal\n   */\n  protected _head: HashLinkNode<K, V>;\n  /**\n   * @internal\n   */\n  protected _tail: HashLinkNode<K, V>;\n  /**\n   * @internal\n   */\n  protected readonly _header: HashLinkNode<K, V>;\n  /**\n   * @description Unique symbol used to tag object.\n   */\n  readonly HASH_TAG = Symbol('@@HASH_TAG');\n  /**\n   * @internal\n   */\n  protected constructor() {\n    super();\n    Object.setPrototypeOf(this._originMap, null);\n    this._header = <HashLinkNode<K, V>>{};\n    this._header._pre = this._header._next = this._head = this._tail = this._header;\n  }\n  /**\n   * @internal\n   */\n  protected _eraseNode(node: HashLinkNode<K, V>) {\n    const { _pre, _next } = node;\n    _pre._next = _next;\n    _next._pre = _pre;\n    if (node === this._head) {\n      this._head = _next;\n    }\n    if (node === this._tail) {\n      this._tail = _pre;\n    }\n    this._length -= 1;\n  }\n  /**\n   * @internal\n   */\n  protected _set(key: K, value?: V, isObject?: boolean) {\n    if (isObject === undefined) isObject = checkObject(key);\n    let newTail;\n    if (isObject) {\n      const index = (<Record<symbol, number>><unknown>key)[this.HASH_TAG];\n      if (index !== undefined) {\n        this._objMap[<number>index]._value = <V>value;\n        return this._length;\n      }\n      Object.defineProperty(key, this.HASH_TAG, {\n        value: this._objMap.length,\n        configurable: true\n      });\n      newTail = {\n        _key: key,\n        _value: <V>value,\n        _pre: this._tail,\n        _next: this._header\n      };\n      this._objMap.push(newTail);\n    } else {\n      const node = this._originMap[<string><unknown>key];\n      if (node) {\n        node._value = <V>value;\n        return this._length;\n      }\n      newTail = {\n        _key: key,\n        _value: <V>value,\n        _pre: this._tail,\n        _next: this._header\n      };\n      this._originMap[<string><unknown>key] = newTail;\n    }\n    if (this._length === 0) {\n      this._head = newTail;\n      this._header._next = newTail;\n    } else {\n      this._tail._next = newTail;\n    }\n    this._tail = newTail;\n    this._header._pre = newTail;\n    return ++this._length;\n  }\n  /**\n   * @internal\n   */\n  protected _findElementNode(key: K, isObject?: boolean) {\n    if (isObject === undefined) isObject = checkObject(key);\n    if (isObject) {\n      const index = (<Record<symbol, number>><unknown>key)[this.HASH_TAG];\n      if (index === undefined) return this._header;\n      return this._objMap[index];\n    } else {\n      return this._originMap[<string><unknown>key] || this._header;\n    }\n  }\n  clear() {\n    const HASH_TAG = this.HASH_TAG;\n    this._objMap.forEach(function (el) {\n      delete (<Record<symbol, number>><unknown>el._key)[HASH_TAG];\n    });\n    this._objMap = [];\n    this._originMap = {};\n    Object.setPrototypeOf(this._originMap, null);\n    this._length = 0;\n    this._head = this._tail = this._header._pre = this._header._next = this._header;\n  }\n  /**\n   * @description Remove the element of the specified key.\n   * @param key - The key you want to remove.\n   * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n   *                   If a `undefined` value is passed in, the type will be automatically judged.\n   * @returns Whether erase successfully.\n   */\n  eraseElementByKey(key: K, isObject?: boolean) {\n    let node;\n    if (isObject === undefined) isObject = checkObject(key);\n    if (isObject) {\n      const index = (<Record<symbol, number>><unknown>key)[this.HASH_TAG];\n      if (index === undefined) return false;\n      delete (<Record<symbol, number>><unknown>key)[this.HASH_TAG];\n      node = this._objMap[index];\n      delete this._objMap[index];\n    } else {\n      node = this._originMap[<string><unknown>key];\n      if (node === undefined) return false;\n      delete this._originMap[<string><unknown>key];\n    }\n    this._eraseNode(node);\n    return true;\n  }\n  eraseElementByIterator(iter: HashContainerIterator<K, V>) {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    this._eraseNode(node);\n    return iter.next();\n  }\n  eraseElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let node = this._head;\n    while (pos--) {\n      node = node._next;\n    }\n    this._eraseNode(node);\n    return this._length;\n  }\n}\n"]}