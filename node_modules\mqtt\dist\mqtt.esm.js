var as=Object.defineProperty;var Yb=Object.getOwnPropertyDescriptor;var Jb=Object.getOwnPropertyNames;var Xb=Object.prototype.hasOwnProperty;var ze=(t,e)=>()=>(t&&(e=t(t=0)),e);var D=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),Kr=(t,e)=>{for(var r in e)as(t,r,{get:e[r],enumerable:!0})},Zb=(t,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Jb(e))!Xb.call(t,i)&&i!==r&&as(t,i,{get:()=>e[i],enumerable:!(n=Yb(e,i))||n.enumerable});return t};var me=t=>Zb(as({},"__esModule",{value:!0}),t);var A=ze(()=>{});var M={};Kr(M,{_debugEnd:()=>Gc,_debugProcess:()=>Vc,_events:()=>lf,_eventsCount:()=>cf,_exiting:()=>xc,_fatalExceptions:()=>Fc,_getActiveHandles:()=>Rc,_getActiveRequests:()=>Oc,_kill:()=>Cc,_linkedBinding:()=>Ac,_maxListeners:()=>uf,_preload_modules:()=>of,_rawDebug:()=>Sc,_startProfilerIdleNotifier:()=>zc,_stopProfilerIdleNotifier:()=>Kc,_tickCallback:()=>Hc,abort:()=>Xc,addListener:()=>ff,allowedNodeEnvironmentFlags:()=>Uc,arch:()=>uc,argv:()=>fc,argv0:()=>nf,assert:()=>Dc,binding:()=>gc,browser:()=>vc,chdir:()=>mc,config:()=>Tc,cpuUsage:()=>Ri,cwd:()=>wc,debugPort:()=>rf,default:()=>_f,dlopen:()=>Pc,domain:()=>Ic,emit:()=>gf,emitWarning:()=>yc,env:()=>cc,execArgv:()=>hc,execPath:()=>tf,exit:()=>qc,features:()=>jc,hasUncaughtExceptionCaptureCallback:()=>$c,hrtime:()=>Oi,kill:()=>Nc,listeners:()=>mf,memoryUsage:()=>Mc,moduleLoadList:()=>Ec,nextTick:()=>oc,off:()=>df,on:()=>Nt,once:()=>hf,openStdin:()=>Lc,pid:()=>Zc,platform:()=>lc,ppid:()=>ef,prependListener:()=>bf,prependOnceListener:()=>wf,reallyExit:()=>kc,release:()=>_c,removeAllListeners:()=>yf,removeListener:()=>pf,resourceUsage:()=>Bc,setSourceMapsEnabled:()=>sf,setUncaughtExceptionCaptureCallback:()=>Wc,stderr:()=>Yc,stdin:()=>Jc,stdout:()=>Qc,title:()=>ac,umask:()=>bc,uptime:()=>af,version:()=>dc,versions:()=>pc});function cs(t){throw new Error("Node.js process "+t+" is not supported by JSPM core outside of Node.js")}function ew(){!Qr||!wr||(Qr=!1,wr.length?Mt=wr.concat(Mt):Pi=-1,Mt.length&&ic())}function ic(){if(!Qr){var t=setTimeout(ew,0);Qr=!0;for(var e=Mt.length;e;){for(wr=Mt,Mt=[];++Pi<e;)wr&&wr[Pi].run();Pi=-1,e=Mt.length}wr=null,Qr=!1,clearTimeout(t)}}function oc(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];Mt.push(new sc(t,e)),Mt.length===1&&!Qr&&setTimeout(ic,0)}function sc(t,e){this.fun=t,this.array=e}function Ue(){}function Ac(t){cs("_linkedBinding")}function Pc(t){cs("dlopen")}function Oc(){return[]}function Rc(){return[]}function Dc(t,e){if(!t)throw new Error(e||"assertion error")}function $c(){return!1}function af(){return Jt.now()/1e3}function Oi(t){var e=Math.floor((Date.now()-Jt.now())*.001),r=Jt.now()*.001,n=Math.floor(r)+e,i=Math.floor(r%1*1e9);return t&&(n=n-t[0],i=i-t[1],i<0&&(n--,i+=ls)),[n,i]}function Nt(){return _f}function mf(t){return[]}var Mt,Qr,wr,Pi,ac,uc,lc,cc,fc,hc,dc,pc,yc,gc,bc,wc,mc,_c,vc,Sc,Ec,Ic,xc,Tc,kc,Cc,Ri,Bc,Mc,Nc,qc,Lc,Uc,jc,Fc,Wc,Hc,Vc,Gc,zc,Kc,Qc,Yc,Jc,Xc,Zc,ef,tf,rf,nf,of,sf,Jt,us,ls,uf,lf,cf,ff,hf,df,pf,yf,gf,bf,wf,_f,vf=ze(()=>{A();x();I();Mt=[],Qr=!1,Pi=-1;sc.prototype.run=function(){this.fun.apply(null,this.array)};ac="browser",uc="x64",lc="browser",cc={PATH:"/usr/bin",LANG:typeof navigator<"u"?navigator.language+".UTF-8":void 0,PWD:"/",HOME:"/home",TMP:"/tmp"},fc=["/usr/bin/node"],hc=[],dc="v16.8.0",pc={},yc=function(t,e){console.warn((e?e+": ":"")+t)},gc=function(t){cs("binding")},bc=function(t){return 0},wc=function(){return"/"},mc=function(t){},_c={name:"node",sourceUrl:"",headersUrl:"",libUrl:""};vc=!0,Sc=Ue,Ec=[];Ic={},xc=!1,Tc={};kc=Ue,Cc=Ue,Ri=function(){return{}},Bc=Ri,Mc=Ri,Nc=Ue,qc=Ue,Lc=Ue,Uc={};jc={inspector:!1,debug:!1,uv:!1,ipv6:!1,tls_alpn:!1,tls_sni:!1,tls_ocsp:!1,tls:!1,cached_builtins:!0},Fc=Ue,Wc=Ue;Hc=Ue,Vc=Ue,Gc=Ue,zc=Ue,Kc=Ue,Qc=void 0,Yc=void 0,Jc=void 0,Xc=Ue,Zc=2,ef=1,tf="/bin/usr/node",rf=9229,nf="node",of=[],sf=Ue,Jt={now:typeof performance<"u"?performance.now.bind(performance):void 0,timing:typeof performance<"u"?performance.timing:void 0};Jt.now===void 0&&(us=Date.now(),Jt.timing&&Jt.timing.navigationStart&&(us=Jt.timing.navigationStart),Jt.now=()=>Date.now()-us);ls=1e9;Oi.bigint=function(t){var e=Oi(t);return typeof BigInt>"u"?e[0]*ls+e[1]:BigInt(e[0]*ls)+BigInt(e[1])};uf=10,lf={},cf=0;ff=Nt,hf=Nt,df=Nt,pf=Nt,yf=Nt,gf=Ue,bf=Nt,wf=Nt;_f={version:dc,versions:pc,arch:uc,platform:lc,browser:vc,release:_c,_rawDebug:Sc,moduleLoadList:Ec,binding:gc,_linkedBinding:Ac,_events:lf,_eventsCount:cf,_maxListeners:uf,on:Nt,addListener:ff,once:hf,off:df,removeListener:pf,removeAllListeners:yf,emit:gf,prependListener:bf,prependOnceListener:wf,listeners:mf,domain:Ic,_exiting:xc,config:Tc,dlopen:Pc,uptime:af,_getActiveRequests:Oc,_getActiveHandles:Rc,reallyExit:kc,_kill:Cc,cpuUsage:Ri,resourceUsage:Bc,memoryUsage:Mc,kill:Nc,exit:qc,openStdin:Lc,allowedNodeEnvironmentFlags:Uc,assert:Dc,features:jc,_fatalExceptions:Fc,setUncaughtExceptionCaptureCallback:Wc,hasUncaughtExceptionCaptureCallback:$c,emitWarning:yc,nextTick:oc,_tickCallback:Hc,_debugProcess:Vc,_debugEnd:Gc,_startProfilerIdleNotifier:zc,_stopProfilerIdleNotifier:Kc,stdout:Qc,stdin:Jc,stderr:Yc,abort:Xc,umask:bc,chdir:mc,cwd:wc,env:cc,title:ac,argv:fc,execArgv:hc,pid:Zc,ppid:ef,execPath:tf,debugPort:rf,hrtime:Oi,argv0:nf,_preload_modules:of,setSourceMapsEnabled:sf}});var I=ze(()=>{vf()});function tw(){if(Sf)return On;Sf=!0,On.byteLength=a,On.toByteArray=f,On.fromByteArray=w;for(var t=[],e=[],r=typeof Uint8Array<"u"?Uint8Array:Array,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",i=0,s=n.length;i<s;++i)t[i]=n[i],e[n.charCodeAt(i)]=i;e[45]=62,e[95]=63;function o(y){var P=y.length;if(P%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var E=y.indexOf("=");E===-1&&(E=P);var b=E===P?0:4-E%4;return[E,b]}function a(y){var P=o(y),E=P[0],b=P[1];return(E+b)*3/4-b}function c(y,P,E){return(P+E)*3/4-E}function f(y){var P,E=o(y),b=E[0],m=E[1],v=new r(c(y,b,m)),R=0,T=m>0?b-4:b,g;for(g=0;g<T;g+=4)P=e[y.charCodeAt(g)]<<18|e[y.charCodeAt(g+1)]<<12|e[y.charCodeAt(g+2)]<<6|e[y.charCodeAt(g+3)],v[R++]=P>>16&255,v[R++]=P>>8&255,v[R++]=P&255;return m===2&&(P=e[y.charCodeAt(g)]<<2|e[y.charCodeAt(g+1)]>>4,v[R++]=P&255),m===1&&(P=e[y.charCodeAt(g)]<<10|e[y.charCodeAt(g+1)]<<4|e[y.charCodeAt(g+2)]>>2,v[R++]=P>>8&255,v[R++]=P&255),v}function h(y){return t[y>>18&63]+t[y>>12&63]+t[y>>6&63]+t[y&63]}function d(y,P,E){for(var b,m=[],v=P;v<E;v+=3)b=(y[v]<<16&16711680)+(y[v+1]<<8&65280)+(y[v+2]&255),m.push(h(b));return m.join("")}function w(y){for(var P,E=y.length,b=E%3,m=[],v=16383,R=0,T=E-b;R<T;R+=v)m.push(d(y,R,R+v>T?T:R+v));return b===1?(P=y[E-1],m.push(t[P>>2]+t[P<<4&63]+"==")):b===2&&(P=(y[E-2]<<8)+y[E-1],m.push(t[P>>10]+t[P>>4&63]+t[P<<2&63]+"=")),m.join("")}return On}function rw(){if(Ef)return ki;Ef=!0;return ki.read=function(t,e,r,n,i){var s,o,a=i*8-n-1,c=(1<<a)-1,f=c>>1,h=-7,d=r?i-1:0,w=r?-1:1,y=t[e+d];for(d+=w,s=y&(1<<-h)-1,y>>=-h,h+=a;h>0;s=s*256+t[e+d],d+=w,h-=8);for(o=s&(1<<-h)-1,s>>=-h,h+=n;h>0;o=o*256+t[e+d],d+=w,h-=8);if(s===0)s=1-f;else{if(s===c)return o?NaN:(y?-1:1)*(1/0);o=o+Math.pow(2,n),s=s-f}return(y?-1:1)*o*Math.pow(2,s-n)},ki.write=function(t,e,r,n,i,s){var o,a,c,f=s*8-i-1,h=(1<<f)-1,d=h>>1,w=i===23?Math.pow(2,-24)-Math.pow(2,-77):0,y=n?0:s-1,P=n?1:-1,E=e<0||e===0&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,o=h):(o=Math.floor(Math.log(e)/Math.LN2),e*(c=Math.pow(2,-o))<1&&(o--,c*=2),o+d>=1?e+=w/c:e+=w*Math.pow(2,1-d),e*c>=2&&(o++,c/=2),o+d>=h?(a=0,o=h):o+d>=1?(a=(e*c-1)*Math.pow(2,i),o=o+d):(a=e*Math.pow(2,d-1)*Math.pow(2,i),o=0));i>=8;t[r+y]=a&255,y+=P,a/=256,i-=8);for(o=o<<i|a,f+=i;f>0;t[r+y]=o&255,y+=P,o/=256,f-=8);t[r+y-P]|=E*128},ki}function If(){if(Af)return mr;Af=!0;let t=tw(),e=rw(),r=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;mr.Buffer=o,mr.SlowBuffer=m,mr.INSPECT_MAX_BYTES=50;let n=**********;mr.kMaxLength=n,o.TYPED_ARRAY_SUPPORT=i(),!o.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function i(){try{let p=new Uint8Array(1),u={foo:function(){return 42}};return Object.setPrototypeOf(u,Uint8Array.prototype),Object.setPrototypeOf(p,u),p.foo()===42}catch{return!1}}Object.defineProperty(o.prototype,"parent",{enumerable:!0,get:function(){if(o.isBuffer(this))return this.buffer}}),Object.defineProperty(o.prototype,"offset",{enumerable:!0,get:function(){if(o.isBuffer(this))return this.byteOffset}});function s(p){if(p>n)throw new RangeError('The value "'+p+'" is invalid for option "size"');let u=new Uint8Array(p);return Object.setPrototypeOf(u,o.prototype),u}function o(p,u,l){if(typeof p=="number"){if(typeof u=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return h(p)}return a(p,u,l)}o.poolSize=8192;function a(p,u,l){if(typeof p=="string")return d(p,u);if(ArrayBuffer.isView(p))return y(p);if(p==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof p);if(Ze(p,ArrayBuffer)||p&&Ze(p.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(Ze(p,SharedArrayBuffer)||p&&Ze(p.buffer,SharedArrayBuffer)))return P(p,u,l);if(typeof p=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');let S=p.valueOf&&p.valueOf();if(S!=null&&S!==p)return o.from(S,u,l);let C=E(p);if(C)return C;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof p[Symbol.toPrimitive]=="function")return o.from(p[Symbol.toPrimitive]("string"),u,l);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof p)}o.from=function(p,u,l){return a(p,u,l)},Object.setPrototypeOf(o.prototype,Uint8Array.prototype),Object.setPrototypeOf(o,Uint8Array);function c(p){if(typeof p!="number")throw new TypeError('"size" argument must be of type number');if(p<0)throw new RangeError('The value "'+p+'" is invalid for option "size"')}function f(p,u,l){return c(p),p<=0?s(p):u!==void 0?typeof l=="string"?s(p).fill(u,l):s(p).fill(u):s(p)}o.alloc=function(p,u,l){return f(p,u,l)};function h(p){return c(p),s(p<0?0:b(p)|0)}o.allocUnsafe=function(p){return h(p)},o.allocUnsafeSlow=function(p){return h(p)};function d(p,u){if((typeof u!="string"||u==="")&&(u="utf8"),!o.isEncoding(u))throw new TypeError("Unknown encoding: "+u);let l=v(p,u)|0,S=s(l),C=S.write(p,u);return C!==l&&(S=S.slice(0,C)),S}function w(p){let u=p.length<0?0:b(p.length)|0,l=s(u);for(let S=0;S<u;S+=1)l[S]=p[S]&255;return l}function y(p){if(Ze(p,Uint8Array)){let u=new Uint8Array(p);return P(u.buffer,u.byteOffset,u.byteLength)}return w(p)}function P(p,u,l){if(u<0||p.byteLength<u)throw new RangeError('"offset" is outside of buffer bounds');if(p.byteLength<u+(l||0))throw new RangeError('"length" is outside of buffer bounds');let S;return u===void 0&&l===void 0?S=new Uint8Array(p):l===void 0?S=new Uint8Array(p,u):S=new Uint8Array(p,u,l),Object.setPrototypeOf(S,o.prototype),S}function E(p){if(o.isBuffer(p)){let u=b(p.length)|0,l=s(u);return l.length===0||p.copy(l,0,0,u),l}if(p.length!==void 0)return typeof p.length!="number"||Tn(p.length)?s(0):w(p);if(p.type==="Buffer"&&Array.isArray(p.data))return w(p.data)}function b(p){if(p>=n)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+n.toString(16)+" bytes");return p|0}function m(p){return+p!=p&&(p=0),o.alloc(+p)}o.isBuffer=function(u){return u!=null&&u._isBuffer===!0&&u!==o.prototype},o.compare=function(u,l){if(Ze(u,Uint8Array)&&(u=o.from(u,u.offset,u.byteLength)),Ze(l,Uint8Array)&&(l=o.from(l,l.offset,l.byteLength)),!o.isBuffer(u)||!o.isBuffer(l))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(u===l)return 0;let S=u.length,C=l.length;for(let N=0,F=Math.min(S,C);N<F;++N)if(u[N]!==l[N]){S=u[N],C=l[N];break}return S<C?-1:C<S?1:0},o.isEncoding=function(u){switch(String(u).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},o.concat=function(u,l){if(!Array.isArray(u))throw new TypeError('"list" argument must be an Array of Buffers');if(u.length===0)return o.alloc(0);let S;if(l===void 0)for(l=0,S=0;S<u.length;++S)l+=u[S].length;let C=o.allocUnsafe(l),N=0;for(S=0;S<u.length;++S){let F=u[S];if(Ze(F,Uint8Array))N+F.length>C.length?(o.isBuffer(F)||(F=o.from(F)),F.copy(C,N)):Uint8Array.prototype.set.call(C,F,N);else if(o.isBuffer(F))F.copy(C,N);else throw new TypeError('"list" argument must be an Array of Buffers');N+=F.length}return C};function v(p,u){if(o.isBuffer(p))return p.length;if(ArrayBuffer.isView(p)||Ze(p,ArrayBuffer))return p.byteLength;if(typeof p!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof p);let l=p.length,S=arguments.length>2&&arguments[2]===!0;if(!S&&l===0)return 0;let C=!1;for(;;)switch(u){case"ascii":case"latin1":case"binary":return l;case"utf8":case"utf-8":return Ct(p).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l*2;case"hex":return l>>>1;case"base64":return Ii(p).length;default:if(C)return S?-1:Ct(p).length;u=(""+u).toLowerCase(),C=!0}}o.byteLength=v;function R(p,u,l){let S=!1;if((u===void 0||u<0)&&(u=0),u>this.length||((l===void 0||l>this.length)&&(l=this.length),l<=0)||(l>>>=0,u>>>=0,l<=u))return"";for(p||(p="utf8");;)switch(p){case"hex":return V(this,u,l);case"utf8":case"utf-8":return H(this,u,l);case"ascii":return X(this,u,l);case"latin1":case"binary":return Q(this,u,l);case"base64":return W(this,u,l);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return te(this,u,l);default:if(S)throw new TypeError("Unknown encoding: "+p);p=(p+"").toLowerCase(),S=!0}}o.prototype._isBuffer=!0;function T(p,u,l){let S=p[u];p[u]=p[l],p[l]=S}o.prototype.swap16=function(){let u=this.length;if(u%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let l=0;l<u;l+=2)T(this,l,l+1);return this},o.prototype.swap32=function(){let u=this.length;if(u%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let l=0;l<u;l+=4)T(this,l,l+3),T(this,l+1,l+2);return this},o.prototype.swap64=function(){let u=this.length;if(u%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let l=0;l<u;l+=8)T(this,l,l+7),T(this,l+1,l+6),T(this,l+2,l+5),T(this,l+3,l+4);return this},o.prototype.toString=function(){let u=this.length;return u===0?"":arguments.length===0?H(this,0,u):R.apply(this,arguments)},o.prototype.toLocaleString=o.prototype.toString,o.prototype.equals=function(u){if(!o.isBuffer(u))throw new TypeError("Argument must be a Buffer");return this===u?!0:o.compare(this,u)===0},o.prototype.inspect=function(){let u="",l=mr.INSPECT_MAX_BYTES;return u=this.toString("hex",0,l).replace(/(.{2})/g,"$1 ").trim(),this.length>l&&(u+=" ... "),"<Buffer "+u+">"},r&&(o.prototype[r]=o.prototype.inspect),o.prototype.compare=function(u,l,S,C,N){if(Ze(u,Uint8Array)&&(u=o.from(u,u.offset,u.byteLength)),!o.isBuffer(u))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof u);if(l===void 0&&(l=0),S===void 0&&(S=u?u.length:0),C===void 0&&(C=0),N===void 0&&(N=this.length),l<0||S>u.length||C<0||N>this.length)throw new RangeError("out of range index");if(C>=N&&l>=S)return 0;if(C>=N)return-1;if(l>=S)return 1;if(l>>>=0,S>>>=0,C>>>=0,N>>>=0,this===u)return 0;let F=N-C,k=S-l,B=Math.min(F,k),re=this.slice(C,N),ie=u.slice(l,S);for(let oe=0;oe<B;++oe)if(re[oe]!==ie[oe]){F=re[oe],k=ie[oe];break}return F<k?-1:k<F?1:0};function g(p,u,l,S,C){if(p.length===0)return-1;if(typeof l=="string"?(S=l,l=0):l>**********?l=**********:l<-2147483648&&(l=-2147483648),l=+l,Tn(l)&&(l=C?0:p.length-1),l<0&&(l=p.length+l),l>=p.length){if(C)return-1;l=p.length-1}else if(l<0)if(C)l=0;else return-1;if(typeof u=="string"&&(u=o.from(u,S)),o.isBuffer(u))return u.length===0?-1:O(p,u,l,S,C);if(typeof u=="number")return u=u&255,typeof Uint8Array.prototype.indexOf=="function"?C?Uint8Array.prototype.indexOf.call(p,u,l):Uint8Array.prototype.lastIndexOf.call(p,u,l):O(p,[u],l,S,C);throw new TypeError("val must be string, number or Buffer")}function O(p,u,l,S,C){let N=1,F=p.length,k=u.length;if(S!==void 0&&(S=String(S).toLowerCase(),S==="ucs2"||S==="ucs-2"||S==="utf16le"||S==="utf-16le")){if(p.length<2||u.length<2)return-1;N=2,F/=2,k/=2,l/=2}function B(ie,oe){return N===1?ie[oe]:ie.readUInt16BE(oe*N)}let re;if(C){let ie=-1;for(re=l;re<F;re++)if(B(p,re)===B(u,ie===-1?0:re-ie)){if(ie===-1&&(ie=re),re-ie+1===k)return ie*N}else ie!==-1&&(re-=re-ie),ie=-1}else for(l+k>F&&(l=F-k),re=l;re>=0;re--){let ie=!0;for(let oe=0;oe<k;oe++)if(B(p,re+oe)!==B(u,oe)){ie=!1;break}if(ie)return re}return-1}o.prototype.includes=function(u,l,S){return this.indexOf(u,l,S)!==-1},o.prototype.indexOf=function(u,l,S){return g(this,u,l,S,!0)},o.prototype.lastIndexOf=function(u,l,S){return g(this,u,l,S,!1)};function _(p,u,l,S){l=Number(l)||0;let C=p.length-l;S?(S=Number(S),S>C&&(S=C)):S=C;let N=u.length;S>N/2&&(S=N/2);let F;for(F=0;F<S;++F){let k=parseInt(u.substr(F*2,2),16);if(Tn(k))return F;p[l+F]=k}return F}function q(p,u,l,S){return Vr(Ct(u,p.length-l),p,l,S)}function $(p,u,l,S){return Vr(Bt(u),p,l,S)}function j(p,u,l,S){return Vr(Ii(u),p,l,S)}function J(p,u,l,S){return Vr(xn(u,p.length-l),p,l,S)}o.prototype.write=function(u,l,S,C){if(l===void 0)C="utf8",S=this.length,l=0;else if(S===void 0&&typeof l=="string")C=l,S=this.length,l=0;else if(isFinite(l))l=l>>>0,isFinite(S)?(S=S>>>0,C===void 0&&(C="utf8")):(C=S,S=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let N=this.length-l;if((S===void 0||S>N)&&(S=N),u.length>0&&(S<0||l<0)||l>this.length)throw new RangeError("Attempt to write outside buffer bounds");C||(C="utf8");let F=!1;for(;;)switch(C){case"hex":return _(this,u,l,S);case"utf8":case"utf-8":return q(this,u,l,S);case"ascii":case"latin1":case"binary":return $(this,u,l,S);case"base64":return j(this,u,l,S);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return J(this,u,l,S);default:if(F)throw new TypeError("Unknown encoding: "+C);C=(""+C).toLowerCase(),F=!0}},o.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function W(p,u,l){return u===0&&l===p.length?t.fromByteArray(p):t.fromByteArray(p.slice(u,l))}function H(p,u,l){l=Math.min(p.length,l);let S=[],C=u;for(;C<l;){let N=p[C],F=null,k=N>239?4:N>223?3:N>191?2:1;if(C+k<=l){let B,re,ie,oe;switch(k){case 1:N<128&&(F=N);break;case 2:B=p[C+1],(B&192)===128&&(oe=(N&31)<<6|B&63,oe>127&&(F=oe));break;case 3:B=p[C+1],re=p[C+2],(B&192)===128&&(re&192)===128&&(oe=(N&15)<<12|(B&63)<<6|re&63,oe>2047&&(oe<55296||oe>57343)&&(F=oe));break;case 4:B=p[C+1],re=p[C+2],ie=p[C+3],(B&192)===128&&(re&192)===128&&(ie&192)===128&&(oe=(N&15)<<18|(B&63)<<12|(re&63)<<6|ie&63,oe>65535&&oe<1114112&&(F=oe))}}F===null?(F=65533,k=1):F>65535&&(F-=65536,S.push(F>>>10&1023|55296),F=56320|F&1023),S.push(F),C+=k}return K(S)}let G=4096;function K(p){let u=p.length;if(u<=G)return String.fromCharCode.apply(String,p);let l="",S=0;for(;S<u;)l+=String.fromCharCode.apply(String,p.slice(S,S+=G));return l}function X(p,u,l){let S="";l=Math.min(p.length,l);for(let C=u;C<l;++C)S+=String.fromCharCode(p[C]&127);return S}function Q(p,u,l){let S="";l=Math.min(p.length,l);for(let C=u;C<l;++C)S+=String.fromCharCode(p[C]);return S}function V(p,u,l){let S=p.length;(!u||u<0)&&(u=0),(!l||l<0||l>S)&&(l=S);let C="";for(let N=u;N<l;++N)C+=Xo[p[N]];return C}function te(p,u,l){let S=p.slice(u,l),C="";for(let N=0;N<S.length-1;N+=2)C+=String.fromCharCode(S[N]+S[N+1]*256);return C}o.prototype.slice=function(u,l){let S=this.length;u=~~u,l=l===void 0?S:~~l,u<0?(u+=S,u<0&&(u=0)):u>S&&(u=S),l<0?(l+=S,l<0&&(l=0)):l>S&&(l=S),l<u&&(l=u);let C=this.subarray(u,l);return Object.setPrototypeOf(C,o.prototype),C};function Y(p,u,l){if(p%1!==0||p<0)throw new RangeError("offset is not uint");if(p+u>l)throw new RangeError("Trying to access beyond buffer length")}o.prototype.readUintLE=o.prototype.readUIntLE=function(u,l,S){u=u>>>0,l=l>>>0,S||Y(u,l,this.length);let C=this[u],N=1,F=0;for(;++F<l&&(N*=256);)C+=this[u+F]*N;return C},o.prototype.readUintBE=o.prototype.readUIntBE=function(u,l,S){u=u>>>0,l=l>>>0,S||Y(u,l,this.length);let C=this[u+--l],N=1;for(;l>0&&(N*=256);)C+=this[u+--l]*N;return C},o.prototype.readUint8=o.prototype.readUInt8=function(u,l){return u=u>>>0,l||Y(u,1,this.length),this[u]},o.prototype.readUint16LE=o.prototype.readUInt16LE=function(u,l){return u=u>>>0,l||Y(u,2,this.length),this[u]|this[u+1]<<8},o.prototype.readUint16BE=o.prototype.readUInt16BE=function(u,l){return u=u>>>0,l||Y(u,2,this.length),this[u]<<8|this[u+1]},o.prototype.readUint32LE=o.prototype.readUInt32LE=function(u,l){return u=u>>>0,l||Y(u,4,this.length),(this[u]|this[u+1]<<8|this[u+2]<<16)+this[u+3]*16777216},o.prototype.readUint32BE=o.prototype.readUInt32BE=function(u,l){return u=u>>>0,l||Y(u,4,this.length),this[u]*16777216+(this[u+1]<<16|this[u+2]<<8|this[u+3])},o.prototype.readBigUInt64LE=at(function(u){u=u>>>0,xe(u,"offset");let l=this[u],S=this[u+7];(l===void 0||S===void 0)&&st(u,this.length-8);let C=l+this[++u]*2**8+this[++u]*2**16+this[++u]*2**24,N=this[++u]+this[++u]*2**8+this[++u]*2**16+S*2**24;return BigInt(C)+(BigInt(N)<<BigInt(32))}),o.prototype.readBigUInt64BE=at(function(u){u=u>>>0,xe(u,"offset");let l=this[u],S=this[u+7];(l===void 0||S===void 0)&&st(u,this.length-8);let C=l*2**24+this[++u]*2**16+this[++u]*2**8+this[++u],N=this[++u]*2**24+this[++u]*2**16+this[++u]*2**8+S;return(BigInt(C)<<BigInt(32))+BigInt(N)}),o.prototype.readIntLE=function(u,l,S){u=u>>>0,l=l>>>0,S||Y(u,l,this.length);let C=this[u],N=1,F=0;for(;++F<l&&(N*=256);)C+=this[u+F]*N;return N*=128,C>=N&&(C-=Math.pow(2,8*l)),C},o.prototype.readIntBE=function(u,l,S){u=u>>>0,l=l>>>0,S||Y(u,l,this.length);let C=l,N=1,F=this[u+--C];for(;C>0&&(N*=256);)F+=this[u+--C]*N;return N*=128,F>=N&&(F-=Math.pow(2,8*l)),F},o.prototype.readInt8=function(u,l){return u=u>>>0,l||Y(u,1,this.length),this[u]&128?(255-this[u]+1)*-1:this[u]},o.prototype.readInt16LE=function(u,l){u=u>>>0,l||Y(u,2,this.length);let S=this[u]|this[u+1]<<8;return S&32768?S|4294901760:S},o.prototype.readInt16BE=function(u,l){u=u>>>0,l||Y(u,2,this.length);let S=this[u+1]|this[u]<<8;return S&32768?S|4294901760:S},o.prototype.readInt32LE=function(u,l){return u=u>>>0,l||Y(u,4,this.length),this[u]|this[u+1]<<8|this[u+2]<<16|this[u+3]<<24},o.prototype.readInt32BE=function(u,l){return u=u>>>0,l||Y(u,4,this.length),this[u]<<24|this[u+1]<<16|this[u+2]<<8|this[u+3]},o.prototype.readBigInt64LE=at(function(u){u=u>>>0,xe(u,"offset");let l=this[u],S=this[u+7];(l===void 0||S===void 0)&&st(u,this.length-8);let C=this[u+4]+this[u+5]*2**8+this[u+6]*2**16+(S<<24);return(BigInt(C)<<BigInt(32))+BigInt(l+this[++u]*2**8+this[++u]*2**16+this[++u]*2**24)}),o.prototype.readBigInt64BE=at(function(u){u=u>>>0,xe(u,"offset");let l=this[u],S=this[u+7];(l===void 0||S===void 0)&&st(u,this.length-8);let C=(l<<24)+this[++u]*2**16+this[++u]*2**8+this[++u];return(BigInt(C)<<BigInt(32))+BigInt(this[++u]*2**24+this[++u]*2**16+this[++u]*2**8+S)}),o.prototype.readFloatLE=function(u,l){return u=u>>>0,l||Y(u,4,this.length),e.read(this,u,!0,23,4)},o.prototype.readFloatBE=function(u,l){return u=u>>>0,l||Y(u,4,this.length),e.read(this,u,!1,23,4)},o.prototype.readDoubleLE=function(u,l){return u=u>>>0,l||Y(u,8,this.length),e.read(this,u,!0,52,8)},o.prototype.readDoubleBE=function(u,l){return u=u>>>0,l||Y(u,8,this.length),e.read(this,u,!1,52,8)};function ee(p,u,l,S,C,N){if(!o.isBuffer(p))throw new TypeError('"buffer" argument must be a Buffer instance');if(u>C||u<N)throw new RangeError('"value" argument is out of bounds');if(l+S>p.length)throw new RangeError("Index out of range")}o.prototype.writeUintLE=o.prototype.writeUIntLE=function(u,l,S,C){if(u=+u,l=l>>>0,S=S>>>0,!C){let k=Math.pow(2,8*S)-1;ee(this,u,l,S,k,0)}let N=1,F=0;for(this[l]=u&255;++F<S&&(N*=256);)this[l+F]=u/N&255;return l+S},o.prototype.writeUintBE=o.prototype.writeUIntBE=function(u,l,S,C){if(u=+u,l=l>>>0,S=S>>>0,!C){let k=Math.pow(2,8*S)-1;ee(this,u,l,S,k,0)}let N=S-1,F=1;for(this[l+N]=u&255;--N>=0&&(F*=256);)this[l+N]=u/F&255;return l+S},o.prototype.writeUint8=o.prototype.writeUInt8=function(u,l,S){return u=+u,l=l>>>0,S||ee(this,u,l,1,255,0),this[l]=u&255,l+1},o.prototype.writeUint16LE=o.prototype.writeUInt16LE=function(u,l,S){return u=+u,l=l>>>0,S||ee(this,u,l,2,65535,0),this[l]=u&255,this[l+1]=u>>>8,l+2},o.prototype.writeUint16BE=o.prototype.writeUInt16BE=function(u,l,S){return u=+u,l=l>>>0,S||ee(this,u,l,2,65535,0),this[l]=u>>>8,this[l+1]=u&255,l+2},o.prototype.writeUint32LE=o.prototype.writeUInt32LE=function(u,l,S){return u=+u,l=l>>>0,S||ee(this,u,l,4,4294967295,0),this[l+3]=u>>>24,this[l+2]=u>>>16,this[l+1]=u>>>8,this[l]=u&255,l+4},o.prototype.writeUint32BE=o.prototype.writeUInt32BE=function(u,l,S){return u=+u,l=l>>>0,S||ee(this,u,l,4,4294967295,0),this[l]=u>>>24,this[l+1]=u>>>16,this[l+2]=u>>>8,this[l+3]=u&255,l+4};function pe(p,u,l,S,C){Oe(u,S,C,p,l,7);let N=Number(u&BigInt(4294967295));p[l++]=N,N=N>>8,p[l++]=N,N=N>>8,p[l++]=N,N=N>>8,p[l++]=N;let F=Number(u>>BigInt(32)&BigInt(4294967295));return p[l++]=F,F=F>>8,p[l++]=F,F=F>>8,p[l++]=F,F=F>>8,p[l++]=F,l}function Z(p,u,l,S,C){Oe(u,S,C,p,l,7);let N=Number(u&BigInt(4294967295));p[l+7]=N,N=N>>8,p[l+6]=N,N=N>>8,p[l+5]=N,N=N>>8,p[l+4]=N;let F=Number(u>>BigInt(32)&BigInt(4294967295));return p[l+3]=F,F=F>>8,p[l+2]=F,F=F>>8,p[l+1]=F,F=F>>8,p[l]=F,l+8}o.prototype.writeBigUInt64LE=at(function(u,l=0){return pe(this,u,l,BigInt(0),BigInt("0xffffffffffffffff"))}),o.prototype.writeBigUInt64BE=at(function(u,l=0){return Z(this,u,l,BigInt(0),BigInt("0xffffffffffffffff"))}),o.prototype.writeIntLE=function(u,l,S,C){if(u=+u,l=l>>>0,!C){let B=Math.pow(2,8*S-1);ee(this,u,l,S,B-1,-B)}let N=0,F=1,k=0;for(this[l]=u&255;++N<S&&(F*=256);)u<0&&k===0&&this[l+N-1]!==0&&(k=1),this[l+N]=(u/F>>0)-k&255;return l+S},o.prototype.writeIntBE=function(u,l,S,C){if(u=+u,l=l>>>0,!C){let B=Math.pow(2,8*S-1);ee(this,u,l,S,B-1,-B)}let N=S-1,F=1,k=0;for(this[l+N]=u&255;--N>=0&&(F*=256);)u<0&&k===0&&this[l+N+1]!==0&&(k=1),this[l+N]=(u/F>>0)-k&255;return l+S},o.prototype.writeInt8=function(u,l,S){return u=+u,l=l>>>0,S||ee(this,u,l,1,127,-128),u<0&&(u=255+u+1),this[l]=u&255,l+1},o.prototype.writeInt16LE=function(u,l,S){return u=+u,l=l>>>0,S||ee(this,u,l,2,32767,-32768),this[l]=u&255,this[l+1]=u>>>8,l+2},o.prototype.writeInt16BE=function(u,l,S){return u=+u,l=l>>>0,S||ee(this,u,l,2,32767,-32768),this[l]=u>>>8,this[l+1]=u&255,l+2},o.prototype.writeInt32LE=function(u,l,S){return u=+u,l=l>>>0,S||ee(this,u,l,4,**********,-2147483648),this[l]=u&255,this[l+1]=u>>>8,this[l+2]=u>>>16,this[l+3]=u>>>24,l+4},o.prototype.writeInt32BE=function(u,l,S){return u=+u,l=l>>>0,S||ee(this,u,l,4,**********,-2147483648),u<0&&(u=4294967295+u+1),this[l]=u>>>24,this[l+1]=u>>>16,this[l+2]=u>>>8,this[l+3]=u&255,l+4},o.prototype.writeBigInt64LE=at(function(u,l=0){return pe(this,u,l,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),o.prototype.writeBigInt64BE=at(function(u,l=0){return Z(this,u,l,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function ne(p,u,l,S,C,N){if(l+S>p.length)throw new RangeError("Index out of range");if(l<0)throw new RangeError("Index out of range")}function fe(p,u,l,S,C){return u=+u,l=l>>>0,C||ne(p,u,l,4),e.write(p,u,l,S,23,4),l+4}o.prototype.writeFloatLE=function(u,l,S){return fe(this,u,l,!0,S)},o.prototype.writeFloatBE=function(u,l,S){return fe(this,u,l,!1,S)};function ge(p,u,l,S,C){return u=+u,l=l>>>0,C||ne(p,u,l,8),e.write(p,u,l,S,52,8),l+8}o.prototype.writeDoubleLE=function(u,l,S){return ge(this,u,l,!0,S)},o.prototype.writeDoubleBE=function(u,l,S){return ge(this,u,l,!1,S)},o.prototype.copy=function(u,l,S,C){if(!o.isBuffer(u))throw new TypeError("argument should be a Buffer");if(S||(S=0),!C&&C!==0&&(C=this.length),l>=u.length&&(l=u.length),l||(l=0),C>0&&C<S&&(C=S),C===S||u.length===0||this.length===0)return 0;if(l<0)throw new RangeError("targetStart out of bounds");if(S<0||S>=this.length)throw new RangeError("Index out of range");if(C<0)throw new RangeError("sourceEnd out of bounds");C>this.length&&(C=this.length),u.length-l<C-S&&(C=u.length-l+S);let N=C-S;return this===u&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(l,S,C):Uint8Array.prototype.set.call(u,this.subarray(S,C),l),N},o.prototype.fill=function(u,l,S,C){if(typeof u=="string"){if(typeof l=="string"?(C=l,l=0,S=this.length):typeof S=="string"&&(C=S,S=this.length),C!==void 0&&typeof C!="string")throw new TypeError("encoding must be a string");if(typeof C=="string"&&!o.isEncoding(C))throw new TypeError("Unknown encoding: "+C);if(u.length===1){let F=u.charCodeAt(0);(C==="utf8"&&F<128||C==="latin1")&&(u=F)}}else typeof u=="number"?u=u&255:typeof u=="boolean"&&(u=Number(u));if(l<0||this.length<l||this.length<S)throw new RangeError("Out of range index");if(S<=l)return this;l=l>>>0,S=S===void 0?this.length:S>>>0,u||(u=0);let N;if(typeof u=="number")for(N=l;N<S;++N)this[N]=u;else{let F=o.isBuffer(u)?u:o.from(u,C),k=F.length;if(k===0)throw new TypeError('The value "'+u+'" is invalid for argument "value"');for(N=0;N<S-l;++N)this[N+l]=F[N%k]}return this};let ye={};function Ae(p,u,l){ye[p]=class extends l{constructor(){super(),Object.defineProperty(this,"message",{value:u.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${p}]`,this.stack,delete this.name}get code(){return p}set code(C){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:C,writable:!0})}toString(){return`${this.name} [${p}]: ${this.message}`}}}Ae("ERR_BUFFER_OUT_OF_BOUNDS",function(p){return p?`${p} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),Ae("ERR_INVALID_ARG_TYPE",function(p,u){return`The "${p}" argument must be of type number. Received type ${typeof u}`},TypeError),Ae("ERR_OUT_OF_RANGE",function(p,u,l){let S=`The value of "${p}" is out of range.`,C=l;return Number.isInteger(l)&&Math.abs(l)>2**32?C=_e(String(l)):typeof l=="bigint"&&(C=String(l),(l>BigInt(2)**BigInt(32)||l<-(BigInt(2)**BigInt(32)))&&(C=_e(C)),C+="n"),S+=` It must be ${u}. Received ${C}`,S},RangeError);function _e(p){let u="",l=p.length,S=p[0]==="-"?1:0;for(;l>=S+4;l-=3)u=`_${p.slice(l-3,l)}${u}`;return`${p.slice(0,l)}${u}`}function ve(p,u,l){xe(u,"offset"),(p[u]===void 0||p[u+l]===void 0)&&st(u,p.length-(l+1))}function Oe(p,u,l,S,C,N){if(p>l||p<u){let F=typeof u=="bigint"?"n":"",k;throw u===0||u===BigInt(0)?k=`>= 0${F} and < 2${F} ** ${(N+1)*8}${F}`:k=`>= -(2${F} ** ${(N+1)*8-1}${F}) and < 2 ** ${(N+1)*8-1}${F}`,new ye.ERR_OUT_OF_RANGE("value",k,p)}ve(S,C,N)}function xe(p,u){if(typeof p!="number")throw new ye.ERR_INVALID_ARG_TYPE(u,"number",p)}function st(p,u,l){throw Math.floor(p)!==p?(xe(p,l),new ye.ERR_OUT_OF_RANGE("offset","an integer",p)):u<0?new ye.ERR_BUFFER_OUT_OF_BOUNDS:new ye.ERR_OUT_OF_RANGE("offset",`>= 0 and <= ${u}`,p)}let Re=/[^+/0-9A-Za-z-_]/g;function Ne(p){if(p=p.split("=")[0],p=p.trim().replace(Re,""),p.length<2)return"";for(;p.length%4!==0;)p=p+"=";return p}function Ct(p,u){u=u||1/0;let l,S=p.length,C=null,N=[];for(let F=0;F<S;++F){if(l=p.charCodeAt(F),l>55295&&l<57344){if(!C){if(l>56319){(u-=3)>-1&&N.push(239,191,189);continue}else if(F+1===S){(u-=3)>-1&&N.push(239,191,189);continue}C=l;continue}if(l<56320){(u-=3)>-1&&N.push(239,191,189),C=l;continue}l=(C-55296<<10|l-56320)+65536}else C&&(u-=3)>-1&&N.push(239,191,189);if(C=null,l<128){if((u-=1)<0)break;N.push(l)}else if(l<2048){if((u-=2)<0)break;N.push(l>>6|192,l&63|128)}else if(l<65536){if((u-=3)<0)break;N.push(l>>12|224,l>>6&63|128,l&63|128)}else if(l<1114112){if((u-=4)<0)break;N.push(l>>18|240,l>>12&63|128,l>>6&63|128,l&63|128)}else throw new Error("Invalid code point")}return N}function Bt(p){let u=[];for(let l=0;l<p.length;++l)u.push(p.charCodeAt(l)&255);return u}function xn(p,u){let l,S,C,N=[];for(let F=0;F<p.length&&!((u-=2)<0);++F)l=p.charCodeAt(F),S=l>>8,C=l%256,N.push(C),N.push(S);return N}function Ii(p){return t.toByteArray(Ne(p))}function Vr(p,u,l,S){let C;for(C=0;C<S&&!(C+l>=u.length||C>=p.length);++C)u[C+l]=p[C];return C}function Ze(p,u){return p instanceof u||p!=null&&p.constructor!=null&&p.constructor.name!=null&&p.constructor.name===u.name}function Tn(p){return p!==p}let Xo=function(){let p="0123456789abcdef",u=new Array(256);for(let l=0;l<16;++l){let S=l*16;for(let C=0;C<16;++C)u[S+C]=p[l]+p[C]}return u}();function at(p){return typeof BigInt>"u"?Zo:p}function Zo(){throw new Error("BigInt not supported")}return mr}var On,Sf,ki,Ef,mr,Af,xf=ze(()=>{A();x();I();On={},Sf=!1;ki={},Ef=!1;mr={},Af=!1});var qe={};Kr(qe,{Buffer:()=>U,INSPECT_MAX_BYTES:()=>nw,default:()=>Xt,kMaxLength:()=>iw});var Xt,U,nw,iw,ke=ze(()=>{A();x();I();xf();Xt=If();Xt.Buffer;Xt.SlowBuffer;Xt.INSPECT_MAX_BYTES;Xt.kMaxLength;U=Xt.Buffer,nw=Xt.INSPECT_MAX_BYTES,iw=Xt.kMaxLength});var x=ze(()=>{ke()});var Ee=D((MP,Tf)=>{"use strict";A();x();I();var fs=class extends Error{constructor(e){if(!Array.isArray(e))throw new TypeError(`Expected input to be an Array, got ${typeof e}`);let r="";for(let n=0;n<e.length;n++)r+=`    ${e[n].stack}
`;super(r),this.name="AggregateError",this.errors=e}};Tf.exports={AggregateError:fs,ArrayIsArray(t){return Array.isArray(t)},ArrayPrototypeIncludes(t,e){return t.includes(e)},ArrayPrototypeIndexOf(t,e){return t.indexOf(e)},ArrayPrototypeJoin(t,e){return t.join(e)},ArrayPrototypeMap(t,e){return t.map(e)},ArrayPrototypePop(t,e){return t.pop(e)},ArrayPrototypePush(t,e){return t.push(e)},ArrayPrototypeSlice(t,e,r){return t.slice(e,r)},Error,FunctionPrototypeCall(t,e,...r){return t.call(e,...r)},FunctionPrototypeSymbolHasInstance(t,e){return Function.prototype[Symbol.hasInstance].call(t,e)},MathFloor:Math.floor,Number,NumberIsInteger:Number.isInteger,NumberIsNaN:Number.isNaN,NumberMAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER,NumberMIN_SAFE_INTEGER:Number.MIN_SAFE_INTEGER,NumberParseInt:Number.parseInt,ObjectDefineProperties(t,e){return Object.defineProperties(t,e)},ObjectDefineProperty(t,e,r){return Object.defineProperty(t,e,r)},ObjectGetOwnPropertyDescriptor(t,e){return Object.getOwnPropertyDescriptor(t,e)},ObjectKeys(t){return Object.keys(t)},ObjectSetPrototypeOf(t,e){return Object.setPrototypeOf(t,e)},Promise,PromisePrototypeCatch(t,e){return t.catch(e)},PromisePrototypeThen(t,e,r){return t.then(e,r)},PromiseReject(t){return Promise.reject(t)},PromiseResolve(t){return Promise.resolve(t)},ReflectApply:Reflect.apply,RegExpPrototypeTest(t,e){return t.test(e)},SafeSet:Set,String,StringPrototypeSlice(t,e,r){return t.slice(e,r)},StringPrototypeToLowerCase(t){return t.toLowerCase()},StringPrototypeToUpperCase(t){return t.toUpperCase()},StringPrototypeTrim(t){return t.trim()},Symbol,SymbolFor:Symbol.for,SymbolAsyncIterator:Symbol.asyncIterator,SymbolHasInstance:Symbol.hasInstance,SymbolIterator:Symbol.iterator,SymbolDispose:Symbol.dispose||Symbol("Symbol.dispose"),SymbolAsyncDispose:Symbol.asyncDispose||Symbol("Symbol.asyncDispose"),TypedArrayPrototypeSet(t,e,r){return t.set(e,r)},Boolean,Uint8Array}});var hs=D((HP,Pf)=>{"use strict";A();x();I();Pf.exports={format(t,...e){return t.replace(/%([sdifj])/g,function(...[r,n]){let i=e.shift();return n==="f"?i.toFixed(6):n==="j"?JSON.stringify(i):n==="s"&&typeof i=="object"?`${i.constructor!==Object?i.constructor.name:""} {}`.trim():i.toString()})},inspect(t){switch(typeof t){case"string":if(t.includes("'"))if(t.includes('"')){if(!t.includes("`")&&!t.includes("${"))return`\`${t}\``}else return`"${t}"`;return`'${t}'`;case"number":return isNaN(t)?"NaN":Object.is(t,-0)?String(t):t;case"bigint":return`${String(t)}n`;case"boolean":case"undefined":return String(t);case"object":return"{}"}}}});var De=D((eO,kf)=>{"use strict";A();x();I();var{format:ow,inspect:Ci}=hs(),{AggregateError:sw}=Ee(),aw=globalThis.AggregateError||sw,uw=Symbol("kIsNodeError"),lw=["string","function","number","object","Function","Object","boolean","bigint","symbol"],cw=/^([A-Z][a-z0-9]*)+$/,fw="__node_internal_",Bi={};function _r(t,e){if(!t)throw new Bi.ERR_INTERNAL_ASSERTION(e)}function Of(t){let e="",r=t.length,n=t[0]==="-"?1:0;for(;r>=n+4;r-=3)e=`_${t.slice(r-3,r)}${e}`;return`${t.slice(0,r)}${e}`}function hw(t,e,r){if(typeof e=="function")return _r(e.length<=r.length,`Code: ${t}; The provided arguments length (${r.length}) does not match the required ones (${e.length}).`),e(...r);let n=(e.match(/%[dfijoOs]/g)||[]).length;return _r(n===r.length,`Code: ${t}; The provided arguments length (${r.length}) does not match the required ones (${n}).`),r.length===0?e:ow(e,...r)}function Le(t,e,r){r||(r=Error);class n extends r{constructor(...s){super(hw(t,e,s))}toString(){return`${this.name} [${t}]: ${this.message}`}}Object.defineProperties(n.prototype,{name:{value:r.name,writable:!0,enumerable:!1,configurable:!0},toString:{value(){return`${this.name} [${t}]: ${this.message}`},writable:!0,enumerable:!1,configurable:!0}}),n.prototype.code=t,n.prototype[uw]=!0,Bi[t]=n}function Rf(t){let e=fw+t.name;return Object.defineProperty(t,"name",{value:e}),t}function dw(t,e){if(t&&e&&t!==e){if(Array.isArray(e.errors))return e.errors.push(t),e;let r=new aw([e,t],e.message);return r.code=e.code,r}return t||e}var ds=class extends Error{constructor(e="The operation was aborted",r=void 0){if(r!==void 0&&typeof r!="object")throw new Bi.ERR_INVALID_ARG_TYPE("options","Object",r);super(e,r),this.code="ABORT_ERR",this.name="AbortError"}};Le("ERR_ASSERTION","%s",Error);Le("ERR_INVALID_ARG_TYPE",(t,e,r)=>{_r(typeof t=="string","'name' must be a string"),Array.isArray(e)||(e=[e]);let n="The ";t.endsWith(" argument")?n+=`${t} `:n+=`"${t}" ${t.includes(".")?"property":"argument"} `,n+="must be ";let i=[],s=[],o=[];for(let c of e)_r(typeof c=="string","All expected entries have to be of type string"),lw.includes(c)?i.push(c.toLowerCase()):cw.test(c)?s.push(c):(_r(c!=="object",'The value "object" should be written as "Object"'),o.push(c));if(s.length>0){let c=i.indexOf("object");c!==-1&&(i.splice(i,c,1),s.push("Object"))}if(i.length>0){switch(i.length){case 1:n+=`of type ${i[0]}`;break;case 2:n+=`one of type ${i[0]} or ${i[1]}`;break;default:{let c=i.pop();n+=`one of type ${i.join(", ")}, or ${c}`}}(s.length>0||o.length>0)&&(n+=" or ")}if(s.length>0){switch(s.length){case 1:n+=`an instance of ${s[0]}`;break;case 2:n+=`an instance of ${s[0]} or ${s[1]}`;break;default:{let c=s.pop();n+=`an instance of ${s.join(", ")}, or ${c}`}}o.length>0&&(n+=" or ")}switch(o.length){case 0:break;case 1:o[0].toLowerCase()!==o[0]&&(n+="an "),n+=`${o[0]}`;break;case 2:n+=`one of ${o[0]} or ${o[1]}`;break;default:{let c=o.pop();n+=`one of ${o.join(", ")}, or ${c}`}}if(r==null)n+=`. Received ${r}`;else if(typeof r=="function"&&r.name)n+=`. Received function ${r.name}`;else if(typeof r=="object"){var a;if((a=r.constructor)!==null&&a!==void 0&&a.name)n+=`. Received an instance of ${r.constructor.name}`;else{let c=Ci(r,{depth:-1});n+=`. Received ${c}`}}else{let c=Ci(r,{colors:!1});c.length>25&&(c=`${c.slice(0,25)}...`),n+=`. Received type ${typeof r} (${c})`}return n},TypeError);Le("ERR_INVALID_ARG_VALUE",(t,e,r="is invalid")=>{let n=Ci(e);return n.length>128&&(n=n.slice(0,128)+"..."),`The ${t.includes(".")?"property":"argument"} '${t}' ${r}. Received ${n}`},TypeError);Le("ERR_INVALID_RETURN_VALUE",(t,e,r)=>{var n;let i=r!=null&&(n=r.constructor)!==null&&n!==void 0&&n.name?`instance of ${r.constructor.name}`:`type ${typeof r}`;return`Expected ${t} to be returned from the "${e}" function but got ${i}.`},TypeError);Le("ERR_MISSING_ARGS",(...t)=>{_r(t.length>0,"At least one arg needs to be specified");let e,r=t.length;switch(t=(Array.isArray(t)?t:[t]).map(n=>`"${n}"`).join(" or "),r){case 1:e+=`The ${t[0]} argument`;break;case 2:e+=`The ${t[0]} and ${t[1]} arguments`;break;default:{let n=t.pop();e+=`The ${t.join(", ")}, and ${n} arguments`}break}return`${e} must be specified`},TypeError);Le("ERR_OUT_OF_RANGE",(t,e,r)=>{_r(e,'Missing "range" argument');let n;if(Number.isInteger(r)&&Math.abs(r)>2**32)n=Of(String(r));else if(typeof r=="bigint"){n=String(r);let i=BigInt(2)**BigInt(32);(r>i||r<-i)&&(n=Of(n)),n+="n"}else n=Ci(r);return`The value of "${t}" is out of range. It must be ${e}. Received ${n}`},RangeError);Le("ERR_MULTIPLE_CALLBACK","Callback called multiple times",Error);Le("ERR_METHOD_NOT_IMPLEMENTED","The %s method is not implemented",Error);Le("ERR_STREAM_ALREADY_FINISHED","Cannot call %s after a stream was finished",Error);Le("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable",Error);Le("ERR_STREAM_DESTROYED","Cannot call %s after a stream was destroyed",Error);Le("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError);Le("ERR_STREAM_PREMATURE_CLOSE","Premature close",Error);Le("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF",Error);Le("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event",Error);Le("ERR_STREAM_WRITE_AFTER_END","write after end",Error);Le("ERR_UNKNOWN_ENCODING","Unknown encoding: %s",TypeError);kf.exports={AbortError:ds,aggregateTwoErrors:Rf(dw),hideStackFrames:Rf,codes:Bi}});var Yr=D((cO,Mi)=>{"use strict";A();x();I();var{AbortController:Cf,AbortSignal:pw}=typeof self<"u"?self:typeof window<"u"?window:void 0;Mi.exports=Cf;Mi.exports.AbortSignal=pw;Mi.exports.default=Cf});var Zt={};Kr(Zt,{EventEmitter:()=>gw,default:()=>Rn,defaultMaxListeners:()=>bw,init:()=>ww,listenerCount:()=>mw,on:()=>_w,once:()=>vw});function yw(){if(Bf)return Ni;Bf=!0;var t=typeof Reflect=="object"?Reflect:null,e=t&&typeof t.apply=="function"?t.apply:function(g,O,_){return Function.prototype.apply.call(g,O,_)},r;t&&typeof t.ownKeys=="function"?r=t.ownKeys:Object.getOwnPropertySymbols?r=function(g){return Object.getOwnPropertyNames(g).concat(Object.getOwnPropertySymbols(g))}:r=function(g){return Object.getOwnPropertyNames(g)};function n(T){console&&console.warn&&console.warn(T)}var i=Number.isNaN||function(g){return g!==g};function s(){s.init.call(this)}Ni=s,Ni.once=m,s.EventEmitter=s,s.prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var o=10;function a(T){if(typeof T!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof T)}Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return o},set:function(T){if(typeof T!="number"||T<0||i(T))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+T+".");o=T}}),s.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(g){if(typeof g!="number"||g<0||i(g))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+g+".");return this._maxListeners=g,this};function c(T){return T._maxListeners===void 0?s.defaultMaxListeners:T._maxListeners}s.prototype.getMaxListeners=function(){return c(this)},s.prototype.emit=function(g){for(var O=[],_=1;_<arguments.length;_++)O.push(arguments[_]);var q=g==="error",$=this._events;if($!==void 0)q=q&&$.error===void 0;else if(!q)return!1;if(q){var j;if(O.length>0&&(j=O[0]),j instanceof Error)throw j;var J=new Error("Unhandled error."+(j?" ("+j.message+")":""));throw J.context=j,J}var W=$[g];if(W===void 0)return!1;if(typeof W=="function")e(W,this,O);else for(var H=W.length,G=P(W,H),_=0;_<H;++_)e(G[_],this,O);return!0};function f(T,g,O,_){var q,$,j;if(a(O),$=T._events,$===void 0?($=T._events=Object.create(null),T._eventsCount=0):($.newListener!==void 0&&(T.emit("newListener",g,O.listener?O.listener:O),$=T._events),j=$[g]),j===void 0)j=$[g]=O,++T._eventsCount;else if(typeof j=="function"?j=$[g]=_?[O,j]:[j,O]:_?j.unshift(O):j.push(O),q=c(T),q>0&&j.length>q&&!j.warned){j.warned=!0;var J=new Error("Possible EventEmitter memory leak detected. "+j.length+" "+String(g)+" listeners added. Use emitter.setMaxListeners() to increase limit");J.name="MaxListenersExceededWarning",J.emitter=T,J.type=g,J.count=j.length,n(J)}return T}s.prototype.addListener=function(g,O){return f(this,g,O,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(g,O){return f(this,g,O,!0)};function h(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function d(T,g,O){var _={fired:!1,wrapFn:void 0,target:T,type:g,listener:O},q=h.bind(_);return q.listener=O,_.wrapFn=q,q}s.prototype.once=function(g,O){return a(O),this.on(g,d(this,g,O)),this},s.prototype.prependOnceListener=function(g,O){return a(O),this.prependListener(g,d(this,g,O)),this},s.prototype.removeListener=function(g,O){var _,q,$,j,J;if(a(O),q=this._events,q===void 0)return this;if(_=q[g],_===void 0)return this;if(_===O||_.listener===O)--this._eventsCount===0?this._events=Object.create(null):(delete q[g],q.removeListener&&this.emit("removeListener",g,_.listener||O));else if(typeof _!="function"){for($=-1,j=_.length-1;j>=0;j--)if(_[j]===O||_[j].listener===O){J=_[j].listener,$=j;break}if($<0)return this;$===0?_.shift():E(_,$),_.length===1&&(q[g]=_[0]),q.removeListener!==void 0&&this.emit("removeListener",g,J||O)}return this},s.prototype.off=s.prototype.removeListener,s.prototype.removeAllListeners=function(g){var O,_,q;if(_=this._events,_===void 0)return this;if(_.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):_[g]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete _[g]),this;if(arguments.length===0){var $=Object.keys(_),j;for(q=0;q<$.length;++q)j=$[q],j!=="removeListener"&&this.removeAllListeners(j);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(O=_[g],typeof O=="function")this.removeListener(g,O);else if(O!==void 0)for(q=O.length-1;q>=0;q--)this.removeListener(g,O[q]);return this};function w(T,g,O){var _=T._events;if(_===void 0)return[];var q=_[g];return q===void 0?[]:typeof q=="function"?O?[q.listener||q]:[q]:O?b(q):P(q,q.length)}s.prototype.listeners=function(g){return w(this,g,!0)},s.prototype.rawListeners=function(g){return w(this,g,!1)},s.listenerCount=function(T,g){return typeof T.listenerCount=="function"?T.listenerCount(g):y.call(T,g)},s.prototype.listenerCount=y;function y(T){var g=this._events;if(g!==void 0){var O=g[T];if(typeof O=="function")return 1;if(O!==void 0)return O.length}return 0}s.prototype.eventNames=function(){return this._eventsCount>0?r(this._events):[]};function P(T,g){for(var O=new Array(g),_=0;_<g;++_)O[_]=T[_];return O}function E(T,g){for(;g+1<T.length;g++)T[g]=T[g+1];T.pop()}function b(T){for(var g=new Array(T.length),O=0;O<g.length;++O)g[O]=T[O].listener||T[O];return g}function m(T,g){return new Promise(function(O,_){function q(j){T.removeListener(g,$),_(j)}function $(){typeof T.removeListener=="function"&&T.removeListener("error",q),O([].slice.call(arguments))}R(T,g,$,{once:!0}),g!=="error"&&v(T,q,{once:!0})})}function v(T,g,O){typeof T.on=="function"&&R(T,"error",g,O)}function R(T,g,O,_){if(typeof T.on=="function")_.once?T.once(g,O):T.on(g,O);else if(typeof T.addEventListener=="function")T.addEventListener(g,function q($){_.once&&T.removeEventListener(g,q),O($)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof T)}return Ni}var Ni,Bf,Rn,gw,bw,ww,mw,_w,vw,er=ze(()=>{A();x();I();Ni={},Bf=!1;Rn=yw();Rn.once;Rn.once=function(t,e){return new Promise((r,n)=>{function i(...o){s!==void 0&&t.removeListener("error",s),r(o)}let s;e!=="error"&&(s=o=>{t.removeListener(name,i),n(o)},t.once("error",s)),t.once(e,i)})};Rn.on=function(t,e){let r=[],n=[],i=null,s=!1,o={async next(){let f=r.shift();if(f)return createIterResult(f,!1);if(i){let h=Promise.reject(i);return i=null,h}return s?createIterResult(void 0,!0):new Promise((h,d)=>n.push({resolve:h,reject:d}))},async return(){t.removeListener(e,a),t.removeListener("error",c),s=!0;for(let f of n)f.resolve(createIterResult(void 0,!0));return createIterResult(void 0,!0)},throw(f){i=f,t.removeListener(e,a),t.removeListener("error",c)},[Symbol.asyncIterator](){return this}};return t.on(e,a),t.on("error",c),o;function a(...f){let h=n.shift();h?h.resolve(createIterResult(f,!1)):r.push(f)}function c(f){s=!0;let h=n.shift();h?h.reject(f):i=f,o.return()}};({EventEmitter:gw,defaultMaxListeners:bw,init:ww,listenerCount:mw,on:_w,once:vw}=Rn)});var $e=D((OO,ys)=>{"use strict";A();x();I();var Sw=(ke(),me(qe)),{format:Ew,inspect:Aw}=hs(),{codes:{ERR_INVALID_ARG_TYPE:ps}}=De(),{kResistStopPropagation:Iw,AggregateError:xw,SymbolDispose:Tw}=Ee(),Pw=globalThis.AbortSignal||Yr().AbortSignal,Ow=globalThis.AbortController||Yr().AbortController,Rw=Object.getPrototypeOf(async function(){}).constructor,Mf=globalThis.Blob||Sw.Blob,kw=typeof Mf<"u"?function(e){return e instanceof Mf}:function(e){return!1},Nf=(t,e)=>{if(t!==void 0&&(t===null||typeof t!="object"||!("aborted"in t)))throw new ps(e,"AbortSignal",t)},Cw=(t,e)=>{if(typeof t!="function")throw new ps(e,"Function",t)};ys.exports={AggregateError:xw,kEmptyObject:Object.freeze({}),once(t){let e=!1;return function(...r){e||(e=!0,t.apply(this,r))}},createDeferredPromise:function(){let t,e;return{promise:new Promise((n,i)=>{t=n,e=i}),resolve:t,reject:e}},promisify(t){return new Promise((e,r)=>{t((n,...i)=>n?r(n):e(...i))})},debuglog(){return function(){}},format:Ew,inspect:Aw,types:{isAsyncFunction(t){return t instanceof Rw},isArrayBufferView(t){return ArrayBuffer.isView(t)}},isBlob:kw,deprecate(t,e){return t},addAbortListener:(er(),me(Zt)).addAbortListener||function(e,r){if(e===void 0)throw new ps("signal","AbortSignal",e);Nf(e,"signal"),Cw(r,"listener");let n;return e.aborted?queueMicrotask(()=>r()):(e.addEventListener("abort",r,{__proto__:null,once:!0,[Iw]:!0}),n=()=>{e.removeEventListener("abort",r)}),{__proto__:null,[Tw](){var i;(i=n)===null||i===void 0||i()}}},AbortSignalAny:Pw.any||function(e){if(e.length===1)return e[0];let r=new Ow,n=()=>r.abort();return e.forEach(i=>{Nf(i,"signals"),i.addEventListener("abort",n,{once:!0})}),r.signal.addEventListener("abort",()=>{e.forEach(i=>i.removeEventListener("abort",n))},{once:!0}),r.signal}};ys.exports.promisify.custom=Symbol.for("nodejs.util.promisify.custom")});var Xr=D((DO,Vf)=>{"use strict";A();x();I();var{ArrayIsArray:bs,ArrayPrototypeIncludes:Df,ArrayPrototypeJoin:jf,ArrayPrototypeMap:Bw,NumberIsInteger:ws,NumberIsNaN:Mw,NumberMAX_SAFE_INTEGER:Nw,NumberMIN_SAFE_INTEGER:qw,NumberParseInt:Lw,ObjectPrototypeHasOwnProperty:Uw,RegExpPrototypeExec:Ff,String:Dw,StringPrototypeToUpperCase:jw,StringPrototypeTrim:Fw}=Ee(),{hideStackFrames:et,codes:{ERR_SOCKET_BAD_PORT:Ww,ERR_INVALID_ARG_TYPE:je,ERR_INVALID_ARG_VALUE:Jr,ERR_OUT_OF_RANGE:vr,ERR_UNKNOWN_SIGNAL:qf}}=De(),{normalizeEncoding:$w}=$e(),{isAsyncFunction:Hw,isArrayBufferView:Vw}=$e().types,Lf={};function Gw(t){return t===(t|0)}function zw(t){return t===t>>>0}var Kw=/^[0-7]+$/,Qw="must be a 32-bit unsigned integer or an octal string";function Yw(t,e,r){if(typeof t>"u"&&(t=r),typeof t=="string"){if(Ff(Kw,t)===null)throw new Jr(e,t,Qw);t=Lw(t,8)}return Wf(t,e),t}var Jw=et((t,e,r=qw,n=Nw)=>{if(typeof t!="number")throw new je(e,"number",t);if(!ws(t))throw new vr(e,"an integer",t);if(t<r||t>n)throw new vr(e,`>= ${r} && <= ${n}`,t)}),Xw=et((t,e,r=-2147483648,n=**********)=>{if(typeof t!="number")throw new je(e,"number",t);if(!ws(t))throw new vr(e,"an integer",t);if(t<r||t>n)throw new vr(e,`>= ${r} && <= ${n}`,t)}),Wf=et((t,e,r=!1)=>{if(typeof t!="number")throw new je(e,"number",t);if(!ws(t))throw new vr(e,"an integer",t);let n=r?1:0,i=4294967295;if(t<n||t>i)throw new vr(e,`>= ${n} && <= ${i}`,t)});function ms(t,e){if(typeof t!="string")throw new je(e,"string",t)}function Zw(t,e,r=void 0,n){if(typeof t!="number")throw new je(e,"number",t);if(r!=null&&t<r||n!=null&&t>n||(r!=null||n!=null)&&Mw(t))throw new vr(e,`${r!=null?`>= ${r}`:""}${r!=null&&n!=null?" && ":""}${n!=null?`<= ${n}`:""}`,t)}var em=et((t,e,r)=>{if(!Df(r,t)){let i="must be one of: "+jf(Bw(r,s=>typeof s=="string"?`'${s}'`:Dw(s)),", ");throw new Jr(e,t,i)}});function $f(t,e){if(typeof t!="boolean")throw new je(e,"boolean",t)}function gs(t,e,r){return t==null||!Uw(t,e)?r:t[e]}var tm=et((t,e,r=null)=>{let n=gs(r,"allowArray",!1),i=gs(r,"allowFunction",!1);if(!gs(r,"nullable",!1)&&t===null||!n&&bs(t)||typeof t!="object"&&(!i||typeof t!="function"))throw new je(e,"Object",t)}),rm=et((t,e)=>{if(t!=null&&typeof t!="object"&&typeof t!="function")throw new je(e,"a dictionary",t)}),qi=et((t,e,r=0)=>{if(!bs(t))throw new je(e,"Array",t);if(t.length<r){let n=`must be longer than ${r}`;throw new Jr(e,t,n)}});function nm(t,e){qi(t,e);for(let r=0;r<t.length;r++)ms(t[r],`${e}[${r}]`)}function im(t,e){qi(t,e);for(let r=0;r<t.length;r++)$f(t[r],`${e}[${r}]`)}function om(t,e){qi(t,e);for(let r=0;r<t.length;r++){let n=t[r],i=`${e}[${r}]`;if(n==null)throw new je(i,"AbortSignal",n);Hf(n,i)}}function sm(t,e="signal"){if(ms(t,e),Lf[t]===void 0)throw Lf[jw(t)]!==void 0?new qf(t+" (signals must use all capital letters)"):new qf(t)}var am=et((t,e="buffer")=>{if(!Vw(t))throw new je(e,["Buffer","TypedArray","DataView"],t)});function um(t,e){let r=$w(e),n=t.length;if(r==="hex"&&n%2!==0)throw new Jr("encoding",e,`is invalid for data of length ${n}`)}function lm(t,e="Port",r=!0){if(typeof t!="number"&&typeof t!="string"||typeof t=="string"&&Fw(t).length===0||+t!==+t>>>0||t>65535||t===0&&!r)throw new Ww(e,t,r);return t|0}var Hf=et((t,e)=>{if(t!==void 0&&(t===null||typeof t!="object"||!("aborted"in t)))throw new je(e,"AbortSignal",t)}),cm=et((t,e)=>{if(typeof t!="function")throw new je(e,"Function",t)}),fm=et((t,e)=>{if(typeof t!="function"||Hw(t))throw new je(e,"Function",t)}),hm=et((t,e)=>{if(t!==void 0)throw new je(e,"undefined",t)});function dm(t,e,r){if(!Df(r,t))throw new je(e,`('${jf(r,"|")}')`,t)}var pm=/^(?:<[^>]*>)(?:\s*;\s*[^;"\s]+(?:=(")?[^;"\s]*\1)?)*$/;function Uf(t,e){if(typeof t>"u"||!Ff(pm,t))throw new Jr(e,t,'must be an array or string of format "</styles.css>; rel=preload; as=style"')}function ym(t){if(typeof t=="string")return Uf(t,"hints"),t;if(bs(t)){let e=t.length,r="";if(e===0)return r;for(let n=0;n<e;n++){let i=t[n];Uf(i,"hints"),r+=i,n!==e-1&&(r+=", ")}return r}throw new Jr("hints",t,'must be an array or string of format "</styles.css>; rel=preload; as=style"')}Vf.exports={isInt32:Gw,isUint32:zw,parseFileMode:Yw,validateArray:qi,validateStringArray:nm,validateBooleanArray:im,validateAbortSignalArray:om,validateBoolean:$f,validateBuffer:am,validateDictionary:rm,validateEncoding:um,validateFunction:cm,validateInt32:Xw,validateInteger:Jw,validateNumber:Zw,validateObject:tm,validateOneOf:em,validatePlainFunction:fm,validatePort:lm,validateSignalName:sm,validateString:ms,validateUint32:Wf,validateUndefined:hm,validateUnion:dm,validateAbortSignal:Hf,validateLinkHeaderValue:ym}});var tr=D((QO,Qf)=>{A();x();I();var Te=Qf.exports={},gt,bt;function _s(){throw new Error("setTimeout has not been defined")}function vs(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?gt=setTimeout:gt=_s}catch{gt=_s}try{typeof clearTimeout=="function"?bt=clearTimeout:bt=vs}catch{bt=vs}})();function Gf(t){if(gt===setTimeout)return setTimeout(t,0);if((gt===_s||!gt)&&setTimeout)return gt=setTimeout,setTimeout(t,0);try{return gt(t,0)}catch{try{return gt.call(null,t,0)}catch{return gt.call(this,t,0)}}}function gm(t){if(bt===clearTimeout)return clearTimeout(t);if((bt===vs||!bt)&&clearTimeout)return bt=clearTimeout,clearTimeout(t);try{return bt(t)}catch{try{return bt.call(null,t)}catch{return bt.call(this,t)}}}var qt=[],Zr=!1,Sr,Li=-1;function bm(){!Zr||!Sr||(Zr=!1,Sr.length?qt=Sr.concat(qt):Li=-1,qt.length&&zf())}function zf(){if(!Zr){var t=Gf(bm);Zr=!0;for(var e=qt.length;e;){for(Sr=qt,qt=[];++Li<e;)Sr&&Sr[Li].run();Li=-1,e=qt.length}Sr=null,Zr=!1,gm(t)}}Te.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];qt.push(new Kf(t,e)),qt.length===1&&!Zr&&Gf(zf)};function Kf(t,e){this.fun=t,this.array=e}Kf.prototype.run=function(){this.fun.apply(null,this.array)};Te.title="browser";Te.browser=!0;Te.env={};Te.argv=[];Te.version="";Te.versions={};function Lt(){}Te.on=Lt;Te.addListener=Lt;Te.once=Lt;Te.off=Lt;Te.removeListener=Lt;Te.removeAllListeners=Lt;Te.emit=Lt;Te.prependListener=Lt;Te.prependOnceListener=Lt;Te.listeners=function(t){return[]};Te.binding=function(t){throw new Error("process.binding is not supported")};Te.cwd=function(){return"/"};Te.chdir=function(t){throw new Error("process.chdir is not supported")};Te.umask=function(){return 0}});var mt=D((oR,ch)=>{"use strict";A();x();I();var{SymbolAsyncIterator:Yf,SymbolIterator:Jf,SymbolFor:Er}=Ee(),Xf=Er("nodejs.stream.destroyed"),Zf=Er("nodejs.stream.errored"),Ss=Er("nodejs.stream.readable"),Es=Er("nodejs.stream.writable"),eh=Er("nodejs.stream.disturbed"),wm=Er("nodejs.webstream.isClosedPromise"),mm=Er("nodejs.webstream.controllerErrorFunction");function Ui(t,e=!1){var r;return!!(t&&typeof t.pipe=="function"&&typeof t.on=="function"&&(!e||typeof t.pause=="function"&&typeof t.resume=="function")&&(!t._writableState||((r=t._readableState)===null||r===void 0?void 0:r.readable)!==!1)&&(!t._writableState||t._readableState))}function Di(t){var e;return!!(t&&typeof t.write=="function"&&typeof t.on=="function"&&(!t._readableState||((e=t._writableState)===null||e===void 0?void 0:e.writable)!==!1))}function _m(t){return!!(t&&typeof t.pipe=="function"&&t._readableState&&typeof t.on=="function"&&typeof t.write=="function")}function wt(t){return t&&(t._readableState||t._writableState||typeof t.write=="function"&&typeof t.on=="function"||typeof t.pipe=="function"&&typeof t.on=="function")}function th(t){return!!(t&&!wt(t)&&typeof t.pipeThrough=="function"&&typeof t.getReader=="function"&&typeof t.cancel=="function")}function rh(t){return!!(t&&!wt(t)&&typeof t.getWriter=="function"&&typeof t.abort=="function")}function nh(t){return!!(t&&!wt(t)&&typeof t.readable=="object"&&typeof t.writable=="object")}function vm(t){return th(t)||rh(t)||nh(t)}function Sm(t,e){return t==null?!1:e===!0?typeof t[Yf]=="function":e===!1?typeof t[Jf]=="function":typeof t[Yf]=="function"||typeof t[Jf]=="function"}function ji(t){if(!wt(t))return null;let e=t._writableState,r=t._readableState,n=e||r;return!!(t.destroyed||t[Xf]||n!=null&&n.destroyed)}function ih(t){if(!Di(t))return null;if(t.writableEnded===!0)return!0;let e=t._writableState;return e!=null&&e.errored?!1:typeof e?.ended!="boolean"?null:e.ended}function Em(t,e){if(!Di(t))return null;if(t.writableFinished===!0)return!0;let r=t._writableState;return r!=null&&r.errored?!1:typeof r?.finished!="boolean"?null:!!(r.finished||e===!1&&r.ended===!0&&r.length===0)}function Am(t){if(!Ui(t))return null;if(t.readableEnded===!0)return!0;let e=t._readableState;return!e||e.errored?!1:typeof e?.ended!="boolean"?null:e.ended}function oh(t,e){if(!Ui(t))return null;let r=t._readableState;return r!=null&&r.errored?!1:typeof r?.endEmitted!="boolean"?null:!!(r.endEmitted||e===!1&&r.ended===!0&&r.length===0)}function sh(t){return t&&t[Ss]!=null?t[Ss]:typeof t?.readable!="boolean"?null:ji(t)?!1:Ui(t)&&t.readable&&!oh(t)}function ah(t){return t&&t[Es]!=null?t[Es]:typeof t?.writable!="boolean"?null:ji(t)?!1:Di(t)&&t.writable&&!ih(t)}function Im(t,e){return wt(t)?ji(t)?!0:!(e?.readable!==!1&&sh(t)||e?.writable!==!1&&ah(t)):null}function xm(t){var e,r;return wt(t)?t.writableErrored?t.writableErrored:(e=(r=t._writableState)===null||r===void 0?void 0:r.errored)!==null&&e!==void 0?e:null:null}function Tm(t){var e,r;return wt(t)?t.readableErrored?t.readableErrored:(e=(r=t._readableState)===null||r===void 0?void 0:r.errored)!==null&&e!==void 0?e:null:null}function Pm(t){if(!wt(t))return null;if(typeof t.closed=="boolean")return t.closed;let e=t._writableState,r=t._readableState;return typeof e?.closed=="boolean"||typeof r?.closed=="boolean"?e?.closed||r?.closed:typeof t._closed=="boolean"&&uh(t)?t._closed:null}function uh(t){return typeof t._closed=="boolean"&&typeof t._defaultKeepAlive=="boolean"&&typeof t._removedConnection=="boolean"&&typeof t._removedContLen=="boolean"}function lh(t){return typeof t._sent100=="boolean"&&uh(t)}function Om(t){var e;return typeof t._consuming=="boolean"&&typeof t._dumped=="boolean"&&((e=t.req)===null||e===void 0?void 0:e.upgradeOrConnect)===void 0}function Rm(t){if(!wt(t))return null;let e=t._writableState,r=t._readableState,n=e||r;return!n&&lh(t)||!!(n&&n.autoDestroy&&n.emitClose&&n.closed===!1)}function km(t){var e;return!!(t&&((e=t[eh])!==null&&e!==void 0?e:t.readableDidRead||t.readableAborted))}function Cm(t){var e,r,n,i,s,o,a,c,f,h;return!!(t&&((e=(r=(n=(i=(s=(o=t[Zf])!==null&&o!==void 0?o:t.readableErrored)!==null&&s!==void 0?s:t.writableErrored)!==null&&i!==void 0?i:(a=t._readableState)===null||a===void 0?void 0:a.errorEmitted)!==null&&n!==void 0?n:(c=t._writableState)===null||c===void 0?void 0:c.errorEmitted)!==null&&r!==void 0?r:(f=t._readableState)===null||f===void 0?void 0:f.errored)!==null&&e!==void 0?e:!((h=t._writableState)===null||h===void 0)&&h.errored))}ch.exports={isDestroyed:ji,kIsDestroyed:Xf,isDisturbed:km,kIsDisturbed:eh,isErrored:Cm,kIsErrored:Zf,isReadable:sh,kIsReadable:Ss,kIsClosedPromise:wm,kControllerErrorFunction:mm,kIsWritable:Es,isClosed:Pm,isDuplexNodeStream:_m,isFinished:Im,isIterable:Sm,isReadableNodeStream:Ui,isReadableStream:th,isReadableEnded:Am,isReadableFinished:oh,isReadableErrored:Tm,isNodeStream:wt,isWebStream:vm,isWritable:ah,isWritableNodeStream:Di,isWritableStream:rh,isWritableEnded:ih,isWritableFinished:Em,isWritableErrored:xm,isServerRequest:Om,isServerResponse:lh,willEmitClose:Rm,isTransformStream:nh}});var Ut=D((yR,Ps)=>{"use strict";A();x();I();var rr=tr(),{AbortError:mh,codes:Bm}=De(),{ERR_INVALID_ARG_TYPE:Mm,ERR_STREAM_PREMATURE_CLOSE:fh}=Bm,{kEmptyObject:Is,once:xs}=$e(),{validateAbortSignal:Nm,validateFunction:qm,validateObject:Lm,validateBoolean:Um}=Xr(),{Promise:Dm,PromisePrototypeThen:jm,SymbolDispose:_h}=Ee(),{isClosed:Fm,isReadable:hh,isReadableNodeStream:As,isReadableStream:Wm,isReadableFinished:dh,isReadableErrored:ph,isWritable:yh,isWritableNodeStream:gh,isWritableStream:$m,isWritableFinished:bh,isWritableErrored:wh,isNodeStream:Hm,willEmitClose:Vm,kIsClosedPromise:Gm}=mt(),en;function zm(t){return t.setHeader&&typeof t.abort=="function"}var Ts=()=>{};function vh(t,e,r){var n,i;if(arguments.length===2?(r=e,e=Is):e==null?e=Is:Lm(e,"options"),qm(r,"callback"),Nm(e.signal,"options.signal"),r=xs(r),Wm(t)||$m(t))return Km(t,e,r);if(!Hm(t))throw new Mm("stream",["ReadableStream","WritableStream","Stream"],t);let s=(n=e.readable)!==null&&n!==void 0?n:As(t),o=(i=e.writable)!==null&&i!==void 0?i:gh(t),a=t._writableState,c=t._readableState,f=()=>{t.writable||w()},h=Vm(t)&&As(t)===s&&gh(t)===o,d=bh(t,!1),w=()=>{d=!0,t.destroyed&&(h=!1),!(h&&(!t.readable||s))&&(!s||y)&&r.call(t)},y=dh(t,!1),P=()=>{y=!0,t.destroyed&&(h=!1),!(h&&(!t.writable||o))&&(!o||d)&&r.call(t)},E=g=>{r.call(t,g)},b=Fm(t),m=()=>{b=!0;let g=wh(t)||ph(t);if(g&&typeof g!="boolean")return r.call(t,g);if(s&&!y&&As(t,!0)&&!dh(t,!1))return r.call(t,new fh);if(o&&!d&&!bh(t,!1))return r.call(t,new fh);r.call(t)},v=()=>{b=!0;let g=wh(t)||ph(t);if(g&&typeof g!="boolean")return r.call(t,g);r.call(t)},R=()=>{t.req.on("finish",w)};zm(t)?(t.on("complete",w),h||t.on("abort",m),t.req?R():t.on("request",R)):o&&!a&&(t.on("end",f),t.on("close",f)),!h&&typeof t.aborted=="boolean"&&t.on("aborted",m),t.on("end",P),t.on("finish",w),e.error!==!1&&t.on("error",E),t.on("close",m),b?rr.nextTick(m):a!=null&&a.errorEmitted||c!=null&&c.errorEmitted?h||rr.nextTick(v):(!s&&(!h||hh(t))&&(d||yh(t)===!1)||!o&&(!h||yh(t))&&(y||hh(t)===!1)||c&&t.req&&t.aborted)&&rr.nextTick(v);let T=()=>{r=Ts,t.removeListener("aborted",m),t.removeListener("complete",w),t.removeListener("abort",m),t.removeListener("request",R),t.req&&t.req.removeListener("finish",w),t.removeListener("end",f),t.removeListener("close",f),t.removeListener("finish",w),t.removeListener("end",P),t.removeListener("error",E),t.removeListener("close",m)};if(e.signal&&!b){let g=()=>{let O=r;T(),O.call(t,new mh(void 0,{cause:e.signal.reason}))};if(e.signal.aborted)rr.nextTick(g);else{en=en||$e().addAbortListener;let O=en(e.signal,g),_=r;r=xs((...q)=>{O[_h](),_.apply(t,q)})}}return T}function Km(t,e,r){let n=!1,i=Ts;if(e.signal)if(i=()=>{n=!0,r.call(t,new mh(void 0,{cause:e.signal.reason}))},e.signal.aborted)rr.nextTick(i);else{en=en||$e().addAbortListener;let o=en(e.signal,i),a=r;r=xs((...c)=>{o[_h](),a.apply(t,c)})}let s=(...o)=>{n||rr.nextTick(()=>r.apply(t,o))};return jm(t[Gm].promise,s,s),Ts}function Qm(t,e){var r;let n=!1;return e===null&&(e=Is),(r=e)!==null&&r!==void 0&&r.cleanup&&(Um(e.cleanup,"cleanup"),n=e.cleanup),new Dm((i,s)=>{let o=vh(t,e,a=>{n&&o(),a?s(a):i()})})}Ps.exports=vh;Ps.exports.finished=Qm});var Ar=D((IR,Oh)=>{"use strict";A();x();I();var _t=tr(),{aggregateTwoErrors:Ym,codes:{ERR_MULTIPLE_CALLBACK:Jm},AbortError:Xm}=De(),{Symbol:Ah}=Ee(),{kIsDestroyed:Zm,isDestroyed:e_,isFinished:t_,isServerRequest:r_}=mt(),Ih=Ah("kDestroy"),Os=Ah("kConstruct");function xh(t,e,r){t&&(t.stack,e&&!e.errored&&(e.errored=t),r&&!r.errored&&(r.errored=t))}function n_(t,e){let r=this._readableState,n=this._writableState,i=n||r;return n!=null&&n.destroyed||r!=null&&r.destroyed?(typeof e=="function"&&e(),this):(xh(t,n,r),n&&(n.destroyed=!0),r&&(r.destroyed=!0),i.constructed?Sh(this,t,e):this.once(Ih,function(s){Sh(this,Ym(s,t),e)}),this)}function Sh(t,e,r){let n=!1;function i(s){if(n)return;n=!0;let o=t._readableState,a=t._writableState;xh(s,a,o),a&&(a.closed=!0),o&&(o.closed=!0),typeof r=="function"&&r(s),s?_t.nextTick(i_,t,s):_t.nextTick(Th,t)}try{t._destroy(e||null,i)}catch(s){i(s)}}function i_(t,e){Rs(t,e),Th(t)}function Th(t){let e=t._readableState,r=t._writableState;r&&(r.closeEmitted=!0),e&&(e.closeEmitted=!0),(r!=null&&r.emitClose||e!=null&&e.emitClose)&&t.emit("close")}function Rs(t,e){let r=t._readableState,n=t._writableState;n!=null&&n.errorEmitted||r!=null&&r.errorEmitted||(n&&(n.errorEmitted=!0),r&&(r.errorEmitted=!0),t.emit("error",e))}function o_(){let t=this._readableState,e=this._writableState;t&&(t.constructed=!0,t.closed=!1,t.closeEmitted=!1,t.destroyed=!1,t.errored=null,t.errorEmitted=!1,t.reading=!1,t.ended=t.readable===!1,t.endEmitted=t.readable===!1),e&&(e.constructed=!0,e.destroyed=!1,e.closed=!1,e.closeEmitted=!1,e.errored=null,e.errorEmitted=!1,e.finalCalled=!1,e.prefinished=!1,e.ended=e.writable===!1,e.ending=e.writable===!1,e.finished=e.writable===!1)}function ks(t,e,r){let n=t._readableState,i=t._writableState;if(i!=null&&i.destroyed||n!=null&&n.destroyed)return this;n!=null&&n.autoDestroy||i!=null&&i.autoDestroy?t.destroy(e):e&&(e.stack,i&&!i.errored&&(i.errored=e),n&&!n.errored&&(n.errored=e),r?_t.nextTick(Rs,t,e):Rs(t,e))}function s_(t,e){if(typeof t._construct!="function")return;let r=t._readableState,n=t._writableState;r&&(r.constructed=!1),n&&(n.constructed=!1),t.once(Os,e),!(t.listenerCount(Os)>1)&&_t.nextTick(a_,t)}function a_(t){let e=!1;function r(n){if(e){ks(t,n??new Jm);return}e=!0;let i=t._readableState,s=t._writableState,o=s||i;i&&(i.constructed=!0),s&&(s.constructed=!0),o.destroyed?t.emit(Ih,n):n?ks(t,n,!0):_t.nextTick(u_,t)}try{t._construct(n=>{_t.nextTick(r,n)})}catch(n){_t.nextTick(r,n)}}function u_(t){t.emit(Os)}function Eh(t){return t?.setHeader&&typeof t.abort=="function"}function Ph(t){t.emit("close")}function l_(t,e){t.emit("error",e),_t.nextTick(Ph,t)}function c_(t,e){!t||e_(t)||(!e&&!t_(t)&&(e=new Xm),r_(t)?(t.socket=null,t.destroy(e)):Eh(t)?t.abort():Eh(t.req)?t.req.abort():typeof t.destroy=="function"?t.destroy(e):typeof t.close=="function"?t.close():e?_t.nextTick(l_,t,e):_t.nextTick(Ph,t),t.destroyed||(t[Zm]=!0))}Oh.exports={construct:s_,destroyer:c_,destroy:n_,undestroy:o_,errorOrDestroy:ks}});var $i=D((NR,kh)=>{"use strict";A();x();I();var{ArrayIsArray:f_,ObjectSetPrototypeOf:Rh}=Ee(),{EventEmitter:Fi}=(er(),me(Zt));function Wi(t){Fi.call(this,t)}Rh(Wi.prototype,Fi.prototype);Rh(Wi,Fi);Wi.prototype.pipe=function(t,e){let r=this;function n(h){t.writable&&t.write(h)===!1&&r.pause&&r.pause()}r.on("data",n);function i(){r.readable&&r.resume&&r.resume()}t.on("drain",i),!t._isStdio&&(!e||e.end!==!1)&&(r.on("end",o),r.on("close",a));let s=!1;function o(){s||(s=!0,t.end())}function a(){s||(s=!0,typeof t.destroy=="function"&&t.destroy())}function c(h){f(),Fi.listenerCount(this,"error")===0&&this.emit("error",h)}Cs(r,"error",c),Cs(t,"error",c);function f(){r.removeListener("data",n),t.removeListener("drain",i),r.removeListener("end",o),r.removeListener("close",a),r.removeListener("error",c),t.removeListener("error",c),r.removeListener("end",f),r.removeListener("close",f),t.removeListener("close",f)}return r.on("end",f),r.on("close",f),t.on("close",f),t.emit("pipe",r),t};function Cs(t,e,r){if(typeof t.prependListener=="function")return t.prependListener(e,r);!t._events||!t._events[e]?t.on(e,r):f_(t._events[e])?t._events[e].unshift(r):t._events[e]=[r,t._events[e]]}kh.exports={Stream:Wi,prependListener:Cs}});var kn=D((VR,Hi)=>{"use strict";A();x();I();var{SymbolDispose:h_}=Ee(),{AbortError:Ch,codes:d_}=De(),{isNodeStream:Bh,isWebStream:p_,kControllerErrorFunction:y_}=mt(),g_=Ut(),{ERR_INVALID_ARG_TYPE:Mh}=d_,Bs,b_=(t,e)=>{if(typeof t!="object"||!("aborted"in t))throw new Mh(e,"AbortSignal",t)};Hi.exports.addAbortSignal=function(e,r){if(b_(e,"signal"),!Bh(r)&&!p_(r))throw new Mh("stream",["ReadableStream","WritableStream","Stream"],r);return Hi.exports.addAbortSignalNoValidate(e,r)};Hi.exports.addAbortSignalNoValidate=function(t,e){if(typeof t!="object"||!("aborted"in t))return e;let r=Bh(e)?()=>{e.destroy(new Ch(void 0,{cause:t.reason}))}:()=>{e[y_](new Ch(void 0,{cause:t.reason}))};if(t.aborted)r();else{Bs=Bs||$e().addAbortListener;let n=Bs(t,r);g_(e,n[h_])}return e}});var Lh=D((rk,qh)=>{"use strict";A();x();I();var{StringPrototypeSlice:Nh,SymbolIterator:w_,TypedArrayPrototypeSet:Vi,Uint8Array:m_}=Ee(),{Buffer:Ms}=(ke(),me(qe)),{inspect:__}=$e();qh.exports=class{constructor(){this.head=null,this.tail=null,this.length=0}push(e){let r={data:e,next:null};this.length>0?this.tail.next=r:this.head=r,this.tail=r,++this.length}unshift(e){let r={data:e,next:this.head};this.length===0&&(this.tail=r),this.head=r,++this.length}shift(){if(this.length===0)return;let e=this.head.data;return this.length===1?this.head=this.tail=null:this.head=this.head.next,--this.length,e}clear(){this.head=this.tail=null,this.length=0}join(e){if(this.length===0)return"";let r=this.head,n=""+r.data;for(;(r=r.next)!==null;)n+=e+r.data;return n}concat(e){if(this.length===0)return Ms.alloc(0);let r=Ms.allocUnsafe(e>>>0),n=this.head,i=0;for(;n;)Vi(r,n.data,i),i+=n.data.length,n=n.next;return r}consume(e,r){let n=this.head.data;if(e<n.length){let i=n.slice(0,e);return this.head.data=n.slice(e),i}return e===n.length?this.shift():r?this._getString(e):this._getBuffer(e)}first(){return this.head.data}*[w_](){for(let e=this.head;e;e=e.next)yield e.data}_getString(e){let r="",n=this.head,i=0;do{let s=n.data;if(e>s.length)r+=s,e-=s.length;else{e===s.length?(r+=s,++i,n.next?this.head=n.next:this.head=this.tail=null):(r+=Nh(s,0,e),this.head=n,n.data=Nh(s,e));break}++i}while((n=n.next)!==null);return this.length-=i,r}_getBuffer(e){let r=Ms.allocUnsafe(e),n=e,i=this.head,s=0;do{let o=i.data;if(e>o.length)Vi(r,o,n-e),e-=o.length;else{e===o.length?(Vi(r,o,n-e),++s,i.next?this.head=i.next:this.head=this.tail=null):(Vi(r,new m_(o.buffer,o.byteOffset,e),n-e),this.head=i,i.data=o.slice(e));break}++s}while((i=i.next)!==null);return this.length-=s,r}[Symbol.for("nodejs.util.inspect.custom")](e,r){return __(this,{...r,depth:0,customInspect:!1})}}});var Cn=D((hk,Fh)=>{"use strict";A();x();I();var{MathFloor:v_,NumberIsInteger:S_}=Ee(),{validateInteger:E_}=Xr(),{ERR_INVALID_ARG_VALUE:A_}=De().codes,Uh=16*1024,Dh=16;function I_(t,e,r){return t.highWaterMark!=null?t.highWaterMark:e?t[r]:null}function jh(t){return t?Dh:Uh}function x_(t,e){E_(e,"value",0),t?Dh=e:Uh=e}function T_(t,e,r,n){let i=I_(e,n,r);if(i!=null){if(!S_(i)||i<0){let s=n?`options.${r}`:"options.highWaterMark";throw new A_(s,i)}return v_(i)}return jh(t.objectMode)}Fh.exports={getHighWaterMark:T_,getDefaultHighWaterMark:jh,setDefaultHighWaterMark:x_}});var Hh=D((Ns,$h)=>{A();x();I();var Gi=(ke(),me(qe)),vt=Gi.Buffer;function Wh(t,e){for(var r in t)e[r]=t[r]}vt.from&&vt.alloc&&vt.allocUnsafe&&vt.allocUnsafeSlow?$h.exports=Gi:(Wh(Gi,Ns),Ns.Buffer=Ir);function Ir(t,e,r){return vt(t,e,r)}Ir.prototype=Object.create(vt.prototype);Wh(vt,Ir);Ir.from=function(t,e,r){if(typeof t=="number")throw new TypeError("Argument must not be a number");return vt(t,e,r)};Ir.alloc=function(t,e,r){if(typeof t!="number")throw new TypeError("Argument must be a number");var n=vt(t);return e!==void 0?typeof r=="string"?n.fill(e,r):n.fill(e):n.fill(0),n};Ir.allocUnsafe=function(t){if(typeof t!="number")throw new TypeError("Argument must be a number");return vt(t)};Ir.allocUnsafeSlow=function(t){if(typeof t!="number")throw new TypeError("Argument must be a number");return Gi.SlowBuffer(t)}});var zh=D(Gh=>{"use strict";A();x();I();var Ls=Hh().Buffer,Vh=Ls.isEncoding||function(t){switch(t=""+t,t&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function P_(t){if(!t)return"utf8";for(var e;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}function O_(t){var e=P_(t);if(typeof e!="string"&&(Ls.isEncoding===Vh||!Vh(t)))throw new Error("Unknown encoding: "+t);return e||t}Gh.StringDecoder=Bn;function Bn(t){this.encoding=O_(t);var e;switch(this.encoding){case"utf16le":this.text=N_,this.end=q_,e=4;break;case"utf8":this.fillLast=C_,e=4;break;case"base64":this.text=L_,this.end=U_,e=3;break;default:this.write=D_,this.end=j_;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=Ls.allocUnsafe(e)}Bn.prototype.write=function(t){if(t.length===0)return"";var e,r;if(this.lastNeed){if(e=this.fillLast(t),e===void 0)return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<t.length?e?e+this.text(t,r):this.text(t,r):e||""};Bn.prototype.end=M_;Bn.prototype.text=B_;Bn.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length};function qs(t){return t<=127?0:t>>5===6?2:t>>4===14?3:t>>3===30?4:t>>6===2?-1:-2}function R_(t,e,r){var n=e.length-1;if(n<r)return 0;var i=qs(e[n]);return i>=0?(i>0&&(t.lastNeed=i-1),i):--n<r||i===-2?0:(i=qs(e[n]),i>=0?(i>0&&(t.lastNeed=i-2),i):--n<r||i===-2?0:(i=qs(e[n]),i>=0?(i>0&&(i===2?i=0:t.lastNeed=i-3),i):0))}function k_(t,e,r){if((e[0]&192)!==128)return t.lastNeed=0,"\uFFFD";if(t.lastNeed>1&&e.length>1){if((e[1]&192)!==128)return t.lastNeed=1,"\uFFFD";if(t.lastNeed>2&&e.length>2&&(e[2]&192)!==128)return t.lastNeed=2,"\uFFFD"}}function C_(t){var e=this.lastTotal-this.lastNeed,r=k_(this,t,e);if(r!==void 0)return r;if(this.lastNeed<=t.length)return t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,e,0,t.length),this.lastNeed-=t.length}function B_(t,e){var r=R_(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=r;var n=t.length-(r-this.lastNeed);return t.copy(this.lastChar,0,n),t.toString("utf8",e,n)}function M_(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"\uFFFD":e}function N_(t,e){if((t.length-e)%2===0){var r=t.toString("utf16le",e);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function q_(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,r)}return e}function L_(t,e){var r=(t.length-e)%3;return r===0?t.toString("base64",e):(this.lastNeed=3-r,this.lastTotal=3,r===1?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-r))}function U_(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function D_(t){return t.toString(this.encoding)}function j_(t){return t&&t.length?this.write(t):""}});var Us=D((Fk,Jh)=>{"use strict";A();x();I();var Kh=tr(),{PromisePrototypeThen:F_,SymbolAsyncIterator:Qh,SymbolIterator:Yh}=Ee(),{Buffer:W_}=(ke(),me(qe)),{ERR_INVALID_ARG_TYPE:$_,ERR_STREAM_NULL_VALUES:H_}=De().codes;function V_(t,e,r){let n;if(typeof e=="string"||e instanceof W_)return new t({objectMode:!0,...r,read(){this.push(e),this.push(null)}});let i;if(e&&e[Qh])i=!0,n=e[Qh]();else if(e&&e[Yh])i=!1,n=e[Yh]();else throw new $_("iterable",["Iterable"],e);let s=new t({objectMode:!0,highWaterMark:1,...r}),o=!1;s._read=function(){o||(o=!0,c())},s._destroy=function(f,h){F_(a(f),()=>Kh.nextTick(h,f),d=>Kh.nextTick(h,d||f))};async function a(f){let h=f!=null,d=typeof n.throw=="function";if(h&&d){let{value:w,done:y}=await n.throw(f);if(await w,y)return}if(typeof n.return=="function"){let{value:w}=await n.return();await w}}async function c(){for(;;){try{let{value:f,done:h}=i?await n.next():n.next();if(h)s.push(null);else{let d=f&&typeof f.then=="function"?await f:f;if(d===null)throw o=!1,new H_;if(s.push(d))continue;o=!1}}catch(f){s.destroy(f)}break}}return s}Jh.exports=V_});var Nn=D((Jk,yd)=>{"use strict";A();x();I();var lt=tr(),{ArrayPrototypeIndexOf:G_,NumberIsInteger:z_,NumberIsNaN:K_,NumberParseInt:Q_,ObjectDefineProperties:Gs,ObjectKeys:Y_,ObjectSetPrototypeOf:ed,Promise:td,SafeSet:J_,SymbolAsyncDispose:X_,SymbolAsyncIterator:Z_,Symbol:ev}=Ee();yd.exports=le;le.ReadableState=Yi;var{EventEmitter:tv}=(er(),me(Zt)),{Stream:nr,prependListener:rv}=$i(),{Buffer:Ds}=(ke(),me(qe)),{addAbortSignal:nv}=kn(),rd=Ut(),he=$e().debuglog("stream",t=>{he=t}),iv=Lh(),nn=Ar(),{getHighWaterMark:ov,getDefaultHighWaterMark:sv}=Cn(),{aggregateTwoErrors:Xh,codes:{ERR_INVALID_ARG_TYPE:av,ERR_METHOD_NOT_IMPLEMENTED:uv,ERR_OUT_OF_RANGE:lv,ERR_STREAM_PUSH_AFTER_EOF:cv,ERR_STREAM_UNSHIFT_AFTER_END_EVENT:fv},AbortError:hv}=De(),{validateObject:dv}=Xr(),xr=ev("kPaused"),{StringDecoder:nd}=zh(),pv=Us();ed(le.prototype,nr.prototype);ed(le,nr);var js=()=>{},{errorOrDestroy:tn}=nn,rn=1,yv=2,id=4,Mn=8,od=16,zi=32,Ki=64,sd=128,gv=256,bv=512,wv=1024,Hs=2048,Vs=4096,mv=8192,_v=16384,vv=32768,ad=65536,Sv=1<<17,Ev=1<<18;function Ce(t){return{enumerable:!1,get(){return(this.state&t)!==0},set(e){e?this.state|=t:this.state&=~t}}}Gs(Yi.prototype,{objectMode:Ce(rn),ended:Ce(yv),endEmitted:Ce(id),reading:Ce(Mn),constructed:Ce(od),sync:Ce(zi),needReadable:Ce(Ki),emittedReadable:Ce(sd),readableListening:Ce(gv),resumeScheduled:Ce(bv),errorEmitted:Ce(wv),emitClose:Ce(Hs),autoDestroy:Ce(Vs),destroyed:Ce(mv),closed:Ce(_v),closeEmitted:Ce(vv),multiAwaitDrain:Ce(ad),readingMore:Ce(Sv),dataEmitted:Ce(Ev)});function Yi(t,e,r){typeof r!="boolean"&&(r=e instanceof St()),this.state=Hs|Vs|od|zi,t&&t.objectMode&&(this.state|=rn),r&&t&&t.readableObjectMode&&(this.state|=rn),this.highWaterMark=t?ov(this,t,"readableHighWaterMark",r):sv(!1),this.buffer=new iv,this.length=0,this.pipes=[],this.flowing=null,this[xr]=null,t&&t.emitClose===!1&&(this.state&=~Hs),t&&t.autoDestroy===!1&&(this.state&=~Vs),this.errored=null,this.defaultEncoding=t&&t.defaultEncoding||"utf8",this.awaitDrainWriters=null,this.decoder=null,this.encoding=null,t&&t.encoding&&(this.decoder=new nd(t.encoding),this.encoding=t.encoding)}function le(t){if(!(this instanceof le))return new le(t);let e=this instanceof St();this._readableState=new Yi(t,this,e),t&&(typeof t.read=="function"&&(this._read=t.read),typeof t.destroy=="function"&&(this._destroy=t.destroy),typeof t.construct=="function"&&(this._construct=t.construct),t.signal&&!e&&nv(t.signal,this)),nr.call(this,t),nn.construct(this,()=>{this._readableState.needReadable&&Qi(this,this._readableState)})}le.prototype.destroy=nn.destroy;le.prototype._undestroy=nn.undestroy;le.prototype._destroy=function(t,e){e(t)};le.prototype[tv.captureRejectionSymbol]=function(t){this.destroy(t)};le.prototype[X_]=function(){let t;return this.destroyed||(t=this.readableEnded?null:new hv,this.destroy(t)),new td((e,r)=>rd(this,n=>n&&n!==t?r(n):e(null)))};le.prototype.push=function(t,e){return ud(this,t,e,!1)};le.prototype.unshift=function(t,e){return ud(this,t,e,!0)};function ud(t,e,r,n){he("readableAddChunk",e);let i=t._readableState,s;if((i.state&rn)===0&&(typeof e=="string"?(r=r||i.defaultEncoding,i.encoding!==r&&(n&&i.encoding?e=Ds.from(e,r).toString(i.encoding):(e=Ds.from(e,r),r=""))):e instanceof Ds?r="":nr._isUint8Array(e)?(e=nr._uint8ArrayToBuffer(e),r=""):e!=null&&(s=new av("chunk",["string","Buffer","Uint8Array"],e))),s)tn(t,s);else if(e===null)i.state&=~Mn,xv(t,i);else if((i.state&rn)!==0||e&&e.length>0)if(n)if((i.state&id)!==0)tn(t,new fv);else{if(i.destroyed||i.errored)return!1;Fs(t,i,e,!0)}else if(i.ended)tn(t,new cv);else{if(i.destroyed||i.errored)return!1;i.state&=~Mn,i.decoder&&!r?(e=i.decoder.write(e),i.objectMode||e.length!==0?Fs(t,i,e,!1):Qi(t,i)):Fs(t,i,e,!1)}else n||(i.state&=~Mn,Qi(t,i));return!i.ended&&(i.length<i.highWaterMark||i.length===0)}function Fs(t,e,r,n){e.flowing&&e.length===0&&!e.sync&&t.listenerCount("data")>0?((e.state&ad)!==0?e.awaitDrainWriters.clear():e.awaitDrainWriters=null,e.dataEmitted=!0,t.emit("data",r)):(e.length+=e.objectMode?1:r.length,n?e.buffer.unshift(r):e.buffer.push(r),(e.state&Ki)!==0&&Ji(t)),Qi(t,e)}le.prototype.isPaused=function(){let t=this._readableState;return t[xr]===!0||t.flowing===!1};le.prototype.setEncoding=function(t){let e=new nd(t);this._readableState.decoder=e,this._readableState.encoding=this._readableState.decoder.encoding;let r=this._readableState.buffer,n="";for(let i of r)n+=e.write(i);return r.clear(),n!==""&&r.push(n),this._readableState.length=n.length,this};var Av=1073741824;function Iv(t){if(t>Av)throw new lv("size","<= 1GiB",t);return t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++,t}function Zh(t,e){return t<=0||e.length===0&&e.ended?0:(e.state&rn)!==0?1:K_(t)?e.flowing&&e.length?e.buffer.first().length:e.length:t<=e.length?t:e.ended?e.length:0}le.prototype.read=function(t){he("read",t),t===void 0?t=NaN:z_(t)||(t=Q_(t,10));let e=this._readableState,r=t;if(t>e.highWaterMark&&(e.highWaterMark=Iv(t)),t!==0&&(e.state&=~sd),t===0&&e.needReadable&&((e.highWaterMark!==0?e.length>=e.highWaterMark:e.length>0)||e.ended))return he("read: emitReadable",e.length,e.ended),e.length===0&&e.ended?Ws(this):Ji(this),null;if(t=Zh(t,e),t===0&&e.ended)return e.length===0&&Ws(this),null;let n=(e.state&Ki)!==0;if(he("need readable",n),(e.length===0||e.length-t<e.highWaterMark)&&(n=!0,he("length less than watermark",n)),e.ended||e.reading||e.destroyed||e.errored||!e.constructed)n=!1,he("reading, ended or constructing",n);else if(n){he("do read"),e.state|=Mn|zi,e.length===0&&(e.state|=Ki);try{this._read(e.highWaterMark)}catch(s){tn(this,s)}e.state&=~zi,e.reading||(t=Zh(r,e))}let i;return t>0?i=dd(t,e):i=null,i===null?(e.needReadable=e.length<=e.highWaterMark,t=0):(e.length-=t,e.multiAwaitDrain?e.awaitDrainWriters.clear():e.awaitDrainWriters=null),e.length===0&&(e.ended||(e.needReadable=!0),r!==t&&e.ended&&Ws(this)),i!==null&&!e.errorEmitted&&!e.closeEmitted&&(e.dataEmitted=!0,this.emit("data",i)),i};function xv(t,e){if(he("onEofChunk"),!e.ended){if(e.decoder){let r=e.decoder.end();r&&r.length&&(e.buffer.push(r),e.length+=e.objectMode?1:r.length)}e.ended=!0,e.sync?Ji(t):(e.needReadable=!1,e.emittedReadable=!0,ld(t))}}function Ji(t){let e=t._readableState;he("emitReadable",e.needReadable,e.emittedReadable),e.needReadable=!1,e.emittedReadable||(he("emitReadable",e.flowing),e.emittedReadable=!0,lt.nextTick(ld,t))}function ld(t){let e=t._readableState;he("emitReadable_",e.destroyed,e.length,e.ended),!e.destroyed&&!e.errored&&(e.length||e.ended)&&(t.emit("readable"),e.emittedReadable=!1),e.needReadable=!e.flowing&&!e.ended&&e.length<=e.highWaterMark,fd(t)}function Qi(t,e){!e.readingMore&&e.constructed&&(e.readingMore=!0,lt.nextTick(Tv,t,e))}function Tv(t,e){for(;!e.reading&&!e.ended&&(e.length<e.highWaterMark||e.flowing&&e.length===0);){let r=e.length;if(he("maybeReadMore read 0"),t.read(0),r===e.length)break}e.readingMore=!1}le.prototype._read=function(t){throw new uv("_read()")};le.prototype.pipe=function(t,e){let r=this,n=this._readableState;n.pipes.length===1&&(n.multiAwaitDrain||(n.multiAwaitDrain=!0,n.awaitDrainWriters=new J_(n.awaitDrainWriters?[n.awaitDrainWriters]:[]))),n.pipes.push(t),he("pipe count=%d opts=%j",n.pipes.length,e);let s=(!e||e.end!==!1)&&t!==lt.stdout&&t!==lt.stderr?a:b;n.endEmitted?lt.nextTick(s):r.once("end",s),t.on("unpipe",o);function o(m,v){he("onunpipe"),m===r&&v&&v.hasUnpiped===!1&&(v.hasUnpiped=!0,h())}function a(){he("onend"),t.end()}let c,f=!1;function h(){he("cleanup"),t.removeListener("close",P),t.removeListener("finish",E),c&&t.removeListener("drain",c),t.removeListener("error",y),t.removeListener("unpipe",o),r.removeListener("end",a),r.removeListener("end",b),r.removeListener("data",w),f=!0,c&&n.awaitDrainWriters&&(!t._writableState||t._writableState.needDrain)&&c()}function d(){f||(n.pipes.length===1&&n.pipes[0]===t?(he("false write response, pause",0),n.awaitDrainWriters=t,n.multiAwaitDrain=!1):n.pipes.length>1&&n.pipes.includes(t)&&(he("false write response, pause",n.awaitDrainWriters.size),n.awaitDrainWriters.add(t)),r.pause()),c||(c=Pv(r,t),t.on("drain",c))}r.on("data",w);function w(m){he("ondata");let v=t.write(m);he("dest.write",v),v===!1&&d()}function y(m){if(he("onerror",m),b(),t.removeListener("error",y),t.listenerCount("error")===0){let v=t._writableState||t._readableState;v&&!v.errorEmitted?tn(t,m):t.emit("error",m)}}rv(t,"error",y);function P(){t.removeListener("finish",E),b()}t.once("close",P);function E(){he("onfinish"),t.removeListener("close",P),b()}t.once("finish",E);function b(){he("unpipe"),r.unpipe(t)}return t.emit("pipe",r),t.writableNeedDrain===!0?d():n.flowing||(he("pipe resume"),r.resume()),t};function Pv(t,e){return function(){let n=t._readableState;n.awaitDrainWriters===e?(he("pipeOnDrain",1),n.awaitDrainWriters=null):n.multiAwaitDrain&&(he("pipeOnDrain",n.awaitDrainWriters.size),n.awaitDrainWriters.delete(e)),(!n.awaitDrainWriters||n.awaitDrainWriters.size===0)&&t.listenerCount("data")&&t.resume()}}le.prototype.unpipe=function(t){let e=this._readableState,r={hasUnpiped:!1};if(e.pipes.length===0)return this;if(!t){let i=e.pipes;e.pipes=[],this.pause();for(let s=0;s<i.length;s++)i[s].emit("unpipe",this,{hasUnpiped:!1});return this}let n=G_(e.pipes,t);return n===-1?this:(e.pipes.splice(n,1),e.pipes.length===0&&this.pause(),t.emit("unpipe",this,r),this)};le.prototype.on=function(t,e){let r=nr.prototype.on.call(this,t,e),n=this._readableState;return t==="data"?(n.readableListening=this.listenerCount("readable")>0,n.flowing!==!1&&this.resume()):t==="readable"&&!n.endEmitted&&!n.readableListening&&(n.readableListening=n.needReadable=!0,n.flowing=!1,n.emittedReadable=!1,he("on readable",n.length,n.reading),n.length?Ji(this):n.reading||lt.nextTick(Ov,this)),r};le.prototype.addListener=le.prototype.on;le.prototype.removeListener=function(t,e){let r=nr.prototype.removeListener.call(this,t,e);return t==="readable"&&lt.nextTick(cd,this),r};le.prototype.off=le.prototype.removeListener;le.prototype.removeAllListeners=function(t){let e=nr.prototype.removeAllListeners.apply(this,arguments);return(t==="readable"||t===void 0)&&lt.nextTick(cd,this),e};function cd(t){let e=t._readableState;e.readableListening=t.listenerCount("readable")>0,e.resumeScheduled&&e[xr]===!1?e.flowing=!0:t.listenerCount("data")>0?t.resume():e.readableListening||(e.flowing=null)}function Ov(t){he("readable nexttick read 0"),t.read(0)}le.prototype.resume=function(){let t=this._readableState;return t.flowing||(he("resume"),t.flowing=!t.readableListening,Rv(this,t)),t[xr]=!1,this};function Rv(t,e){e.resumeScheduled||(e.resumeScheduled=!0,lt.nextTick(kv,t,e))}function kv(t,e){he("resume",e.reading),e.reading||t.read(0),e.resumeScheduled=!1,t.emit("resume"),fd(t),e.flowing&&!e.reading&&t.read(0)}le.prototype.pause=function(){return he("call pause flowing=%j",this._readableState.flowing),this._readableState.flowing!==!1&&(he("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState[xr]=!0,this};function fd(t){let e=t._readableState;for(he("flow",e.flowing);e.flowing&&t.read()!==null;);}le.prototype.wrap=function(t){let e=!1;t.on("data",n=>{!this.push(n)&&t.pause&&(e=!0,t.pause())}),t.on("end",()=>{this.push(null)}),t.on("error",n=>{tn(this,n)}),t.on("close",()=>{this.destroy()}),t.on("destroy",()=>{this.destroy()}),this._read=()=>{e&&t.resume&&(e=!1,t.resume())};let r=Y_(t);for(let n=1;n<r.length;n++){let i=r[n];this[i]===void 0&&typeof t[i]=="function"&&(this[i]=t[i].bind(t))}return this};le.prototype[Z_]=function(){return hd(this)};le.prototype.iterator=function(t){return t!==void 0&&dv(t,"options"),hd(this,t)};function hd(t,e){typeof t.read!="function"&&(t=le.wrap(t,{objectMode:!0}));let r=Cv(t,e);return r.stream=t,r}async function*Cv(t,e){let r=js;function n(o){this===t?(r(),r=js):r=o}t.on("readable",n);let i,s=rd(t,{writable:!1},o=>{i=o?Xh(i,o):null,r(),r=js});try{for(;;){let o=t.destroyed?null:t.read();if(o!==null)yield o;else{if(i)throw i;if(i===null)return;await new td(n)}}}catch(o){throw i=Xh(i,o),i}finally{(i||e?.destroyOnReturn!==!1)&&(i===void 0||t._readableState.autoDestroy)?nn.destroyer(t,null):(t.off("readable",n),s())}}Gs(le.prototype,{readable:{__proto__:null,get(){let t=this._readableState;return!!t&&t.readable!==!1&&!t.destroyed&&!t.errorEmitted&&!t.endEmitted},set(t){this._readableState&&(this._readableState.readable=!!t)}},readableDidRead:{__proto__:null,enumerable:!1,get:function(){return this._readableState.dataEmitted}},readableAborted:{__proto__:null,enumerable:!1,get:function(){return!!(this._readableState.readable!==!1&&(this._readableState.destroyed||this._readableState.errored)&&!this._readableState.endEmitted)}},readableHighWaterMark:{__proto__:null,enumerable:!1,get:function(){return this._readableState.highWaterMark}},readableBuffer:{__proto__:null,enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}},readableFlowing:{__proto__:null,enumerable:!1,get:function(){return this._readableState.flowing},set:function(t){this._readableState&&(this._readableState.flowing=t)}},readableLength:{__proto__:null,enumerable:!1,get(){return this._readableState.length}},readableObjectMode:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.objectMode:!1}},readableEncoding:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.encoding:null}},errored:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.errored:null}},closed:{__proto__:null,get(){return this._readableState?this._readableState.closed:!1}},destroyed:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.destroyed:!1},set(t){this._readableState&&(this._readableState.destroyed=t)}},readableEnded:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.endEmitted:!1}}});Gs(Yi.prototype,{pipesCount:{__proto__:null,get(){return this.pipes.length}},paused:{__proto__:null,get(){return this[xr]!==!1},set(t){this[xr]=!!t}}});le._fromList=dd;function dd(t,e){if(e.length===0)return null;let r;return e.objectMode?r=e.buffer.shift():!t||t>=e.length?(e.decoder?r=e.buffer.join(""):e.buffer.length===1?r=e.buffer.first():r=e.buffer.concat(e.length),e.buffer.clear()):r=e.buffer.consume(t,e.decoder),r}function Ws(t){let e=t._readableState;he("endReadable",e.endEmitted),e.endEmitted||(e.ended=!0,lt.nextTick(Bv,e,t))}function Bv(t,e){if(he("endReadableNT",t.endEmitted,t.length),!t.errored&&!t.closeEmitted&&!t.endEmitted&&t.length===0){if(t.endEmitted=!0,e.emit("end"),e.writable&&e.allowHalfOpen===!1)lt.nextTick(Mv,e);else if(t.autoDestroy){let r=e._writableState;(!r||r.autoDestroy&&(r.finished||r.writable===!1))&&e.destroy()}}}function Mv(t){t.writable&&!t.writableEnded&&!t.destroyed&&t.end()}le.from=function(t,e){return pv(le,t,e)};var $s;function pd(){return $s===void 0&&($s={}),$s}le.fromWeb=function(t,e){return pd().newStreamReadableFromReadableStream(t,e)};le.toWeb=function(t,e){return pd().newReadableStreamFromStreamReadable(t,e)};le.wrap=function(t,e){var r,n;return new le({objectMode:(r=(n=t.readableObjectMode)!==null&&n!==void 0?n:t.objectMode)!==null&&r!==void 0?r:!0,...e,destroy(i,s){nn.destroyer(t,i),s(i)}}).wrap(t)}});var ro=D((aC,Td)=>{"use strict";A();x();I();var Tr=tr(),{ArrayPrototypeSlice:wd,Error:Nv,FunctionPrototypeSymbolHasInstance:md,ObjectDefineProperty:_d,ObjectDefineProperties:qv,ObjectSetPrototypeOf:vd,StringPrototypeToLowerCase:Lv,Symbol:Uv,SymbolHasInstance:Dv}=Ee();Td.exports=Ie;Ie.WritableState=Un;var{EventEmitter:jv}=(er(),me(Zt)),qn=$i().Stream,{Buffer:Xi}=(ke(),me(qe)),to=Ar(),{addAbortSignal:Fv}=kn(),{getHighWaterMark:Wv,getDefaultHighWaterMark:$v}=Cn(),{ERR_INVALID_ARG_TYPE:Hv,ERR_METHOD_NOT_IMPLEMENTED:Vv,ERR_MULTIPLE_CALLBACK:Sd,ERR_STREAM_CANNOT_PIPE:Gv,ERR_STREAM_DESTROYED:Ln,ERR_STREAM_ALREADY_FINISHED:zv,ERR_STREAM_NULL_VALUES:Kv,ERR_STREAM_WRITE_AFTER_END:Qv,ERR_UNKNOWN_ENCODING:Ed}=De().codes,{errorOrDestroy:on}=to;vd(Ie.prototype,qn.prototype);vd(Ie,qn);function Qs(){}var sn=Uv("kOnFinished");function Un(t,e,r){typeof r!="boolean"&&(r=e instanceof St()),this.objectMode=!!(t&&t.objectMode),r&&(this.objectMode=this.objectMode||!!(t&&t.writableObjectMode)),this.highWaterMark=t?Wv(this,t,"writableHighWaterMark",r):$v(!1),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;let n=!!(t&&t.decodeStrings===!1);this.decodeStrings=!n,this.defaultEncoding=t&&t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=Jv.bind(void 0,e),this.writecb=null,this.writelen=0,this.afterWriteTickInfo=null,eo(this),this.pendingcb=0,this.constructed=!0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!t||t.emitClose!==!1,this.autoDestroy=!t||t.autoDestroy!==!1,this.errored=null,this.closed=!1,this.closeEmitted=!1,this[sn]=[]}function eo(t){t.buffered=[],t.bufferedIndex=0,t.allBuffers=!0,t.allNoop=!0}Un.prototype.getBuffer=function(){return wd(this.buffered,this.bufferedIndex)};_d(Un.prototype,"bufferedRequestCount",{__proto__:null,get(){return this.buffered.length-this.bufferedIndex}});function Ie(t){let e=this instanceof St();if(!e&&!md(Ie,this))return new Ie(t);this._writableState=new Un(t,this,e),t&&(typeof t.write=="function"&&(this._write=t.write),typeof t.writev=="function"&&(this._writev=t.writev),typeof t.destroy=="function"&&(this._destroy=t.destroy),typeof t.final=="function"&&(this._final=t.final),typeof t.construct=="function"&&(this._construct=t.construct),t.signal&&Fv(t.signal,this)),qn.call(this,t),to.construct(this,()=>{let r=this._writableState;r.writing||Js(this,r),Xs(this,r)})}_d(Ie,Dv,{__proto__:null,value:function(t){return md(this,t)?!0:this!==Ie?!1:t&&t._writableState instanceof Un}});Ie.prototype.pipe=function(){on(this,new Gv)};function Ad(t,e,r,n){let i=t._writableState;if(typeof r=="function")n=r,r=i.defaultEncoding;else{if(!r)r=i.defaultEncoding;else if(r!=="buffer"&&!Xi.isEncoding(r))throw new Ed(r);typeof n!="function"&&(n=Qs)}if(e===null)throw new Kv;if(!i.objectMode)if(typeof e=="string")i.decodeStrings!==!1&&(e=Xi.from(e,r),r="buffer");else if(e instanceof Xi)r="buffer";else if(qn._isUint8Array(e))e=qn._uint8ArrayToBuffer(e),r="buffer";else throw new Hv("chunk",["string","Buffer","Uint8Array"],e);let s;return i.ending?s=new Qv:i.destroyed&&(s=new Ln("write")),s?(Tr.nextTick(n,s),on(t,s,!0),s):(i.pendingcb++,Yv(t,i,e,r,n))}Ie.prototype.write=function(t,e,r){return Ad(this,t,e,r)===!0};Ie.prototype.cork=function(){this._writableState.corked++};Ie.prototype.uncork=function(){let t=this._writableState;t.corked&&(t.corked--,t.writing||Js(this,t))};Ie.prototype.setDefaultEncoding=function(e){if(typeof e=="string"&&(e=Lv(e)),!Xi.isEncoding(e))throw new Ed(e);return this._writableState.defaultEncoding=e,this};function Yv(t,e,r,n,i){let s=e.objectMode?1:r.length;e.length+=s;let o=e.length<e.highWaterMark;return o||(e.needDrain=!0),e.writing||e.corked||e.errored||!e.constructed?(e.buffered.push({chunk:r,encoding:n,callback:i}),e.allBuffers&&n!=="buffer"&&(e.allBuffers=!1),e.allNoop&&i!==Qs&&(e.allNoop=!1)):(e.writelen=s,e.writecb=i,e.writing=!0,e.sync=!0,t._write(r,n,e.onwrite),e.sync=!1),o&&!e.errored&&!e.destroyed}function gd(t,e,r,n,i,s,o){e.writelen=n,e.writecb=o,e.writing=!0,e.sync=!0,e.destroyed?e.onwrite(new Ln("write")):r?t._writev(i,e.onwrite):t._write(i,s,e.onwrite),e.sync=!1}function bd(t,e,r,n){--e.pendingcb,n(r),Ys(e),on(t,r)}function Jv(t,e){let r=t._writableState,n=r.sync,i=r.writecb;if(typeof i!="function"){on(t,new Sd);return}r.writing=!1,r.writecb=null,r.length-=r.writelen,r.writelen=0,e?(e.stack,r.errored||(r.errored=e),t._readableState&&!t._readableState.errored&&(t._readableState.errored=e),n?Tr.nextTick(bd,t,r,e,i):bd(t,r,e,i)):(r.buffered.length>r.bufferedIndex&&Js(t,r),n?r.afterWriteTickInfo!==null&&r.afterWriteTickInfo.cb===i?r.afterWriteTickInfo.count++:(r.afterWriteTickInfo={count:1,cb:i,stream:t,state:r},Tr.nextTick(Xv,r.afterWriteTickInfo)):Id(t,r,1,i))}function Xv({stream:t,state:e,count:r,cb:n}){return e.afterWriteTickInfo=null,Id(t,e,r,n)}function Id(t,e,r,n){for(!e.ending&&!t.destroyed&&e.length===0&&e.needDrain&&(e.needDrain=!1,t.emit("drain"));r-- >0;)e.pendingcb--,n();e.destroyed&&Ys(e),Xs(t,e)}function Ys(t){if(t.writing)return;for(let i=t.bufferedIndex;i<t.buffered.length;++i){var e;let{chunk:s,callback:o}=t.buffered[i],a=t.objectMode?1:s.length;t.length-=a,o((e=t.errored)!==null&&e!==void 0?e:new Ln("write"))}let r=t[sn].splice(0);for(let i=0;i<r.length;i++){var n;r[i]((n=t.errored)!==null&&n!==void 0?n:new Ln("end"))}eo(t)}function Js(t,e){if(e.corked||e.bufferProcessing||e.destroyed||!e.constructed)return;let{buffered:r,bufferedIndex:n,objectMode:i}=e,s=r.length-n;if(!s)return;let o=n;if(e.bufferProcessing=!0,s>1&&t._writev){e.pendingcb-=s-1;let a=e.allNoop?Qs:f=>{for(let h=o;h<r.length;++h)r[h].callback(f)},c=e.allNoop&&o===0?r:wd(r,o);c.allBuffers=e.allBuffers,gd(t,e,!0,e.length,c,"",a),eo(e)}else{do{let{chunk:a,encoding:c,callback:f}=r[o];r[o++]=null;let h=i?1:a.length;gd(t,e,!1,h,a,c,f)}while(o<r.length&&!e.writing);o===r.length?eo(e):o>256?(r.splice(0,o),e.bufferedIndex=0):e.bufferedIndex=o}e.bufferProcessing=!1}Ie.prototype._write=function(t,e,r){if(this._writev)this._writev([{chunk:t,encoding:e}],r);else throw new Vv("_write()")};Ie.prototype._writev=null;Ie.prototype.end=function(t,e,r){let n=this._writableState;typeof t=="function"?(r=t,t=null,e=null):typeof e=="function"&&(r=e,e=null);let i;if(t!=null){let s=Ad(this,t,e);s instanceof Nv&&(i=s)}return n.corked&&(n.corked=1,this.uncork()),i||(!n.errored&&!n.ending?(n.ending=!0,Xs(this,n,!0),n.ended=!0):n.finished?i=new zv("end"):n.destroyed&&(i=new Ln("end"))),typeof r=="function"&&(i||n.finished?Tr.nextTick(r,i):n[sn].push(r)),this};function Zi(t){return t.ending&&!t.destroyed&&t.constructed&&t.length===0&&!t.errored&&t.buffered.length===0&&!t.finished&&!t.writing&&!t.errorEmitted&&!t.closeEmitted}function Zv(t,e){let r=!1;function n(i){if(r){on(t,i??Sd());return}if(r=!0,e.pendingcb--,i){let s=e[sn].splice(0);for(let o=0;o<s.length;o++)s[o](i);on(t,i,e.sync)}else Zi(e)&&(e.prefinished=!0,t.emit("prefinish"),e.pendingcb++,Tr.nextTick(Ks,t,e))}e.sync=!0,e.pendingcb++;try{t._final(n)}catch(i){n(i)}e.sync=!1}function e0(t,e){!e.prefinished&&!e.finalCalled&&(typeof t._final=="function"&&!e.destroyed?(e.finalCalled=!0,Zv(t,e)):(e.prefinished=!0,t.emit("prefinish")))}function Xs(t,e,r){Zi(e)&&(e0(t,e),e.pendingcb===0&&(r?(e.pendingcb++,Tr.nextTick((n,i)=>{Zi(i)?Ks(n,i):i.pendingcb--},t,e)):Zi(e)&&(e.pendingcb++,Ks(t,e))))}function Ks(t,e){e.pendingcb--,e.finished=!0;let r=e[sn].splice(0);for(let n=0;n<r.length;n++)r[n]();if(t.emit("finish"),e.autoDestroy){let n=t._readableState;(!n||n.autoDestroy&&(n.endEmitted||n.readable===!1))&&t.destroy()}}qv(Ie.prototype,{closed:{__proto__:null,get(){return this._writableState?this._writableState.closed:!1}},destroyed:{__proto__:null,get(){return this._writableState?this._writableState.destroyed:!1},set(t){this._writableState&&(this._writableState.destroyed=t)}},writable:{__proto__:null,get(){let t=this._writableState;return!!t&&t.writable!==!1&&!t.destroyed&&!t.errored&&!t.ending&&!t.ended},set(t){this._writableState&&(this._writableState.writable=!!t)}},writableFinished:{__proto__:null,get(){return this._writableState?this._writableState.finished:!1}},writableObjectMode:{__proto__:null,get(){return this._writableState?this._writableState.objectMode:!1}},writableBuffer:{__proto__:null,get(){return this._writableState&&this._writableState.getBuffer()}},writableEnded:{__proto__:null,get(){return this._writableState?this._writableState.ending:!1}},writableNeedDrain:{__proto__:null,get(){let t=this._writableState;return t?!t.destroyed&&!t.ending&&t.needDrain:!1}},writableHighWaterMark:{__proto__:null,get(){return this._writableState&&this._writableState.highWaterMark}},writableCorked:{__proto__:null,get(){return this._writableState?this._writableState.corked:0}},writableLength:{__proto__:null,get(){return this._writableState&&this._writableState.length}},errored:{__proto__:null,enumerable:!1,get(){return this._writableState?this._writableState.errored:null}},writableAborted:{__proto__:null,enumerable:!1,get:function(){return!!(this._writableState.writable!==!1&&(this._writableState.destroyed||this._writableState.errored)&&!this._writableState.finished)}}});var t0=to.destroy;Ie.prototype.destroy=function(t,e){let r=this._writableState;return!r.destroyed&&(r.bufferedIndex<r.buffered.length||r[sn].length)&&Tr.nextTick(Ys,r),t0.call(this,t,e),this};Ie.prototype._undestroy=to.undestroy;Ie.prototype._destroy=function(t,e){e(t)};Ie.prototype[jv.captureRejectionSymbol]=function(t){this.destroy(t)};var zs;function xd(){return zs===void 0&&(zs={}),zs}Ie.fromWeb=function(t,e){return xd().newStreamWritableFromWritableStream(t,e)};Ie.toWeb=function(t){return xd().newWritableStreamFromStreamWritable(t)}});var Wd=D((bC,Fd)=>{A();x();I();var Zs=tr(),r0=(ke(),me(qe)),{isReadable:n0,isWritable:i0,isIterable:Pd,isNodeStream:o0,isReadableNodeStream:Od,isWritableNodeStream:Rd,isDuplexNodeStream:s0,isReadableStream:kd,isWritableStream:Cd}=mt(),Bd=Ut(),{AbortError:Dd,codes:{ERR_INVALID_ARG_TYPE:a0,ERR_INVALID_RETURN_VALUE:Md}}=De(),{destroyer:un}=Ar(),u0=St(),jd=Nn(),l0=ro(),{createDeferredPromise:Nd}=$e(),qd=Us(),Ld=globalThis.Blob||r0.Blob,c0=typeof Ld<"u"?function(e){return e instanceof Ld}:function(e){return!1},f0=globalThis.AbortController||Yr().AbortController,{FunctionPrototypeCall:Ud}=Ee(),ir=class extends u0{constructor(e){super(e),e?.readable===!1&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),e?.writable===!1&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)}};Fd.exports=function t(e,r){if(s0(e))return e;if(Od(e))return an({readable:e});if(Rd(e))return an({writable:e});if(o0(e))return an({writable:!1,readable:!1});if(kd(e))return an({readable:jd.fromWeb(e)});if(Cd(e))return an({writable:l0.fromWeb(e)});if(typeof e=="function"){let{value:i,write:s,final:o,destroy:a}=h0(e);if(Pd(i))return qd(ir,i,{objectMode:!0,write:s,final:o,destroy:a});let c=i?.then;if(typeof c=="function"){let f,h=Ud(c,i,d=>{if(d!=null)throw new Md("nully","body",d)},d=>{un(f,d)});return f=new ir({objectMode:!0,readable:!1,write:s,final(d){o(async()=>{try{await h,Zs.nextTick(d,null)}catch(w){Zs.nextTick(d,w)}})},destroy:a})}throw new Md("Iterable, AsyncIterable or AsyncFunction",r,i)}if(c0(e))return t(e.arrayBuffer());if(Pd(e))return qd(ir,e,{objectMode:!0,writable:!1});if(kd(e?.readable)&&Cd(e?.writable))return ir.fromWeb(e);if(typeof e?.writable=="object"||typeof e?.readable=="object"){let i=e!=null&&e.readable?Od(e?.readable)?e?.readable:t(e.readable):void 0,s=e!=null&&e.writable?Rd(e?.writable)?e?.writable:t(e.writable):void 0;return an({readable:i,writable:s})}let n=e?.then;if(typeof n=="function"){let i;return Ud(n,e,s=>{s!=null&&i.push(s),i.push(null)},s=>{un(i,s)}),i=new ir({objectMode:!0,writable:!1,read(){}})}throw new a0(r,["Blob","ReadableStream","WritableStream","Stream","Iterable","AsyncIterable","Function","{ readable, writable } pair","Promise"],e)};function h0(t){let{promise:e,resolve:r}=Nd(),n=new f0,i=n.signal;return{value:t(async function*(){for(;;){let o=e;e=null;let{chunk:a,done:c,cb:f}=await o;if(Zs.nextTick(f),c)return;if(i.aborted)throw new Dd(void 0,{cause:i.reason});({promise:e,resolve:r}=Nd()),yield a}}(),{signal:i}),write(o,a,c){let f=r;r=null,f({chunk:o,done:!1,cb:c})},final(o){let a=r;r=null,a({done:!0,cb:o})},destroy(o,a){n.abort(),a(o)}}}function an(t){let e=t.readable&&typeof t.readable.read!="function"?jd.wrap(t.readable):t.readable,r=t.writable,n=!!n0(e),i=!!i0(r),s,o,a,c,f;function h(d){let w=c;c=null,w?w(d):d&&f.destroy(d)}return f=new ir({readableObjectMode:!!(e!=null&&e.readableObjectMode),writableObjectMode:!!(r!=null&&r.writableObjectMode),readable:n,writable:i}),i&&(Bd(r,d=>{i=!1,d&&un(e,d),h(d)}),f._write=function(d,w,y){r.write(d,w)?y():s=y},f._final=function(d){r.end(),o=d},r.on("drain",function(){if(s){let d=s;s=null,d()}}),r.on("finish",function(){if(o){let d=o;o=null,d()}})),n&&(Bd(e,d=>{n=!1,d&&un(e,d),h(d)}),e.on("readable",function(){if(a){let d=a;a=null,d()}}),e.on("end",function(){f.push(null)}),f._read=function(){for(;;){let d=e.read();if(d===null){a=f._read;return}if(!f.push(d))return}}),f._destroy=function(d,w){!d&&c!==null&&(d=new Dd),a=null,s=null,o=null,c===null?w(d):(c=w,un(r,d),un(e,d))},f}});var St=D((TC,Vd)=>{"use strict";A();x();I();var{ObjectDefineProperties:d0,ObjectGetOwnPropertyDescriptor:Dt,ObjectKeys:p0,ObjectSetPrototypeOf:$d}=Ee();Vd.exports=ct;var ra=Nn(),tt=ro();$d(ct.prototype,ra.prototype);$d(ct,ra);{let t=p0(tt.prototype);for(let e=0;e<t.length;e++){let r=t[e];ct.prototype[r]||(ct.prototype[r]=tt.prototype[r])}}function ct(t){if(!(this instanceof ct))return new ct(t);ra.call(this,t),tt.call(this,t),t?(this.allowHalfOpen=t.allowHalfOpen!==!1,t.readable===!1&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),t.writable===!1&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)):this.allowHalfOpen=!0}d0(ct.prototype,{writable:{__proto__:null,...Dt(tt.prototype,"writable")},writableHighWaterMark:{__proto__:null,...Dt(tt.prototype,"writableHighWaterMark")},writableObjectMode:{__proto__:null,...Dt(tt.prototype,"writableObjectMode")},writableBuffer:{__proto__:null,...Dt(tt.prototype,"writableBuffer")},writableLength:{__proto__:null,...Dt(tt.prototype,"writableLength")},writableFinished:{__proto__:null,...Dt(tt.prototype,"writableFinished")},writableCorked:{__proto__:null,...Dt(tt.prototype,"writableCorked")},writableEnded:{__proto__:null,...Dt(tt.prototype,"writableEnded")},writableNeedDrain:{__proto__:null,...Dt(tt.prototype,"writableNeedDrain")},destroyed:{__proto__:null,get(){return this._readableState===void 0||this._writableState===void 0?!1:this._readableState.destroyed&&this._writableState.destroyed},set(t){this._readableState&&this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}}});var ea;function Hd(){return ea===void 0&&(ea={}),ea}ct.fromWeb=function(t,e){return Hd().newStreamDuplexFromReadableWritablePair(t,e)};ct.toWeb=function(t){return Hd().newReadableWritablePairFromDuplex(t)};var ta;ct.from=function(t){return ta||(ta=Wd()),ta(t,"body")}});var oa=D((LC,zd)=>{"use strict";A();x();I();var{ObjectSetPrototypeOf:Gd,Symbol:y0}=Ee();zd.exports=jt;var{ERR_METHOD_NOT_IMPLEMENTED:g0}=De().codes,ia=St(),{getHighWaterMark:b0}=Cn();Gd(jt.prototype,ia.prototype);Gd(jt,ia);var Dn=y0("kCallback");function jt(t){if(!(this instanceof jt))return new jt(t);let e=t?b0(this,t,"readableHighWaterMark",!0):null;e===0&&(t={...t,highWaterMark:null,readableHighWaterMark:e,writableHighWaterMark:t.writableHighWaterMark||0}),ia.call(this,t),this._readableState.sync=!1,this[Dn]=null,t&&(typeof t.transform=="function"&&(this._transform=t.transform),typeof t.flush=="function"&&(this._flush=t.flush)),this.on("prefinish",w0)}function na(t){typeof this._flush=="function"&&!this.destroyed?this._flush((e,r)=>{if(e){t?t(e):this.destroy(e);return}r!=null&&this.push(r),this.push(null),t&&t()}):(this.push(null),t&&t())}function w0(){this._final!==na&&na.call(this)}jt.prototype._final=na;jt.prototype._transform=function(t,e,r){throw new g0("_transform()")};jt.prototype._write=function(t,e,r){let n=this._readableState,i=this._writableState,s=n.length;this._transform(t,e,(o,a)=>{if(o){r(o);return}a!=null&&this.push(a),i.ended||s===n.length||n.length<n.highWaterMark?r():this[Dn]=r})};jt.prototype._read=function(){if(this[Dn]){let t=this[Dn];this[Dn]=null,t()}}});var aa=D((zC,Qd)=>{"use strict";A();x();I();var{ObjectSetPrototypeOf:Kd}=Ee();Qd.exports=ln;var sa=oa();Kd(ln.prototype,sa.prototype);Kd(ln,sa);function ln(t){if(!(this instanceof ln))return new ln(t);sa.call(this,t)}ln.prototype._transform=function(t,e,r){r(null,t)}});var so=D((nB,ep)=>{A();x();I();var jn=tr(),{ArrayIsArray:m0,Promise:_0,SymbolAsyncIterator:v0,SymbolDispose:S0}=Ee(),oo=Ut(),{once:E0}=$e(),A0=Ar(),Yd=St(),{aggregateTwoErrors:I0,codes:{ERR_INVALID_ARG_TYPE:ga,ERR_INVALID_RETURN_VALUE:ua,ERR_MISSING_ARGS:x0,ERR_STREAM_DESTROYED:T0,ERR_STREAM_PREMATURE_CLOSE:P0},AbortError:O0}=De(),{validateFunction:R0,validateAbortSignal:k0}=Xr(),{isIterable:Pr,isReadable:la,isReadableNodeStream:io,isNodeStream:Jd,isTransformStream:cn,isWebStream:C0,isReadableStream:ca,isReadableFinished:B0}=mt(),M0=globalThis.AbortController||Yr().AbortController,fa,ha,da;function Xd(t,e,r){let n=!1;t.on("close",()=>{n=!0});let i=oo(t,{readable:e,writable:r},s=>{n=!s});return{destroy:s=>{n||(n=!0,A0.destroyer(t,s||new T0("pipe")))},cleanup:i}}function N0(t){return R0(t[t.length-1],"streams[stream.length - 1]"),t.pop()}function pa(t){if(Pr(t))return t;if(io(t))return q0(t);throw new ga("val",["Readable","Iterable","AsyncIterable"],t)}async function*q0(t){ha||(ha=Nn()),yield*ha.prototype[v0].call(t)}async function no(t,e,r,{end:n}){let i,s=null,o=f=>{if(f&&(i=f),s){let h=s;s=null,h()}},a=()=>new _0((f,h)=>{i?h(i):s=()=>{i?h(i):f()}});e.on("drain",o);let c=oo(e,{readable:!1},o);try{e.writableNeedDrain&&await a();for await(let f of t)e.write(f)||await a();n&&(e.end(),await a()),r()}catch(f){r(i!==f?I0(i,f):f)}finally{c(),e.off("drain",o)}}async function ya(t,e,r,{end:n}){cn(e)&&(e=e.writable);let i=e.getWriter();try{for await(let s of t)await i.ready,i.write(s).catch(()=>{});await i.ready,n&&await i.close(),r()}catch(s){try{await i.abort(s),r(s)}catch(o){r(o)}}}function L0(...t){return Zd(t,E0(N0(t)))}function Zd(t,e,r){if(t.length===1&&m0(t[0])&&(t=t[0]),t.length<2)throw new x0("streams");let n=new M0,i=n.signal,s=r?.signal,o=[];k0(s,"options.signal");function a(){P(new O0)}da=da||$e().addAbortListener;let c;s&&(c=da(s,a));let f,h,d=[],w=0;function y(R){P(R,--w===0)}function P(R,T){var g;if(R&&(!f||f.code==="ERR_STREAM_PREMATURE_CLOSE")&&(f=R),!(!f&&!T)){for(;d.length;)d.shift()(f);(g=c)===null||g===void 0||g[S0](),n.abort(),T&&(f||o.forEach(O=>O()),jn.nextTick(e,f,h))}}let E;for(let R=0;R<t.length;R++){let T=t[R],g=R<t.length-1,O=R>0,_=g||r?.end!==!1,q=R===t.length-1;if(Jd(T)){let $=function(j){j&&j.name!=="AbortError"&&j.code!=="ERR_STREAM_PREMATURE_CLOSE"&&y(j)};var v=$;if(_){let{destroy:j,cleanup:J}=Xd(T,g,O);d.push(j),la(T)&&q&&o.push(J)}T.on("error",$),la(T)&&q&&o.push(()=>{T.removeListener("error",$)})}if(R===0)if(typeof T=="function"){if(E=T({signal:i}),!Pr(E))throw new ua("Iterable, AsyncIterable or Stream","source",E)}else Pr(T)||io(T)||cn(T)?E=T:E=Yd.from(T);else if(typeof T=="function"){if(cn(E)){var b;E=pa((b=E)===null||b===void 0?void 0:b.readable)}else E=pa(E);if(E=T(E,{signal:i}),g){if(!Pr(E,!0))throw new ua("AsyncIterable",`transform[${R-1}]`,E)}else{var m;fa||(fa=aa());let $=new fa({objectMode:!0}),j=(m=E)===null||m===void 0?void 0:m.then;if(typeof j=="function")w++,j.call(E,H=>{h=H,H!=null&&$.write(H),_&&$.end(),jn.nextTick(y)},H=>{$.destroy(H),jn.nextTick(y,H)});else if(Pr(E,!0))w++,no(E,$,y,{end:_});else if(ca(E)||cn(E)){let H=E.readable||E;w++,no(H,$,y,{end:_})}else throw new ua("AsyncIterable or Promise","destination",E);E=$;let{destroy:J,cleanup:W}=Xd(E,!1,!0);d.push(J),q&&o.push(W)}}else if(Jd(T)){if(io(E)){w+=2;let $=U0(E,T,y,{end:_});la(T)&&q&&o.push($)}else if(cn(E)||ca(E)){let $=E.readable||E;w++,no($,T,y,{end:_})}else if(Pr(E))w++,no(E,T,y,{end:_});else throw new ga("val",["Readable","Iterable","AsyncIterable","ReadableStream","TransformStream"],E);E=T}else if(C0(T)){if(io(E))w++,ya(pa(E),T,y,{end:_});else if(ca(E)||Pr(E))w++,ya(E,T,y,{end:_});else if(cn(E))w++,ya(E.readable,T,y,{end:_});else throw new ga("val",["Readable","Iterable","AsyncIterable","ReadableStream","TransformStream"],E);E=T}else E=Yd.from(T)}return(i!=null&&i.aborted||s!=null&&s.aborted)&&jn.nextTick(a),E}function U0(t,e,r,{end:n}){let i=!1;if(e.on("close",()=>{i||r(new P0)}),t.pipe(e,{end:!1}),n){let o=function(){i=!0,e.end()};var s=o;B0(t)?jn.nextTick(o):t.once("end",o)}else r();return oo(t,{readable:!0,writable:!1},o=>{let a=t._readableState;o&&o.code==="ERR_STREAM_PREMATURE_CLOSE"&&a&&a.ended&&!a.errored&&!a.errorEmitted?t.once("end",r).once("error",r):r(o)}),oo(e,{readable:!1,writable:!0},r)}ep.exports={pipelineImpl:Zd,pipeline:L0}});var wa=D((dB,sp)=>{"use strict";A();x();I();var{pipeline:D0}=so(),ao=St(),{destroyer:j0}=Ar(),{isNodeStream:uo,isReadable:tp,isWritable:rp,isWebStream:ba,isTransformStream:Or,isWritableStream:np,isReadableStream:ip}=mt(),{AbortError:F0,codes:{ERR_INVALID_ARG_VALUE:op,ERR_MISSING_ARGS:W0}}=De(),$0=Ut();sp.exports=function(...e){if(e.length===0)throw new W0("streams");if(e.length===1)return ao.from(e[0]);let r=[...e];if(typeof e[0]=="function"&&(e[0]=ao.from(e[0])),typeof e[e.length-1]=="function"){let y=e.length-1;e[y]=ao.from(e[y])}for(let y=0;y<e.length;++y)if(!(!uo(e[y])&&!ba(e[y]))){if(y<e.length-1&&!(tp(e[y])||ip(e[y])||Or(e[y])))throw new op(`streams[${y}]`,r[y],"must be readable");if(y>0&&!(rp(e[y])||np(e[y])||Or(e[y])))throw new op(`streams[${y}]`,r[y],"must be writable")}let n,i,s,o,a;function c(y){let P=o;o=null,P?P(y):y?a.destroy(y):!w&&!d&&a.destroy()}let f=e[0],h=D0(e,c),d=!!(rp(f)||np(f)||Or(f)),w=!!(tp(h)||ip(h)||Or(h));if(a=new ao({writableObjectMode:!!(f!=null&&f.writableObjectMode),readableObjectMode:!!(h!=null&&h.readableObjectMode),writable:d,readable:w}),d){if(uo(f))a._write=function(P,E,b){f.write(P,E)?b():n=b},a._final=function(P){f.end(),i=P},f.on("drain",function(){if(n){let P=n;n=null,P()}});else if(ba(f)){let E=(Or(f)?f.writable:f).getWriter();a._write=async function(b,m,v){try{await E.ready,E.write(b).catch(()=>{}),v()}catch(R){v(R)}},a._final=async function(b){try{await E.ready,E.close().catch(()=>{}),i=b}catch(m){b(m)}}}let y=Or(h)?h.readable:h;$0(y,()=>{if(i){let P=i;i=null,P()}})}if(w){if(uo(h))h.on("readable",function(){if(s){let y=s;s=null,y()}}),h.on("end",function(){a.push(null)}),a._read=function(){for(;;){let y=h.read();if(y===null){s=a._read;return}if(!a.push(y))return}};else if(ba(h)){let P=(Or(h)?h.readable:h).getReader();a._read=async function(){for(;;)try{let{value:E,done:b}=await P.read();if(!a.push(E))return;if(b){a.push(null);return}}catch{return}}}}return a._destroy=function(y,P){!y&&o!==null&&(y=new F0),s=null,n=null,i=null,o===null?P(y):(o=P,uo(h)&&j0(h,y))},a}});var gp=D((EB,va)=>{"use strict";A();x();I();var H0=globalThis.AbortController||Yr().AbortController,{codes:{ERR_INVALID_ARG_VALUE:V0,ERR_INVALID_ARG_TYPE:Fn,ERR_MISSING_ARGS:G0,ERR_OUT_OF_RANGE:z0},AbortError:Et}=De(),{validateAbortSignal:Rr,validateInteger:ap,validateObject:kr}=Xr(),K0=Ee().Symbol("kWeak"),Q0=Ee().Symbol("kResistStopPropagation"),{finished:Y0}=Ut(),J0=wa(),{addAbortSignalNoValidate:X0}=kn(),{isWritable:Z0,isNodeStream:eS}=mt(),{deprecate:tS}=$e(),{ArrayPrototypePush:rS,Boolean:nS,MathFloor:up,Number:iS,NumberIsNaN:oS,Promise:lp,PromiseReject:cp,PromiseResolve:sS,PromisePrototypeThen:fp,Symbol:dp}=Ee(),lo=dp("kEmpty"),hp=dp("kEof");function aS(t,e){if(e!=null&&kr(e,"options"),e?.signal!=null&&Rr(e.signal,"options.signal"),eS(t)&&!Z0(t))throw new V0("stream",t,"must be writable");let r=J0(this,t);return e!=null&&e.signal&&X0(e.signal,r),r}function co(t,e){if(typeof t!="function")throw new Fn("fn",["Function","AsyncFunction"],t);e!=null&&kr(e,"options"),e?.signal!=null&&Rr(e.signal,"options.signal");let r=1;e?.concurrency!=null&&(r=up(e.concurrency));let n=r-1;return e?.highWaterMark!=null&&(n=up(e.highWaterMark)),ap(r,"options.concurrency",1),ap(n,"options.highWaterMark",0),n+=r,async function*(){let s=$e().AbortSignalAny([e?.signal].filter(nS)),o=this,a=[],c={signal:s},f,h,d=!1,w=0;function y(){d=!0,P()}function P(){w-=1,E()}function E(){h&&!d&&w<r&&a.length<n&&(h(),h=null)}async function b(){try{for await(let m of o){if(d)return;if(s.aborted)throw new Et;try{if(m=t(m,c),m===lo)continue;m=sS(m)}catch(v){m=cp(v)}w+=1,fp(m,P,y),a.push(m),f&&(f(),f=null),!d&&(a.length>=n||w>=r)&&await new lp(v=>{h=v})}a.push(hp)}catch(m){let v=cp(m);fp(v,P,y),a.push(v)}finally{d=!0,f&&(f(),f=null)}}b();try{for(;;){for(;a.length>0;){let m=await a[0];if(m===hp)return;if(s.aborted)throw new Et;m!==lo&&(yield m),a.shift(),E()}await new lp(m=>{f=m})}}finally{d=!0,h&&(h(),h=null)}}.call(this)}function uS(t=void 0){return t!=null&&kr(t,"options"),t?.signal!=null&&Rr(t.signal,"options.signal"),async function*(){let r=0;for await(let i of this){var n;if(t!=null&&(n=t.signal)!==null&&n!==void 0&&n.aborted)throw new Et({cause:t.signal.reason});yield[r++,i]}}.call(this)}async function pp(t,e=void 0){for await(let r of _a.call(this,t,e))return!0;return!1}async function lS(t,e=void 0){if(typeof t!="function")throw new Fn("fn",["Function","AsyncFunction"],t);return!await pp.call(this,async(...r)=>!await t(...r),e)}async function cS(t,e){for await(let r of _a.call(this,t,e))return r}async function fS(t,e){if(typeof t!="function")throw new Fn("fn",["Function","AsyncFunction"],t);async function r(n,i){return await t(n,i),lo}for await(let n of co.call(this,r,e));}function _a(t,e){if(typeof t!="function")throw new Fn("fn",["Function","AsyncFunction"],t);async function r(n,i){return await t(n,i)?n:lo}return co.call(this,r,e)}var ma=class extends G0{constructor(){super("reduce"),this.message="Reduce of an empty stream requires an initial value"}};async function hS(t,e,r){var n;if(typeof t!="function")throw new Fn("reducer",["Function","AsyncFunction"],t);r!=null&&kr(r,"options"),r?.signal!=null&&Rr(r.signal,"options.signal");let i=arguments.length>1;if(r!=null&&(n=r.signal)!==null&&n!==void 0&&n.aborted){let f=new Et(void 0,{cause:r.signal.reason});throw this.once("error",()=>{}),await Y0(this.destroy(f)),f}let s=new H0,o=s.signal;if(r!=null&&r.signal){let f={once:!0,[K0]:this,[Q0]:!0};r.signal.addEventListener("abort",()=>s.abort(),f)}let a=!1;try{for await(let f of this){var c;if(a=!0,r!=null&&(c=r.signal)!==null&&c!==void 0&&c.aborted)throw new Et;i?e=await t(e,f,{signal:o}):(e=f,i=!0)}if(!a&&!i)throw new ma}finally{s.abort()}return e}async function dS(t){t!=null&&kr(t,"options"),t?.signal!=null&&Rr(t.signal,"options.signal");let e=[];for await(let n of this){var r;if(t!=null&&(r=t.signal)!==null&&r!==void 0&&r.aborted)throw new Et(void 0,{cause:t.signal.reason});rS(e,n)}return e}function pS(t,e){let r=co.call(this,t,e);return async function*(){for await(let i of r)yield*i}.call(this)}function yp(t){if(t=iS(t),oS(t))return 0;if(t<0)throw new z0("number",">= 0",t);return t}function yS(t,e=void 0){return e!=null&&kr(e,"options"),e?.signal!=null&&Rr(e.signal,"options.signal"),t=yp(t),async function*(){var n;if(e!=null&&(n=e.signal)!==null&&n!==void 0&&n.aborted)throw new Et;for await(let s of this){var i;if(e!=null&&(i=e.signal)!==null&&i!==void 0&&i.aborted)throw new Et;t--<=0&&(yield s)}}.call(this)}function gS(t,e=void 0){return e!=null&&kr(e,"options"),e?.signal!=null&&Rr(e.signal,"options.signal"),t=yp(t),async function*(){var n;if(e!=null&&(n=e.signal)!==null&&n!==void 0&&n.aborted)throw new Et;for await(let s of this){var i;if(e!=null&&(i=e.signal)!==null&&i!==void 0&&i.aborted)throw new Et;if(t-- >0&&(yield s),t<=0)return}}.call(this)}va.exports.streamReturningOperators={asIndexedPairs:tS(uS,"readable.asIndexedPairs will be removed in a future version."),drop:yS,filter:_a,flatMap:pS,map:co,take:gS,compose:aS};va.exports.promiseReturningOperators={every:lS,forEach:fS,reduce:hS,toArray:dS,some:pp,find:cS}});var Sa=D((BB,bp)=>{"use strict";A();x();I();var{ArrayPrototypePop:bS,Promise:wS}=Ee(),{isIterable:mS,isNodeStream:_S,isWebStream:vS}=mt(),{pipelineImpl:SS}=so(),{finished:ES}=Ut();Ea();function AS(...t){return new wS((e,r)=>{let n,i,s=t[t.length-1];if(s&&typeof s=="object"&&!_S(s)&&!mS(s)&&!vS(s)){let o=bS(t);n=o.signal,i=o.end}SS(t,(o,a)=>{o?r(o):e(a)},{signal:n,end:i})})}bp.exports={finished:ES,pipeline:AS}});var Ea=D(($B,xp)=>{"use strict";A();x();I();var{Buffer:IS}=(ke(),me(qe)),{ObjectDefineProperty:Ft,ObjectKeys:_p,ReflectApply:vp}=Ee(),{promisify:{custom:Sp}}=$e(),{streamReturningOperators:wp,promiseReturningOperators:mp}=gp(),{codes:{ERR_ILLEGAL_CONSTRUCTOR:Ep}}=De(),xS=wa(),{setDefaultHighWaterMark:TS,getDefaultHighWaterMark:PS}=Cn(),{pipeline:Ap}=so(),{destroyer:OS}=Ar(),Ip=Ut(),Aa=Sa(),Wn=mt(),Se=xp.exports=$i().Stream;Se.isDestroyed=Wn.isDestroyed;Se.isDisturbed=Wn.isDisturbed;Se.isErrored=Wn.isErrored;Se.isReadable=Wn.isReadable;Se.isWritable=Wn.isWritable;Se.Readable=Nn();for(let t of _p(wp)){let r=function(...n){if(new.target)throw Ep();return Se.Readable.from(vp(e,this,n))},e=wp[t];Ft(r,"name",{__proto__:null,value:e.name}),Ft(r,"length",{__proto__:null,value:e.length}),Ft(Se.Readable.prototype,t,{__proto__:null,value:r,enumerable:!1,configurable:!0,writable:!0})}for(let t of _p(mp)){let r=function(...n){if(new.target)throw Ep();return vp(e,this,n)},e=mp[t];Ft(r,"name",{__proto__:null,value:e.name}),Ft(r,"length",{__proto__:null,value:e.length}),Ft(Se.Readable.prototype,t,{__proto__:null,value:r,enumerable:!1,configurable:!0,writable:!0})}Se.Writable=ro();Se.Duplex=St();Se.Transform=oa();Se.PassThrough=aa();Se.pipeline=Ap;var{addAbortSignal:RS}=kn();Se.addAbortSignal=RS;Se.finished=Ip;Se.destroy=OS;Se.compose=xS;Se.setDefaultHighWaterMark=TS;Se.getDefaultHighWaterMark=PS;Ft(Se,"promises",{__proto__:null,configurable:!0,enumerable:!0,get(){return Aa}});Ft(Ap,Sp,{__proto__:null,enumerable:!0,get(){return Aa.pipeline}});Ft(Ip,Sp,{__proto__:null,enumerable:!0,get(){return Aa.finished}});Se.Stream=Se;Se._isUint8Array=function(e){return e instanceof Uint8Array};Se._uint8ArrayToBuffer=function(e){return IS.from(e.buffer,e.byteOffset,e.byteLength)}});var or=D((ZB,Pe)=>{"use strict";A();x();I();var Be=Ea(),kS=Sa(),CS=Be.Readable.destroy;Pe.exports=Be.Readable;Pe.exports._uint8ArrayToBuffer=Be._uint8ArrayToBuffer;Pe.exports._isUint8Array=Be._isUint8Array;Pe.exports.isDisturbed=Be.isDisturbed;Pe.exports.isErrored=Be.isErrored;Pe.exports.isReadable=Be.isReadable;Pe.exports.Readable=Be.Readable;Pe.exports.Writable=Be.Writable;Pe.exports.Duplex=Be.Duplex;Pe.exports.Transform=Be.Transform;Pe.exports.PassThrough=Be.PassThrough;Pe.exports.addAbortSignal=Be.addAbortSignal;Pe.exports.finished=Be.finished;Pe.exports.destroy=Be.destroy;Pe.exports.destroy=CS;Pe.exports.pipeline=Be.pipeline;Pe.exports.compose=Be.compose;Object.defineProperty(Be,"promises",{configurable:!0,enumerable:!0,get(){return kS}});Pe.exports.Stream=Be.Stream;Pe.exports.default=Pe.exports});var Tp=D((lM,Ia)=>{A();x();I();typeof Object.create=="function"?Ia.exports=function(e,r){r&&(e.super_=r,e.prototype=Object.create(r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:Ia.exports=function(e,r){if(r){e.super_=r;var n=function(){};n.prototype=r.prototype,e.prototype=new n,e.prototype.constructor=e}}});var Rp=D((mM,Op)=>{"use strict";A();x();I();var{Buffer:ft}=(ke(),me(qe)),Pp=Symbol.for("BufferList");function be(t){if(!(this instanceof be))return new be(t);be._init.call(this,t)}be._init=function(e){Object.defineProperty(this,Pp,{value:!0}),this._bufs=[],this.length=0,e&&this.append(e)};be.prototype._new=function(e){return new be(e)};be.prototype._offset=function(e){if(e===0)return[0,0];let r=0;for(let n=0;n<this._bufs.length;n++){let i=r+this._bufs[n].length;if(e<i||n===this._bufs.length-1)return[n,e-r];r=i}};be.prototype._reverseOffset=function(t){let e=t[0],r=t[1];for(let n=0;n<e;n++)r+=this._bufs[n].length;return r};be.prototype.getBuffers=function(){return this._bufs};be.prototype.get=function(e){if(e>this.length||e<0)return;let r=this._offset(e);return this._bufs[r[0]][r[1]]};be.prototype.slice=function(e,r){return typeof e=="number"&&e<0&&(e+=this.length),typeof r=="number"&&r<0&&(r+=this.length),this.copy(null,0,e,r)};be.prototype.copy=function(e,r,n,i){if((typeof n!="number"||n<0)&&(n=0),(typeof i!="number"||i>this.length)&&(i=this.length),n>=this.length||i<=0)return e||ft.alloc(0);let s=!!e,o=this._offset(n),a=i-n,c=a,f=s&&r||0,h=o[1];if(n===0&&i===this.length){if(!s)return this._bufs.length===1?this._bufs[0]:ft.concat(this._bufs,this.length);for(let d=0;d<this._bufs.length;d++)this._bufs[d].copy(e,f),f+=this._bufs[d].length;return e}if(c<=this._bufs[o[0]].length-h)return s?this._bufs[o[0]].copy(e,r,h,h+c):this._bufs[o[0]].slice(h,h+c);s||(e=ft.allocUnsafe(a));for(let d=o[0];d<this._bufs.length;d++){let w=this._bufs[d].length-h;if(c>w)this._bufs[d].copy(e,f,h),f+=w;else{this._bufs[d].copy(e,f,h,h+c),f+=w;break}c-=w,h&&(h=0)}return e.length>f?e.slice(0,f):e};be.prototype.shallowSlice=function(e,r){if(e=e||0,r=typeof r!="number"?this.length:r,e<0&&(e+=this.length),r<0&&(r+=this.length),e===r)return this._new();let n=this._offset(e),i=this._offset(r),s=this._bufs.slice(n[0],i[0]+1);return i[1]===0?s.pop():s[s.length-1]=s[s.length-1].slice(0,i[1]),n[1]!==0&&(s[0]=s[0].slice(n[1])),this._new(s)};be.prototype.toString=function(e,r,n){return this.slice(r,n).toString(e)};be.prototype.consume=function(e){if(e=Math.trunc(e),Number.isNaN(e)||e<=0)return this;for(;this._bufs.length;)if(e>=this._bufs[0].length)e-=this._bufs[0].length,this.length-=this._bufs[0].length,this._bufs.shift();else{this._bufs[0]=this._bufs[0].slice(e),this.length-=e;break}return this};be.prototype.duplicate=function(){let e=this._new();for(let r=0;r<this._bufs.length;r++)e.append(this._bufs[r]);return e};be.prototype.append=function(e){return this._attach(e,be.prototype._appendBuffer)};be.prototype.prepend=function(e){return this._attach(e,be.prototype._prependBuffer,!0)};be.prototype._attach=function(e,r,n){if(e==null)return this;if(e.buffer)r.call(this,ft.from(e.buffer,e.byteOffset,e.byteLength));else if(Array.isArray(e)){let[i,s]=n?[e.length-1,-1]:[0,1];for(let o=i;o>=0&&o<e.length;o+=s)this._attach(e[o],r,n)}else if(this._isBufferList(e)){let[i,s]=n?[e._bufs.length-1,-1]:[0,1];for(let o=i;o>=0&&o<e._bufs.length;o+=s)this._attach(e._bufs[o],r,n)}else typeof e=="number"&&(e=e.toString()),r.call(this,ft.from(e));return this};be.prototype._appendBuffer=function(e){this._bufs.push(e),this.length+=e.length};be.prototype._prependBuffer=function(e){this._bufs.unshift(e),this.length+=e.length};be.prototype.indexOf=function(t,e,r){if(r===void 0&&typeof e=="string"&&(r=e,e=void 0),typeof t=="function"||Array.isArray(t))throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.');if(typeof t=="number"?t=ft.from([t]):typeof t=="string"?t=ft.from(t,r):this._isBufferList(t)?t=t.slice():Array.isArray(t.buffer)?t=ft.from(t.buffer,t.byteOffset,t.byteLength):ft.isBuffer(t)||(t=ft.from(t)),e=Number(e||0),isNaN(e)&&(e=0),e<0&&(e=this.length+e),e<0&&(e=0),t.length===0)return e>this.length?this.length:e;let n=this._offset(e),i=n[0],s=n[1];for(;i<this._bufs.length;i++){let o=this._bufs[i];for(;s<o.length;)if(o.length-s>=t.length){let c=o.indexOf(t,s);if(c!==-1)return this._reverseOffset([i,c]);s=o.length-t.length+1}else{let c=this._reverseOffset([i,s]);if(this._match(c,t))return c;s++}s=0}return-1};be.prototype._match=function(t,e){if(this.length-t<e.length)return!1;for(let r=0;r<e.length;r++)if(this.get(t+r)!==e[r])return!1;return!0};(function(){let t={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readBigInt64BE:8,readBigInt64LE:8,readBigUInt64BE:8,readBigUInt64LE:8,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1,readIntBE:null,readIntLE:null,readUIntBE:null,readUIntLE:null};for(let e in t)(function(r){t[r]===null?be.prototype[r]=function(n,i){return this.slice(n,n+i)[r](0,i)}:be.prototype[r]=function(n=0){return this.slice(n,n+t[r])[r](0)}})(e)})();be.prototype._isBufferList=function(e){return e instanceof be||be.isBufferList(e)};be.isBufferList=function(e){return e!=null&&e[Pp]};Op.exports=be});var kp=D((OM,fo)=>{"use strict";A();x();I();var xa=or().Duplex,BS=Tp(),$n=Rp();function Fe(t){if(!(this instanceof Fe))return new Fe(t);if(typeof t=="function"){this._callback=t;let e=function(n){this._callback&&(this._callback(n),this._callback=null)}.bind(this);this.on("pipe",function(n){n.on("error",e)}),this.on("unpipe",function(n){n.removeListener("error",e)}),t=null}$n._init.call(this,t),xa.call(this)}BS(Fe,xa);Object.assign(Fe.prototype,$n.prototype);Fe.prototype._new=function(e){return new Fe(e)};Fe.prototype._write=function(e,r,n){this._appendBuffer(e),typeof n=="function"&&n()};Fe.prototype._read=function(e){if(!this.length)return this.push(null);e=Math.min(e,this.length),this.push(this.slice(0,e)),this.consume(e)};Fe.prototype.end=function(e){xa.prototype.end.call(this,e),this._callback&&(this._callback(null,this.slice()),this._callback=null)};Fe.prototype._destroy=function(e,r){this._bufs.length=0,this.length=0,r(e)};Fe.prototype._isBufferList=function(e){return e instanceof Fe||e instanceof $n||Fe.isBufferList(e)};Fe.isBufferList=$n.isBufferList;fo.exports=Fe;fo.exports.BufferListStream=Fe;fo.exports.BufferList=$n});var Bp=D((DM,Cp)=>{A();x();I();var Ta=class{constructor(){this.cmd=null,this.retain=!1,this.qos=0,this.dup=!1,this.length=-1,this.topic=null,this.payload=null}};Cp.exports=Ta});var Pa=D((QM,Mp)=>{A();x();I();var z=Mp.exports,{Buffer:Ke}=(ke(),me(qe));z.types={0:"reserved",1:"connect",2:"connack",3:"publish",4:"puback",5:"pubrec",6:"pubrel",7:"pubcomp",8:"subscribe",9:"suback",10:"unsubscribe",11:"unsuback",12:"pingreq",13:"pingresp",14:"disconnect",15:"auth"};z.requiredHeaderFlags={1:0,2:0,4:0,5:0,6:2,7:0,8:2,9:0,10:2,11:0,12:0,13:0,14:0,15:0};z.requiredHeaderFlagsErrors={};for(let t in z.requiredHeaderFlags){let e=z.requiredHeaderFlags[t];z.requiredHeaderFlagsErrors[t]="Invalid header flag bits, must be 0x"+e.toString(16)+" for "+z.types[t]+" packet"}z.codes={};for(let t in z.types){let e=z.types[t];z.codes[e]=t}z.CMD_SHIFT=4;z.CMD_MASK=240;z.DUP_MASK=8;z.QOS_MASK=3;z.QOS_SHIFT=1;z.RETAIN_MASK=1;z.VARBYTEINT_MASK=127;z.VARBYTEINT_FIN_MASK=128;z.VARBYTEINT_MAX=268435455;z.SESSIONPRESENT_MASK=1;z.SESSIONPRESENT_HEADER=Ke.from([z.SESSIONPRESENT_MASK]);z.CONNACK_HEADER=Ke.from([z.codes.connack<<z.CMD_SHIFT]);z.USERNAME_MASK=128;z.PASSWORD_MASK=64;z.WILL_RETAIN_MASK=32;z.WILL_QOS_MASK=24;z.WILL_QOS_SHIFT=3;z.WILL_FLAG_MASK=4;z.CLEAN_SESSION_MASK=2;z.CONNECT_HEADER=Ke.from([z.codes.connect<<z.CMD_SHIFT]);z.properties={sessionExpiryInterval:17,willDelayInterval:24,receiveMaximum:33,maximumPacketSize:39,topicAliasMaximum:34,requestResponseInformation:25,requestProblemInformation:23,userProperties:38,authenticationMethod:21,authenticationData:22,payloadFormatIndicator:1,messageExpiryInterval:2,contentType:3,responseTopic:8,correlationData:9,maximumQoS:36,retainAvailable:37,assignedClientIdentifier:18,reasonString:31,wildcardSubscriptionAvailable:40,subscriptionIdentifiersAvailable:41,sharedSubscriptionAvailable:42,serverKeepAlive:19,responseInformation:26,serverReference:28,topicAlias:35,subscriptionIdentifier:11};z.propertiesCodes={};for(let t in z.properties){let e=z.properties[t];z.propertiesCodes[e]=t}z.propertiesTypes={sessionExpiryInterval:"int32",willDelayInterval:"int32",receiveMaximum:"int16",maximumPacketSize:"int32",topicAliasMaximum:"int16",requestResponseInformation:"byte",requestProblemInformation:"byte",userProperties:"pair",authenticationMethod:"string",authenticationData:"binary",payloadFormatIndicator:"byte",messageExpiryInterval:"int32",contentType:"string",responseTopic:"string",correlationData:"binary",maximumQoS:"int8",retainAvailable:"byte",assignedClientIdentifier:"string",reasonString:"string",wildcardSubscriptionAvailable:"byte",subscriptionIdentifiersAvailable:"byte",sharedSubscriptionAvailable:"byte",serverKeepAlive:"int16",responseInformation:"string",serverReference:"string",topicAlias:"int16",subscriptionIdentifier:"var"};function sr(t){return[0,1,2].map(e=>[0,1].map(r=>[0,1].map(n=>{let i=Ke.alloc(1);return i.writeUInt8(z.codes[t]<<z.CMD_SHIFT|(r?z.DUP_MASK:0)|e<<z.QOS_SHIFT|n,0,!0),i})))}z.PUBLISH_HEADER=sr("publish");z.SUBSCRIBE_HEADER=sr("subscribe");z.SUBSCRIBE_OPTIONS_QOS_MASK=3;z.SUBSCRIBE_OPTIONS_NL_MASK=1;z.SUBSCRIBE_OPTIONS_NL_SHIFT=2;z.SUBSCRIBE_OPTIONS_RAP_MASK=1;z.SUBSCRIBE_OPTIONS_RAP_SHIFT=3;z.SUBSCRIBE_OPTIONS_RH_MASK=3;z.SUBSCRIBE_OPTIONS_RH_SHIFT=4;z.SUBSCRIBE_OPTIONS_RH=[0,16,32];z.SUBSCRIBE_OPTIONS_NL=4;z.SUBSCRIBE_OPTIONS_RAP=8;z.SUBSCRIBE_OPTIONS_QOS=[0,1,2];z.UNSUBSCRIBE_HEADER=sr("unsubscribe");z.ACKS={unsuback:sr("unsuback"),puback:sr("puback"),pubcomp:sr("pubcomp"),pubrel:sr("pubrel"),pubrec:sr("pubrec")};z.SUBACK_HEADER=Ke.from([z.codes.suback<<z.CMD_SHIFT]);z.VERSION3=Ke.from([3]);z.VERSION4=Ke.from([4]);z.VERSION5=Ke.from([5]);z.VERSION131=Ke.from([131]);z.VERSION132=Ke.from([132]);z.QOS=[0,1,2].map(t=>Ke.from([t]));z.EMPTY={pingreq:Ke.from([z.codes.pingreq<<4,0]),pingresp:Ke.from([z.codes.pingresp<<4,0]),disconnect:Ke.from([z.codes.disconnect<<4,0])};z.MQTT5_PUBACK_PUBREC_CODES={0:"Success",16:"No matching subscribers",128:"Unspecified error",131:"Implementation specific error",135:"Not authorized",144:"Topic Name invalid",145:"Packet identifier in use",151:"Quota exceeded",153:"Payload format invalid"};z.MQTT5_PUBREL_PUBCOMP_CODES={0:"Success",146:"Packet Identifier not found"};z.MQTT5_SUBACK_CODES={0:"Granted QoS 0",1:"Granted QoS 1",2:"Granted QoS 2",128:"Unspecified error",131:"Implementation specific error",135:"Not authorized",143:"Topic Filter invalid",145:"Packet Identifier in use",151:"Quota exceeded",158:"Shared Subscriptions not supported",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};z.MQTT5_UNSUBACK_CODES={0:"Success",17:"No subscription existed",128:"Unspecified error",131:"Implementation specific error",135:"Not authorized",143:"Topic Filter invalid",145:"Packet Identifier in use"};z.MQTT5_DISCONNECT_CODES={0:"Normal disconnection",4:"Disconnect with Will Message",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",135:"Not authorized",137:"Server busy",139:"Server shutting down",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};z.MQTT5_AUTH_CODES={0:"Success",24:"Continue authentication",25:"Re-authenticate"}});var qp=D((o2,Np)=>{A();x();I();var fn=1e3,hn=fn*60,dn=hn*60,Cr=dn*24,MS=Cr*7,NS=Cr*365.25;Np.exports=function(t,e){e=e||{};var r=typeof t;if(r==="string"&&t.length>0)return qS(t);if(r==="number"&&isFinite(t))return e.long?US(t):LS(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))};function qS(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var r=parseFloat(e[1]),n=(e[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return r*NS;case"weeks":case"week":case"w":return r*MS;case"days":case"day":case"d":return r*Cr;case"hours":case"hour":case"hrs":case"hr":case"h":return r*dn;case"minutes":case"minute":case"mins":case"min":case"m":return r*hn;case"seconds":case"second":case"secs":case"sec":case"s":return r*fn;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}function LS(t){var e=Math.abs(t);return e>=Cr?Math.round(t/Cr)+"d":e>=dn?Math.round(t/dn)+"h":e>=hn?Math.round(t/hn)+"m":e>=fn?Math.round(t/fn)+"s":t+"ms"}function US(t){var e=Math.abs(t);return e>=Cr?ho(t,e,Cr,"day"):e>=dn?ho(t,e,dn,"hour"):e>=hn?ho(t,e,hn,"minute"):e>=fn?ho(t,e,fn,"second"):t+" ms"}function ho(t,e,r,n){var i=e>=r*1.5;return Math.round(t/r)+" "+n+(i?"s":"")}});var Up=D((y2,Lp)=>{A();x();I();function DS(t){r.debug=r,r.default=r,r.coerce=c,r.disable=o,r.enable=i,r.enabled=a,r.humanize=qp(),r.destroy=f,Object.keys(t).forEach(h=>{r[h]=t[h]}),r.names=[],r.skips=[],r.formatters={};function e(h){let d=0;for(let w=0;w<h.length;w++)d=(d<<5)-d+h.charCodeAt(w),d|=0;return r.colors[Math.abs(d)%r.colors.length]}r.selectColor=e;function r(h){let d,w=null,y,P;function E(...b){if(!E.enabled)return;let m=E,v=Number(new Date),R=v-(d||v);m.diff=R,m.prev=d,m.curr=v,d=v,b[0]=r.coerce(b[0]),typeof b[0]!="string"&&b.unshift("%O");let T=0;b[0]=b[0].replace(/%([a-zA-Z%])/g,(O,_)=>{if(O==="%%")return"%";T++;let q=r.formatters[_];if(typeof q=="function"){let $=b[T];O=q.call(m,$),b.splice(T,1),T--}return O}),r.formatArgs.call(m,b),(m.log||r.log).apply(m,b)}return E.namespace=h,E.useColors=r.useColors(),E.color=r.selectColor(h),E.extend=n,E.destroy=r.destroy,Object.defineProperty(E,"enabled",{enumerable:!0,configurable:!1,get:()=>w!==null?w:(y!==r.namespaces&&(y=r.namespaces,P=r.enabled(h)),P),set:b=>{w=b}}),typeof r.init=="function"&&r.init(E),E}function n(h,d){let w=r(this.namespace+(typeof d>"u"?":":d)+h);return w.log=this.log,w}function i(h){r.save(h),r.namespaces=h,r.names=[],r.skips=[];let d=(typeof h=="string"?h:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(let w of d)w[0]==="-"?r.skips.push(w.slice(1)):r.names.push(w)}function s(h,d){let w=0,y=0,P=-1,E=0;for(;w<h.length;)if(y<d.length&&(d[y]===h[w]||d[y]==="*"))d[y]==="*"?(P=y,E=w,y++):(w++,y++);else if(P!==-1)y=P+1,E++,w=E;else return!1;for(;y<d.length&&d[y]==="*";)y++;return y===d.length}function o(){let h=[...r.names,...r.skips.map(d=>"-"+d)].join(",");return r.enable(""),h}function a(h){for(let d of r.skips)if(s(h,d))return!1;for(let d of r.names)if(s(h,d))return!0;return!1}function c(h){return h instanceof Error?h.stack||h.message:h}function f(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return r.enable(r.load()),r}Lp.exports=DS});var At=D((Qe,po)=>{A();x();I();Qe.formatArgs=FS;Qe.save=WS;Qe.load=$S;Qe.useColors=jS;Qe.storage=HS();Qe.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();Qe.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function jS(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let t;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(t=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(t[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function FS(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+po.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;t.splice(1,0,e,"color: inherit");let r=0,n=0;t[0].replace(/%[a-zA-Z%]/g,i=>{i!=="%%"&&(r++,i==="%c"&&(n=r))}),t.splice(n,0,e)}Qe.log=console.debug||console.log||(()=>{});function WS(t){try{t?Qe.storage.setItem("debug",t):Qe.storage.removeItem("debug")}catch{}}function $S(){let t;try{t=Qe.storage.getItem("debug")||Qe.storage.getItem("DEBUG")}catch{}return!t&&typeof M<"u"&&"env"in M&&(t=M.env.DEBUG),t}function HS(){try{return localStorage}catch{}}po.exports=Up()(Qe);var{formatters:VS}=po.exports;VS.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var Fp=D((M2,jp)=>{A();x();I();var GS=kp(),{EventEmitter:zS}=(er(),me(Zt)),Dp=Bp(),de=Pa(),ae=At()("mqtt-packet:parser"),Oa=class t extends zS{constructor(){super(),this.parser=this.constructor.parser}static parser(e){return this instanceof t?(this.settings=e||{},this._states=["_parseHeader","_parseLength","_parsePayload","_newPacket"],this._resetState(),this):new t().parser(e)}_resetState(){ae("_resetState: resetting packet, error, _list, and _stateCounter"),this.packet=new Dp,this.error=null,this._list=GS(),this._stateCounter=0}parse(e){for(this.error&&this._resetState(),this._list.append(e),ae("parse: current state: %s",this._states[this._stateCounter]);(this.packet.length!==-1||this._list.length>0)&&this[this._states[this._stateCounter]]()&&!this.error;)this._stateCounter++,ae("parse: state complete. _stateCounter is now: %d",this._stateCounter),ae("parse: packet.length: %d, buffer list length: %d",this.packet.length,this._list.length),this._stateCounter>=this._states.length&&(this._stateCounter=0);return ae("parse: exited while loop. packet: %d, buffer list length: %d",this.packet.length,this._list.length),this._list.length}_parseHeader(){let e=this._list.readUInt8(0),r=e>>de.CMD_SHIFT;this.packet.cmd=de.types[r];let n=e&15,i=de.requiredHeaderFlags[r];return i!=null&&n!==i?this._emitError(new Error(de.requiredHeaderFlagsErrors[r])):(this.packet.retain=(e&de.RETAIN_MASK)!==0,this.packet.qos=e>>de.QOS_SHIFT&de.QOS_MASK,this.packet.qos>2?this._emitError(new Error("Packet must not have both QoS bits set to 1")):(this.packet.dup=(e&de.DUP_MASK)!==0,ae("_parseHeader: packet: %o",this.packet),this._list.consume(1),!0))}_parseLength(){let e=this._parseVarByteNum(!0);return e&&(this.packet.length=e.value,this._list.consume(e.bytes)),ae("_parseLength %d",e.value),!!e}_parsePayload(){ae("_parsePayload: payload %O",this._list);let e=!1;if(this.packet.length===0||this._list.length>=this.packet.length){switch(this._pos=0,this.packet.cmd){case"connect":this._parseConnect();break;case"connack":this._parseConnack();break;case"publish":this._parsePublish();break;case"puback":case"pubrec":case"pubrel":case"pubcomp":this._parseConfirmation();break;case"subscribe":this._parseSubscribe();break;case"suback":this._parseSuback();break;case"unsubscribe":this._parseUnsubscribe();break;case"unsuback":this._parseUnsuback();break;case"pingreq":case"pingresp":break;case"disconnect":this._parseDisconnect();break;case"auth":this._parseAuth();break;default:this._emitError(new Error("Not supported"))}e=!0}return ae("_parsePayload complete result: %s",e),e}_parseConnect(){ae("_parseConnect");let e,r,n,i,s={},o=this.packet,a=this._parseString();if(a===null)return this._emitError(new Error("Cannot parse protocolId"));if(a!=="MQTT"&&a!=="MQIsdp")return this._emitError(new Error("Invalid protocolId"));if(o.protocolId=a,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(o.protocolVersion=this._list.readUInt8(this._pos),o.protocolVersion>=128&&(o.bridgeMode=!0,o.protocolVersion=o.protocolVersion-128),o.protocolVersion!==3&&o.protocolVersion!==4&&o.protocolVersion!==5)return this._emitError(new Error("Invalid protocol version"));if(this._pos++,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(this._list.readUInt8(this._pos)&1)return this._emitError(new Error("Connect flag bit 0 must be 0, but got 1"));s.username=this._list.readUInt8(this._pos)&de.USERNAME_MASK,s.password=this._list.readUInt8(this._pos)&de.PASSWORD_MASK,s.will=this._list.readUInt8(this._pos)&de.WILL_FLAG_MASK;let c=!!(this._list.readUInt8(this._pos)&de.WILL_RETAIN_MASK),f=(this._list.readUInt8(this._pos)&de.WILL_QOS_MASK)>>de.WILL_QOS_SHIFT;if(s.will)o.will={},o.will.retain=c,o.will.qos=f;else{if(c)return this._emitError(new Error("Will Retain Flag must be set to zero when Will Flag is set to 0"));if(f)return this._emitError(new Error("Will QoS must be set to zero when Will Flag is set to 0"))}if(o.clean=(this._list.readUInt8(this._pos)&de.CLEAN_SESSION_MASK)!==0,this._pos++,o.keepalive=this._parseNum(),o.keepalive===-1)return this._emitError(new Error("Packet too short"));if(o.protocolVersion===5){let d=this._parseProperties();Object.getOwnPropertyNames(d).length&&(o.properties=d)}let h=this._parseString();if(h===null)return this._emitError(new Error("Packet too short"));if(o.clientId=h,ae("_parseConnect: packet.clientId: %s",o.clientId),s.will){if(o.protocolVersion===5){let d=this._parseProperties();Object.getOwnPropertyNames(d).length&&(o.will.properties=d)}if(e=this._parseString(),e===null)return this._emitError(new Error("Cannot parse will topic"));if(o.will.topic=e,ae("_parseConnect: packet.will.topic: %s",o.will.topic),r=this._parseBuffer(),r===null)return this._emitError(new Error("Cannot parse will payload"));o.will.payload=r,ae("_parseConnect: packet.will.paylaod: %s",o.will.payload)}if(s.username){if(i=this._parseString(),i===null)return this._emitError(new Error("Cannot parse username"));o.username=i,ae("_parseConnect: packet.username: %s",o.username)}if(s.password){if(n=this._parseBuffer(),n===null)return this._emitError(new Error("Cannot parse password"));o.password=n}return this.settings=o,ae("_parseConnect: complete"),o}_parseConnack(){ae("_parseConnack");let e=this.packet;if(this._list.length<1)return null;let r=this._list.readUInt8(this._pos++);if(r>1)return this._emitError(new Error("Invalid connack flags, bits 7-1 must be set to 0"));if(e.sessionPresent=!!(r&de.SESSIONPRESENT_MASK),this.settings.protocolVersion===5)this._list.length>=2?e.reasonCode=this._list.readUInt8(this._pos++):e.reasonCode=0;else{if(this._list.length<2)return null;e.returnCode=this._list.readUInt8(this._pos++)}if(e.returnCode===-1||e.reasonCode===-1)return this._emitError(new Error("Cannot parse return code"));if(this.settings.protocolVersion===5){let n=this._parseProperties();Object.getOwnPropertyNames(n).length&&(e.properties=n)}ae("_parseConnack: complete")}_parsePublish(){ae("_parsePublish");let e=this.packet;if(e.topic=this._parseString(),e.topic===null)return this._emitError(new Error("Cannot parse topic"));if(!(e.qos>0&&!this._parseMessageId())){if(this.settings.protocolVersion===5){let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}e.payload=this._list.slice(this._pos,e.length),ae("_parsePublish: payload from buffer list: %o",e.payload)}}_parseSubscribe(){ae("_parseSubscribe");let e=this.packet,r,n,i,s,o,a,c;if(e.subscriptions=[],!!this._parseMessageId()){if(this.settings.protocolVersion===5){let f=this._parseProperties();Object.getOwnPropertyNames(f).length&&(e.properties=f)}if(e.length<=0)return this._emitError(new Error("Malformed subscribe, no payload specified"));for(;this._pos<e.length;){if(r=this._parseString(),r===null)return this._emitError(new Error("Cannot parse topic"));if(this._pos>=e.length)return this._emitError(new Error("Malformed Subscribe Payload"));if(n=this._parseByte(),this.settings.protocolVersion===5){if(n&192)return this._emitError(new Error("Invalid subscribe topic flag bits, bits 7-6 must be 0"))}else if(n&252)return this._emitError(new Error("Invalid subscribe topic flag bits, bits 7-2 must be 0"));if(i=n&de.SUBSCRIBE_OPTIONS_QOS_MASK,i>2)return this._emitError(new Error("Invalid subscribe QoS, must be <= 2"));if(a=(n>>de.SUBSCRIBE_OPTIONS_NL_SHIFT&de.SUBSCRIBE_OPTIONS_NL_MASK)!==0,o=(n>>de.SUBSCRIBE_OPTIONS_RAP_SHIFT&de.SUBSCRIBE_OPTIONS_RAP_MASK)!==0,s=n>>de.SUBSCRIBE_OPTIONS_RH_SHIFT&de.SUBSCRIBE_OPTIONS_RH_MASK,s>2)return this._emitError(new Error("Invalid retain handling, must be <= 2"));c={topic:r,qos:i},this.settings.protocolVersion===5?(c.nl=a,c.rap=o,c.rh=s):this.settings.bridgeMode&&(c.rh=0,c.rap=!0,c.nl=!0),ae("_parseSubscribe: push subscription `%s` to subscription",c),e.subscriptions.push(c)}}}_parseSuback(){ae("_parseSuback");let e=this.packet;if(this.packet.granted=[],!!this._parseMessageId()){if(this.settings.protocolVersion===5){let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}if(e.length<=0)return this._emitError(new Error("Malformed suback, no payload specified"));for(;this._pos<this.packet.length;){let r=this._list.readUInt8(this._pos++);if(this.settings.protocolVersion===5){if(!de.MQTT5_SUBACK_CODES[r])return this._emitError(new Error("Invalid suback code"))}else if(r>2&&r!==128)return this._emitError(new Error("Invalid suback QoS, must be 0, 1, 2 or 128"));this.packet.granted.push(r)}}}_parseUnsubscribe(){ae("_parseUnsubscribe");let e=this.packet;if(e.unsubscriptions=[],!!this._parseMessageId()){if(this.settings.protocolVersion===5){let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}if(e.length<=0)return this._emitError(new Error("Malformed unsubscribe, no payload specified"));for(;this._pos<e.length;){let r=this._parseString();if(r===null)return this._emitError(new Error("Cannot parse topic"));ae("_parseUnsubscribe: push topic `%s` to unsubscriptions",r),e.unsubscriptions.push(r)}}}_parseUnsuback(){ae("_parseUnsuback");let e=this.packet;if(!this._parseMessageId())return this._emitError(new Error("Cannot parse messageId"));if((this.settings.protocolVersion===3||this.settings.protocolVersion===4)&&e.length!==2)return this._emitError(new Error("Malformed unsuback, payload length must be 2"));if(e.length<=0)return this._emitError(new Error("Malformed unsuback, no payload specified"));if(this.settings.protocolVersion===5){let r=this._parseProperties();for(Object.getOwnPropertyNames(r).length&&(e.properties=r),e.granted=[];this._pos<this.packet.length;){let n=this._list.readUInt8(this._pos++);if(!de.MQTT5_UNSUBACK_CODES[n])return this._emitError(new Error("Invalid unsuback code"));this.packet.granted.push(n)}}}_parseConfirmation(){ae("_parseConfirmation: packet.cmd: `%s`",this.packet.cmd);let e=this.packet;if(this._parseMessageId(),this.settings.protocolVersion===5){if(e.length>2){switch(e.reasonCode=this._parseByte(),this.packet.cmd){case"puback":case"pubrec":if(!de.MQTT5_PUBACK_PUBREC_CODES[e.reasonCode])return this._emitError(new Error("Invalid "+this.packet.cmd+" reason code"));break;case"pubrel":case"pubcomp":if(!de.MQTT5_PUBREL_PUBCOMP_CODES[e.reasonCode])return this._emitError(new Error("Invalid "+this.packet.cmd+" reason code"));break}ae("_parseConfirmation: packet.reasonCode `%d`",e.reasonCode)}else e.reasonCode=0;if(e.length>3){let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}}return!0}_parseDisconnect(){let e=this.packet;if(ae("_parseDisconnect"),this.settings.protocolVersion===5){this._list.length>0?(e.reasonCode=this._parseByte(),de.MQTT5_DISCONNECT_CODES[e.reasonCode]||this._emitError(new Error("Invalid disconnect reason code"))):e.reasonCode=0;let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}return ae("_parseDisconnect result: true"),!0}_parseAuth(){ae("_parseAuth");let e=this.packet;if(this.settings.protocolVersion!==5)return this._emitError(new Error("Not supported auth packet for this version MQTT"));if(e.reasonCode=this._parseByte(),!de.MQTT5_AUTH_CODES[e.reasonCode])return this._emitError(new Error("Invalid auth reason code"));let r=this._parseProperties();return Object.getOwnPropertyNames(r).length&&(e.properties=r),ae("_parseAuth: result: true"),!0}_parseMessageId(){let e=this.packet;return e.messageId=this._parseNum(),e.messageId===null?(this._emitError(new Error("Cannot parse messageId")),!1):(ae("_parseMessageId: packet.messageId %d",e.messageId),!0)}_parseString(e){let r=this._parseNum(),n=r+this._pos;if(r===-1||n>this._list.length||n>this.packet.length)return null;let i=this._list.toString("utf8",this._pos,n);return this._pos+=r,ae("_parseString: result: %s",i),i}_parseStringPair(){return ae("_parseStringPair"),{name:this._parseString(),value:this._parseString()}}_parseBuffer(){let e=this._parseNum(),r=e+this._pos;if(e===-1||r>this._list.length||r>this.packet.length)return null;let n=this._list.slice(this._pos,r);return this._pos+=e,ae("_parseBuffer: result: %o",n),n}_parseNum(){if(this._list.length-this._pos<2)return-1;let e=this._list.readUInt16BE(this._pos);return this._pos+=2,ae("_parseNum: result: %s",e),e}_parse4ByteNum(){if(this._list.length-this._pos<4)return-1;let e=this._list.readUInt32BE(this._pos);return this._pos+=4,ae("_parse4ByteNum: result: %s",e),e}_parseVarByteNum(e){ae("_parseVarByteNum");let r=4,n=0,i=1,s=0,o=!1,a,c=this._pos?this._pos:0;for(;n<r&&c+n<this._list.length;){if(a=this._list.readUInt8(c+n++),s+=i*(a&de.VARBYTEINT_MASK),i*=128,(a&de.VARBYTEINT_FIN_MASK)===0){o=!0;break}if(this._list.length<=n)break}return!o&&n===r&&this._list.length>=n&&this._emitError(new Error("Invalid variable byte integer")),c&&(this._pos+=n),o?e?o={bytes:n,value:s}:o=s:o=!1,ae("_parseVarByteNum: result: %o",o),o}_parseByte(){let e;return this._pos<this._list.length&&(e=this._list.readUInt8(this._pos),this._pos++),ae("_parseByte: result: %o",e),e}_parseByType(e){switch(ae("_parseByType: type: %s",e),e){case"byte":return this._parseByte()!==0;case"int8":return this._parseByte();case"int16":return this._parseNum();case"int32":return this._parse4ByteNum();case"var":return this._parseVarByteNum();case"string":return this._parseString();case"pair":return this._parseStringPair();case"binary":return this._parseBuffer()}}_parseProperties(){ae("_parseProperties");let e=this._parseVarByteNum(),n=this._pos+e,i={};for(;this._pos<n;){let s=this._parseByte();if(!s)return this._emitError(new Error("Cannot parse property code type")),!1;let o=de.propertiesCodes[s];if(!o)return this._emitError(new Error("Unknown property")),!1;if(o==="userProperties"){i[o]||(i[o]=Object.create(null));let a=this._parseByType(de.propertiesTypes[o]);if(i[o][a.name])if(Array.isArray(i[o][a.name]))i[o][a.name].push(a.value);else{let c=i[o][a.name];i[o][a.name]=[c],i[o][a.name].push(a.value)}else i[o][a.name]=a.value;continue}i[o]?Array.isArray(i[o])?i[o].push(this._parseByType(de.propertiesTypes[o])):(i[o]=[i[o]],i[o].push(this._parseByType(de.propertiesTypes[o]))):i[o]=this._parseByType(de.propertiesTypes[o])}return i}_newPacket(){return ae("_newPacket"),this.packet&&(this._list.consume(this.packet.length),ae("_newPacket: parser emit packet: packet.cmd: %s, packet.payload: %s, packet.length: %d",this.packet.cmd,this.packet.payload,this.packet.length),this.emit("packet",this.packet)),ae("_newPacket: new packet"),this.packet=new Dp,this._pos=0,!0}_emitError(e){ae("_emitError",e),this.error=e,this.emit("error",e)}};jp.exports=Oa});var Vp=D((H2,Hp)=>{A();x();I();var{Buffer:Hn}=(ke(),me(qe)),KS=65536,Wp={},QS=Hn.isBuffer(Hn.from([1,2]).subarray(0,1));function $p(t){let e=Hn.allocUnsafe(2);return e.writeUInt8(t>>8,0),e.writeUInt8(t&255,1),e}function YS(){for(let t=0;t<KS;t++)Wp[t]=$p(t)}function JS(t){let r=0,n=0,i=Hn.allocUnsafe(4);do r=t%128|0,t=t/128|0,t>0&&(r=r|128),i.writeUInt8(r,n++);while(t>0&&n<4);return t>0&&(n=0),QS?i.subarray(0,n):i.slice(0,n)}function XS(t){let e=Hn.allocUnsafe(4);return e.writeUInt32BE(t,0),e}Hp.exports={cache:Wp,generateCache:YS,generateNumber:$p,genBufVariableByteInt:JS,generate4ByteBuffer:XS}});var Gp=D((eN,Ra)=>{"use strict";A();x();I();typeof M>"u"||!M.version||M.version.indexOf("v0.")===0||M.version.indexOf("v1.")===0&&M.version.indexOf("v1.8.")!==0?Ra.exports={nextTick:ZS}:Ra.exports=M;function ZS(t,e,r,n){if(typeof t!="function")throw new TypeError('"callback" argument must be a function');var i=arguments.length,s,o;switch(i){case 0:case 1:return M.nextTick(t);case 2:return M.nextTick(function(){t.call(null,e)});case 3:return M.nextTick(function(){t.call(null,e,r)});case 4:return M.nextTick(function(){t.call(null,e,r,n)});default:for(s=new Array(i-1),o=0;o<s.length;)s[o++]=arguments[o];return M.nextTick(function(){t.apply(null,s)})}}});var Ba=D((cN,ey)=>{A();x();I();var ue=Pa(),{Buffer:se}=(ke(),me(qe)),eE=se.allocUnsafe(0),tE=se.from([0]),Vn=Vp(),rE=Gp().nextTick,rt=At()("mqtt-packet:writeToStream"),yo=Vn.cache,nE=Vn.generateNumber,iE=Vn.generateCache,ka=Vn.genBufVariableByteInt,oE=Vn.generate4ByteBuffer,He=Ca,go=!0;function Xp(t,e,r){switch(rt("generate called"),e.cork&&(e.cork(),rE(sE,e)),go&&(go=!1,iE()),rt("generate: packet.cmd: %s",t.cmd),t.cmd){case"connect":return aE(t,e,r);case"connack":return uE(t,e,r);case"publish":return lE(t,e,r);case"puback":case"pubrec":case"pubrel":case"pubcomp":return cE(t,e,r);case"subscribe":return fE(t,e,r);case"suback":return hE(t,e,r);case"unsubscribe":return dE(t,e,r);case"unsuback":return pE(t,e,r);case"pingreq":case"pingresp":return yE(t,e,r);case"disconnect":return gE(t,e,r);case"auth":return bE(t,e,r);default:return e.destroy(new Error("Unknown command")),!1}}Object.defineProperty(Xp,"cacheNumbers",{get(){return He===Ca},set(t){t?((!yo||Object.keys(yo).length===0)&&(go=!0),He=Ca):(go=!1,He=wE)}});function sE(t){t.uncork()}function aE(t,e,r){let n=t||{},i=n.protocolId||"MQTT",s=n.protocolVersion||4,o=n.will,a=n.clean,c=n.keepalive||0,f=n.clientId||"",h=n.username,d=n.password,w=n.properties;a===void 0&&(a=!0);let y=0;if(!i||typeof i!="string"&&!se.isBuffer(i))return e.destroy(new Error("Invalid protocolId")),!1;if(y+=i.length+2,s!==3&&s!==4&&s!==5)return e.destroy(new Error("Invalid protocol version")),!1;if(y+=1,(typeof f=="string"||se.isBuffer(f))&&(f||s>=4)&&(f||a))y+=se.byteLength(f)+2;else{if(s<4)return e.destroy(new Error("clientId must be supplied before 3.1.1")),!1;if(a*1===0)return e.destroy(new Error("clientId must be given if cleanSession set to 0")),!1}if(typeof c!="number"||c<0||c>65535||c%1!==0)return e.destroy(new Error("Invalid keepalive")),!1;y+=2,y+=1;let P,E;if(s===5){if(P=ar(e,w),!P)return!1;y+=P.length}if(o){if(typeof o!="object")return e.destroy(new Error("Invalid will")),!1;if(!o.topic||typeof o.topic!="string")return e.destroy(new Error("Invalid will topic")),!1;if(y+=se.byteLength(o.topic)+2,y+=2,o.payload)if(o.payload.length>=0)typeof o.payload=="string"?y+=se.byteLength(o.payload):y+=o.payload.length;else return e.destroy(new Error("Invalid will payload")),!1;if(E={},s===5){if(E=ar(e,o.properties),!E)return!1;y+=E.length}}let b=!1;if(h!=null)if(Jp(h))b=!0,y+=se.byteLength(h)+2;else return e.destroy(new Error("Invalid username")),!1;if(d!=null){if(!b)return e.destroy(new Error("Username is required to use password")),!1;if(Jp(d))y+=Zp(d)+2;else return e.destroy(new Error("Invalid password")),!1}e.write(ue.CONNECT_HEADER),nt(e,y),pn(e,i),n.bridgeMode&&(s+=128),e.write(s===131?ue.VERSION131:s===132?ue.VERSION132:s===4?ue.VERSION4:s===5?ue.VERSION5:ue.VERSION3);let m=0;return m|=h!=null?ue.USERNAME_MASK:0,m|=d!=null?ue.PASSWORD_MASK:0,m|=o&&o.retain?ue.WILL_RETAIN_MASK:0,m|=o&&o.qos?o.qos<<ue.WILL_QOS_SHIFT:0,m|=o?ue.WILL_FLAG_MASK:0,m|=a?ue.CLEAN_SESSION_MASK:0,e.write(se.from([m])),He(e,c),s===5&&P.write(),pn(e,f),o&&(s===5&&E.write(),Br(e,o.topic),pn(e,o.payload)),h!=null&&pn(e,h),d!=null&&pn(e,d),!0}function uE(t,e,r){let n=r?r.protocolVersion:4,i=t||{},s=n===5?i.reasonCode:i.returnCode,o=i.properties,a=2;if(typeof s!="number")return e.destroy(new Error("Invalid return code")),!1;let c=null;if(n===5){if(c=ar(e,o),!c)return!1;a+=c.length}return e.write(ue.CONNACK_HEADER),nt(e,a),e.write(i.sessionPresent?ue.SESSIONPRESENT_HEADER:tE),e.write(se.from([s])),c?.write(),!0}function lE(t,e,r){rt("publish: packet: %o",t);let n=r?r.protocolVersion:4,i=t||{},s=i.qos||0,o=i.retain?ue.RETAIN_MASK:0,a=i.topic,c=i.payload||eE,f=i.messageId,h=i.properties,d=0;if(typeof a=="string")d+=se.byteLength(a)+2;else if(se.isBuffer(a))d+=a.length+2;else return e.destroy(new Error("Invalid topic")),!1;if(se.isBuffer(c)?d+=c.length:d+=se.byteLength(c),s&&typeof f!="number")return e.destroy(new Error("Invalid messageId")),!1;s&&(d+=2);let w=null;if(n===5){if(w=ar(e,h),!w)return!1;d+=w.length}return e.write(ue.PUBLISH_HEADER[s][i.dup?1:0][o?1:0]),nt(e,d),He(e,Zp(a)),e.write(a),s>0&&He(e,f),w?.write(),rt("publish: payload: %o",c),e.write(c)}function cE(t,e,r){let n=r?r.protocolVersion:4,i=t||{},s=i.cmd||"puback",o=i.messageId,a=i.dup&&s==="pubrel"?ue.DUP_MASK:0,c=0,f=i.reasonCode,h=i.properties,d=n===5?3:2;if(s==="pubrel"&&(c=1),typeof o!="number")return e.destroy(new Error("Invalid messageId")),!1;let w=null;if(n===5&&typeof h=="object"){if(w=Gn(e,h,r,d),!w)return!1;d+=w.length}return e.write(ue.ACKS[s][c][a][0]),d===3&&(d+=f!==0?1:-1),nt(e,d),He(e,o),n===5&&d!==2&&e.write(se.from([f])),w!==null?w.write():d===4&&e.write(se.from([0])),!0}function fE(t,e,r){rt("subscribe: packet: ");let n=r?r.protocolVersion:4,i=t||{},s=i.dup?ue.DUP_MASK:0,o=i.messageId,a=i.subscriptions,c=i.properties,f=0;if(typeof o!="number")return e.destroy(new Error("Invalid messageId")),!1;f+=2;let h=null;if(n===5){if(h=ar(e,c),!h)return!1;f+=h.length}if(typeof a=="object"&&a.length)for(let w=0;w<a.length;w+=1){let y=a[w].topic,P=a[w].qos;if(typeof y!="string")return e.destroy(new Error("Invalid subscriptions - invalid topic")),!1;if(typeof P!="number")return e.destroy(new Error("Invalid subscriptions - invalid qos")),!1;if(n===5){if(typeof(a[w].nl||!1)!="boolean")return e.destroy(new Error("Invalid subscriptions - invalid No Local")),!1;if(typeof(a[w].rap||!1)!="boolean")return e.destroy(new Error("Invalid subscriptions - invalid Retain as Published")),!1;let m=a[w].rh||0;if(typeof m!="number"||m>2)return e.destroy(new Error("Invalid subscriptions - invalid Retain Handling")),!1}f+=se.byteLength(y)+2+1}else return e.destroy(new Error("Invalid subscriptions")),!1;rt("subscribe: writing to stream: %o",ue.SUBSCRIBE_HEADER),e.write(ue.SUBSCRIBE_HEADER[1][s?1:0][0]),nt(e,f),He(e,o),h!==null&&h.write();let d=!0;for(let w of a){let y=w.topic,P=w.qos,E=+w.nl,b=+w.rap,m=w.rh,v;Br(e,y),v=ue.SUBSCRIBE_OPTIONS_QOS[P],n===5&&(v|=E?ue.SUBSCRIBE_OPTIONS_NL:0,v|=b?ue.SUBSCRIBE_OPTIONS_RAP:0,v|=m?ue.SUBSCRIBE_OPTIONS_RH[m]:0),d=e.write(se.from([v]))}return d}function hE(t,e,r){let n=r?r.protocolVersion:4,i=t||{},s=i.messageId,o=i.granted,a=i.properties,c=0;if(typeof s!="number")return e.destroy(new Error("Invalid messageId")),!1;if(c+=2,typeof o=="object"&&o.length)for(let h=0;h<o.length;h+=1){if(typeof o[h]!="number")return e.destroy(new Error("Invalid qos vector")),!1;c+=1}else return e.destroy(new Error("Invalid qos vector")),!1;let f=null;if(n===5){if(f=Gn(e,a,r,c),!f)return!1;c+=f.length}return e.write(ue.SUBACK_HEADER),nt(e,c),He(e,s),f!==null&&f.write(),e.write(se.from(o))}function dE(t,e,r){let n=r?r.protocolVersion:4,i=t||{},s=i.messageId,o=i.dup?ue.DUP_MASK:0,a=i.unsubscriptions,c=i.properties,f=0;if(typeof s!="number")return e.destroy(new Error("Invalid messageId")),!1;if(f+=2,typeof a=="object"&&a.length)for(let w=0;w<a.length;w+=1){if(typeof a[w]!="string")return e.destroy(new Error("Invalid unsubscriptions")),!1;f+=se.byteLength(a[w])+2}else return e.destroy(new Error("Invalid unsubscriptions")),!1;let h=null;if(n===5){if(h=ar(e,c),!h)return!1;f+=h.length}e.write(ue.UNSUBSCRIBE_HEADER[1][o?1:0][0]),nt(e,f),He(e,s),h!==null&&h.write();let d=!0;for(let w=0;w<a.length;w++)d=Br(e,a[w]);return d}function pE(t,e,r){let n=r?r.protocolVersion:4,i=t||{},s=i.messageId,o=i.dup?ue.DUP_MASK:0,a=i.granted,c=i.properties,f=i.cmd,h=0,d=2;if(typeof s!="number")return e.destroy(new Error("Invalid messageId")),!1;if(n===5)if(typeof a=="object"&&a.length)for(let y=0;y<a.length;y+=1){if(typeof a[y]!="number")return e.destroy(new Error("Invalid qos vector")),!1;d+=1}else return e.destroy(new Error("Invalid qos vector")),!1;let w=null;if(n===5){if(w=Gn(e,c,r,d),!w)return!1;d+=w.length}return e.write(ue.ACKS[f][h][o][0]),nt(e,d),He(e,s),w!==null&&w.write(),n===5&&e.write(se.from(a)),!0}function yE(t,e,r){return e.write(ue.EMPTY[t.cmd])}function gE(t,e,r){let n=r?r.protocolVersion:4,i=t||{},s=i.reasonCode,o=i.properties,a=n===5?1:0,c=null;if(n===5){if(c=Gn(e,o,r,a),!c)return!1;a+=c.length}return e.write(se.from([ue.codes.disconnect<<4])),nt(e,a),n===5&&e.write(se.from([s])),c!==null&&c.write(),!0}function bE(t,e,r){let n=r?r.protocolVersion:4,i=t||{},s=i.reasonCode,o=i.properties,a=n===5?1:0;n!==5&&e.destroy(new Error("Invalid mqtt version for auth packet"));let c=Gn(e,o,r,a);return c?(a+=c.length,e.write(se.from([ue.codes.auth<<4])),nt(e,a),e.write(se.from([s])),c!==null&&c.write(),!0):!1}var zp={};function nt(t,e){if(e>ue.VARBYTEINT_MAX)return t.destroy(new Error(`Invalid variable byte integer: ${e}`)),!1;let r=zp[e];return r||(r=ka(e),e<16384&&(zp[e]=r)),rt("writeVarByteInt: writing to stream: %o",r),t.write(r)}function Br(t,e){let r=se.byteLength(e);return He(t,r),rt("writeString: %s",e),t.write(e,"utf8")}function Kp(t,e,r){Br(t,e),Br(t,r)}function Ca(t,e){return rt("writeNumberCached: number: %d",e),rt("writeNumberCached: %o",yo[e]),t.write(yo[e])}function wE(t,e){let r=nE(e);return rt("writeNumberGenerated: %o",r),t.write(r)}function mE(t,e){let r=oE(e);return rt("write4ByteNumber: %o",r),t.write(r)}function pn(t,e){typeof e=="string"?Br(t,e):e?(He(t,e.length),t.write(e)):He(t,0)}function ar(t,e){if(typeof e!="object"||e.length!=null)return{length:1,write(){Yp(t,{},0)}};let r=0;function n(s,o){let a=ue.propertiesTypes[s],c=0;switch(a){case"byte":{if(typeof o!="boolean")return t.destroy(new Error(`Invalid ${s}: ${o}`)),!1;c+=2;break}case"int8":{if(typeof o!="number"||o<0||o>255)return t.destroy(new Error(`Invalid ${s}: ${o}`)),!1;c+=2;break}case"binary":{if(o&&o===null)return t.destroy(new Error(`Invalid ${s}: ${o}`)),!1;c+=1+se.byteLength(o)+2;break}case"int16":{if(typeof o!="number"||o<0||o>65535)return t.destroy(new Error(`Invalid ${s}: ${o}`)),!1;c+=3;break}case"int32":{if(typeof o!="number"||o<0||o>4294967295)return t.destroy(new Error(`Invalid ${s}: ${o}`)),!1;c+=5;break}case"var":{if(typeof o!="number"||o<0||o>268435455)return t.destroy(new Error(`Invalid ${s}: ${o}`)),!1;c+=1+se.byteLength(ka(o));break}case"string":{if(typeof o!="string")return t.destroy(new Error(`Invalid ${s}: ${o}`)),!1;c+=3+se.byteLength(o.toString());break}case"pair":{if(typeof o!="object")return t.destroy(new Error(`Invalid ${s}: ${o}`)),!1;c+=Object.getOwnPropertyNames(o).reduce((f,h)=>{let d=o[h];return Array.isArray(d)?f+=d.reduce((w,y)=>(w+=3+se.byteLength(h.toString())+2+se.byteLength(y.toString()),w),0):f+=3+se.byteLength(h.toString())+2+se.byteLength(o[h].toString()),f},0);break}default:return t.destroy(new Error(`Invalid property ${s}: ${o}`)),!1}return c}if(e)for(let s in e){let o=0,a=0,c=e[s];if(c!==void 0){if(Array.isArray(c))for(let f=0;f<c.length;f++){if(a=n(s,c[f]),!a)return!1;o+=a}else{if(a=n(s,c),!a)return!1;o=a}if(!o)return!1;r+=o}}return{length:se.byteLength(ka(r))+r,write(){Yp(t,e,r)}}}function Gn(t,e,r,n){let i=["reasonString","userProperties"],s=r&&r.properties&&r.properties.maximumPacketSize?r.properties.maximumPacketSize:0,o=ar(t,e);if(s)for(;n+o.length>s;){let a=i.shift();if(a&&e[a])delete e[a],o=ar(t,e);else return!1}return o}function Qp(t,e,r){switch(ue.propertiesTypes[e]){case"byte":{t.write(se.from([ue.properties[e]])),t.write(se.from([+r]));break}case"int8":{t.write(se.from([ue.properties[e]])),t.write(se.from([r]));break}case"binary":{t.write(se.from([ue.properties[e]])),pn(t,r);break}case"int16":{t.write(se.from([ue.properties[e]])),He(t,r);break}case"int32":{t.write(se.from([ue.properties[e]])),mE(t,r);break}case"var":{t.write(se.from([ue.properties[e]])),nt(t,r);break}case"string":{t.write(se.from([ue.properties[e]])),Br(t,r);break}case"pair":{Object.getOwnPropertyNames(r).forEach(i=>{let s=r[i];Array.isArray(s)?s.forEach(o=>{t.write(se.from([ue.properties[e]])),Kp(t,i.toString(),o.toString())}):(t.write(se.from([ue.properties[e]])),Kp(t,i.toString(),s.toString()))});break}default:return t.destroy(new Error(`Invalid property ${e} value: ${r}`)),!1}}function Yp(t,e,r){nt(t,r);for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&e[n]!=null){let i=e[n];if(Array.isArray(i))for(let s=0;s<i.length;s++)Qp(t,n,i[s]);else Qp(t,n,i)}}function Zp(t){return t?t instanceof se?t.length:se.byteLength(t):0}function Jp(t){return typeof t=="string"||t instanceof se}ey.exports=Xp});var ny=D((_N,ry)=>{A();x();I();var _E=Ba(),{EventEmitter:vE}=(er(),me(Zt)),{Buffer:ty}=(ke(),me(qe));function SE(t,e){let r=new Ma;return _E(t,r,e),r.concat()}var Ma=class extends vE{constructor(){super(),this._array=new Array(20),this._i=0}write(e){return this._array[this._i++]=e,!0}concat(){let e=0,r=new Array(this._array.length),n=this._array,i=0,s;for(s=0;s<n.length&&n[s]!==void 0;s++)typeof n[s]!="string"?r[s]=n[s].length:r[s]=ty.byteLength(n[s]),e+=r[s];let o=ty.allocUnsafe(e);for(s=0;s<n.length&&n[s]!==void 0;s++)typeof n[s]!="string"?(n[s].copy(o,i),i+=r[s]):(o.write(n[s],i),i+=r[s]);return o}destroy(e){e&&this.emit("error",e)}};ry.exports=SE});var iy=D(bo=>{A();x();I();bo.parser=Fp().parser;bo.generate=ny();bo.writeToStream=Ba()});var sy=D((jN,oy)=>{"use strict";A();x();I();oy.exports=EE;function yn(t){return t instanceof U?U.from(t):new t.constructor(t.buffer.slice(),t.byteOffset,t.length)}function EE(t){if(t=t||{},t.circles)return AE(t);let e=new Map;if(e.set(Date,o=>new Date(o)),e.set(Map,(o,a)=>new Map(n(Array.from(o),a))),e.set(Set,(o,a)=>new Set(n(Array.from(o),a))),t.constructorHandlers)for(let o of t.constructorHandlers)e.set(o[0],o[1]);let r=null;return t.proto?s:i;function n(o,a){let c=Object.keys(o),f=new Array(c.length);for(let h=0;h<c.length;h++){let d=c[h],w=o[d];typeof w!="object"||w===null?f[d]=w:w.constructor!==Object&&(r=e.get(w.constructor))?f[d]=r(w,a):ArrayBuffer.isView(w)?f[d]=yn(w):f[d]=a(w)}return f}function i(o){if(typeof o!="object"||o===null)return o;if(Array.isArray(o))return n(o,i);if(o.constructor!==Object&&(r=e.get(o.constructor)))return r(o,i);let a={};for(let c in o){if(Object.hasOwnProperty.call(o,c)===!1)continue;let f=o[c];typeof f!="object"||f===null?a[c]=f:f.constructor!==Object&&(r=e.get(f.constructor))?a[c]=r(f,i):ArrayBuffer.isView(f)?a[c]=yn(f):a[c]=i(f)}return a}function s(o){if(typeof o!="object"||o===null)return o;if(Array.isArray(o))return n(o,s);if(o.constructor!==Object&&(r=e.get(o.constructor)))return r(o,s);let a={};for(let c in o){let f=o[c];typeof f!="object"||f===null?a[c]=f:f.constructor!==Object&&(r=e.get(f.constructor))?a[c]=r(f,s):ArrayBuffer.isView(f)?a[c]=yn(f):a[c]=s(f)}return a}}function AE(t){let e=[],r=[],n=new Map;if(n.set(Date,c=>new Date(c)),n.set(Map,(c,f)=>new Map(s(Array.from(c),f))),n.set(Set,(c,f)=>new Set(s(Array.from(c),f))),t.constructorHandlers)for(let c of t.constructorHandlers)n.set(c[0],c[1]);let i=null;return t.proto?a:o;function s(c,f){let h=Object.keys(c),d=new Array(h.length);for(let w=0;w<h.length;w++){let y=h[w],P=c[y];if(typeof P!="object"||P===null)d[y]=P;else if(P.constructor!==Object&&(i=n.get(P.constructor)))d[y]=i(P,f);else if(ArrayBuffer.isView(P))d[y]=yn(P);else{let E=e.indexOf(P);E!==-1?d[y]=r[E]:d[y]=f(P)}}return d}function o(c){if(typeof c!="object"||c===null)return c;if(Array.isArray(c))return s(c,o);if(c.constructor!==Object&&(i=n.get(c.constructor)))return i(c,o);let f={};e.push(c),r.push(f);for(let h in c){if(Object.hasOwnProperty.call(c,h)===!1)continue;let d=c[h];if(typeof d!="object"||d===null)f[h]=d;else if(d.constructor!==Object&&(i=n.get(d.constructor)))f[h]=i(d,o);else if(ArrayBuffer.isView(d))f[h]=yn(d);else{let w=e.indexOf(d);w!==-1?f[h]=r[w]:f[h]=o(d)}}return e.pop(),r.pop(),f}function a(c){if(typeof c!="object"||c===null)return c;if(Array.isArray(c))return s(c,a);if(c.constructor!==Object&&(i=n.get(c.constructor)))return i(c,a);let f={};e.push(c),r.push(f);for(let h in c){let d=c[h];if(typeof d!="object"||d===null)f[h]=d;else if(d.constructor!==Object&&(i=n.get(d.constructor)))f[h]=i(d,a);else if(ArrayBuffer.isView(d))f[h]=yn(d);else{let w=e.indexOf(d);w!==-1?f[h]=r[w]:f[h]=a(d)}}return e.pop(),r.pop(),f}}});var uy=D((YN,ay)=>{"use strict";A();x();I();ay.exports=sy()()});var cy=D(wo=>{"use strict";A();x();I();Object.defineProperty(wo,"__esModule",{value:!0});wo.validateTopic=ly;wo.validateTopics=IE;function ly(t){let e=t.split("/");for(let r=0;r<e.length;r++)if(e[r]!=="+"){if(e[r]==="#")return r===e.length-1;if(e[r].indexOf("+")!==-1||e[r].indexOf("#")!==-1)return!1}return!0}function IE(t){if(t.length===0)return"empty_topic_list";for(let e=0;e<t.length;e++)if(!ly(t[e]))return t[e];return null}});var La=D(qa=>{"use strict";A();x();I();Object.defineProperty(qa,"__esModule",{value:!0});var xE=or(),TE={objectMode:!0},PE={clean:!0},Na=class{options;_inflights;constructor(e){this.options=e||{},this.options={...PE,...e},this._inflights=new Map}put(e,r){return this._inflights.set(e.messageId,e),r&&r(),this}createStream(){let e=new xE.Readable(TE),r=[],n=!1,i=0;return this._inflights.forEach((s,o)=>{r.push(s)}),e._read=()=>{!n&&i<r.length?e.push(r[i++]):e.push(null)},e.destroy=s=>{if(!n)return n=!0,setTimeout(()=>{e.emit("close")},0),e},e}del(e,r){let n=this._inflights.get(e.messageId);return n?(this._inflights.delete(e.messageId),r(null,n)):r&&r(new Error("missing packet")),this}get(e,r){let n=this._inflights.get(e.messageId);return n?r(null,n):r&&r(new Error("missing packet")),this}close(e){this.options.clean&&(this._inflights=null),e&&e()}};qa.default=Na});var hy=D(Ua=>{"use strict";A();x();I();Object.defineProperty(Ua,"__esModule",{value:!0});var fy=[0,16,128,131,135,144,145,151,153],OE=(t,e,r)=>{t.log("handlePublish: packet %o",e),r=typeof r<"u"?r:t.noop;let n=e.topic.toString(),i=e.payload,{qos:s}=e,{messageId:o}=e,{options:a}=t;if(t.options.protocolVersion===5){let c;if(e.properties&&(c=e.properties.topicAlias),typeof c<"u")if(n.length===0)if(c>0&&c<=65535){let f=t.topicAliasRecv.getTopicByAlias(c);if(f)n=f,t.log("handlePublish :: topic complemented by alias. topic: %s - alias: %d",n,c);else{t.log("handlePublish :: unregistered topic alias. alias: %d",c),t.emit("error",new Error("Received unregistered Topic Alias"));return}}else{t.log("handlePublish :: topic alias out of range. alias: %d",c),t.emit("error",new Error("Received Topic Alias is out of range"));return}else if(t.topicAliasRecv.put(n,c))t.log("handlePublish :: registered topic: %s - alias: %d",n,c);else{t.log("handlePublish :: topic alias out of range. alias: %d",c),t.emit("error",new Error("Received Topic Alias is out of range"));return}}switch(t.log("handlePublish: qos %d",s),s){case 2:{a.customHandleAcks(n,i,e,(c,f)=>{if(typeof c=="number"&&(f=c,c=null),c)return t.emit("error",c);if(fy.indexOf(f)===-1)return t.emit("error",new Error("Wrong reason code for pubrec"));f?t._sendPacket({cmd:"pubrec",messageId:o,reasonCode:f},r):t.incomingStore.put(e,()=>{t._sendPacket({cmd:"pubrec",messageId:o},r)})});break}case 1:{a.customHandleAcks(n,i,e,(c,f)=>{if(typeof c=="number"&&(f=c,c=null),c)return t.emit("error",c);if(fy.indexOf(f)===-1)return t.emit("error",new Error("Wrong reason code for puback"));f||t.emit("message",n,i,e),t.handleMessage(e,h=>{if(h)return r&&r(h);t._sendPacket({cmd:"puback",messageId:o,reasonCode:f},r)})});break}case 0:t.emit("message",n,i,e),t.handleMessage(e,r);break;default:t.log("handlePublish: unknown QoS. Doing nothing.");break}};Ua.default=OE});var dy=D((qq,RE)=>{RE.exports={version:"5.14.1"}});var Mr=D(ht=>{"use strict";A();x();I();Object.defineProperty(ht,"__esModule",{value:!0});ht.MQTTJS_VERSION=ht.nextTick=ht.ErrorWithSubackPacket=ht.ErrorWithReasonCode=void 0;ht.applyMixin=kE;var Da=class t extends Error{code;constructor(e,r){super(e),this.code=r,Object.setPrototypeOf(this,t.prototype),Object.getPrototypeOf(this).name="ErrorWithReasonCode"}};ht.ErrorWithReasonCode=Da;var ja=class t extends Error{packet;constructor(e,r){super(e),this.packet=r,Object.setPrototypeOf(this,t.prototype),Object.getPrototypeOf(this).name="ErrorWithSubackPacket"}};ht.ErrorWithSubackPacket=ja;function kE(t,e,r=!1){let n=[e];for(;;){let i=n[0],s=Object.getPrototypeOf(i);if(s?.prototype)n.unshift(s);else break}for(let i of n)for(let s of Object.getOwnPropertyNames(i.prototype))(r||s!=="constructor")&&Object.defineProperty(t.prototype,s,Object.getOwnPropertyDescriptor(i.prototype,s)??Object.create(null))}ht.nextTick=typeof M?.nextTick=="function"?M.nextTick:t=>{setTimeout(t,0)};ht.MQTTJS_VERSION=dy().version});var zn=D(ur=>{"use strict";A();x();I();Object.defineProperty(ur,"__esModule",{value:!0});ur.ReasonCodes=void 0;var py=Mr();ur.ReasonCodes={0:"",1:"Unacceptable protocol version",2:"Identifier rejected",3:"Server unavailable",4:"Bad username or password",5:"Not authorized",16:"No matching subscribers",17:"No subscription existed",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",132:"Unsupported Protocol Version",133:"Client Identifier not valid",134:"Bad User Name or Password",135:"Not authorized",136:"Server unavailable",137:"Server busy",138:"Banned",139:"Server shutting down",140:"Bad authentication method",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",145:"Packet identifier in use",146:"Packet Identifier not found",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};var CE=(t,e)=>{let{messageId:r}=e,n=e.cmd,i=null,s=t.outgoing[r]?t.outgoing[r].cb:null,o=null;if(!s){t.log("_handleAck :: Server sent an ack in error. Ignoring.");return}switch(t.log("_handleAck :: packet type",n),n){case"pubcomp":case"puback":{let a=e.reasonCode;a&&a>0&&a!==16?(o=new py.ErrorWithReasonCode(`Publish error: ${ur.ReasonCodes[a]}`,a),t._removeOutgoingAndStoreMessage(r,()=>{s(o,e)})):t._removeOutgoingAndStoreMessage(r,s);break}case"pubrec":{i={cmd:"pubrel",qos:2,messageId:r};let a=e.reasonCode;a&&a>0&&a!==16?(o=new py.ErrorWithReasonCode(`Publish error: ${ur.ReasonCodes[a]}`,a),t._removeOutgoingAndStoreMessage(r,()=>{s(o,e)})):t._sendPacket(i);break}case"suback":{delete t.outgoing[r],t.messageIdProvider.deallocate(r);let a=e.granted;for(let c=0;c<a.length;c++){let f=a[c];if((f&128)!==0){o=new Error(`Subscribe error: ${ur.ReasonCodes[f]}`),o.code=f;let h=t.messageIdToTopic[r];h&&h.forEach(d=>{delete t._resubscribeTopics[d]})}}delete t.messageIdToTopic[r],t._invokeStoreProcessingQueue(),s(o,e);break}case"unsuback":{delete t.outgoing[r],t.messageIdProvider.deallocate(r),t._invokeStoreProcessingQueue(),s(null,e);break}default:t.emit("error",new Error("unrecognized packet type"))}t.disconnecting&&Object.keys(t.outgoing).length===0&&t.emit("outgoingEmpty")};ur.default=CE});var gy=D(Fa=>{"use strict";A();x();I();Object.defineProperty(Fa,"__esModule",{value:!0});var yy=Mr(),BE=zn(),ME=(t,e)=>{let{options:r}=t,n=r.protocolVersion,i=n===5?e.reasonCode:e.returnCode;if(n!==5){let s=new yy.ErrorWithReasonCode(`Protocol error: Auth packets are only supported in MQTT 5. Your version:${n}`,i);t.emit("error",s);return}t.handleAuth(e,(s,o)=>{if(s){t.emit("error",s);return}if(i===24)t.reconnecting=!1,t._sendPacket(o);else{let a=new yy.ErrorWithReasonCode(`Connection refused: ${BE.ReasonCodes[i]}`,i);t.emit("error",a)}})};Fa.default=ME});var vy=D(_o=>{"use strict";A();x();I();Object.defineProperty(_o,"__esModule",{value:!0});_o.LRUCache=void 0;var gn=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,wy=new Set,Wa=typeof M=="object"&&M?M:{},my=(t,e,r,n)=>{typeof Wa.emitWarning=="function"?Wa.emitWarning(t,e,r,n):console.error(`[${r}] ${e}: ${t}`)},mo=globalThis.AbortController,by=globalThis.AbortSignal;if(typeof mo>"u"){by=class{onabort;_onabort=[];reason;aborted=!1;addEventListener(n,i){this._onabort.push(i)}},mo=class{constructor(){e()}signal=new by;abort(n){if(!this.signal.aborted){this.signal.reason=n,this.signal.aborted=!0;for(let i of this.signal._onabort)i(n);this.signal.onabort?.(n)}}};let t=Wa.env?.LRU_CACHE_IGNORE_AC_WARNING!=="1",e=()=>{t&&(t=!1,my("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",e))}}var NE=t=>!wy.has(t),yL=Symbol("type"),lr=t=>t&&t===Math.floor(t)&&t>0&&isFinite(t),_y=t=>lr(t)?t<=Math.pow(2,8)?Uint8Array:t<=Math.pow(2,16)?Uint16Array:t<=Math.pow(2,32)?Uint32Array:t<=Number.MAX_SAFE_INTEGER?bn:null:null,bn=class extends Array{constructor(e){super(e),this.fill(0)}},$a=class t{heap;length;static#u=!1;static create(e){let r=_y(e);if(!r)return[];t.#u=!0;let n=new t(e,r);return t.#u=!1,n}constructor(e,r){if(!t.#u)throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new r(e),this.length=0}push(e){this.heap[this.length++]=e}pop(){return this.heap[--this.length]}},Ha=class t{#u;#f;#y;#g;#R;#k;ttl;ttlResolution;ttlAutopurge;updateAgeOnGet;updateAgeOnHas;allowStale;noDisposeOnSet;noUpdateTTL;maxEntrySize;sizeCalculation;noDeleteOnFetchRejection;noDeleteOnStaleGet;allowStaleOnFetchAbort;allowStaleOnFetchRejection;ignoreFetchAbort;#i;#b;#n;#r;#e;#l;#h;#a;#o;#w;#s;#m;#_;#d;#v;#x;#c;static unsafeExposeInternals(e){return{starts:e.#_,ttls:e.#d,sizes:e.#m,keyMap:e.#n,keyList:e.#r,valList:e.#e,next:e.#l,prev:e.#h,get head(){return e.#a},get tail(){return e.#o},free:e.#w,isBackgroundFetch:r=>e.#t(r),backgroundFetch:(r,n,i,s)=>e.#M(r,n,i,s),moveToTail:r=>e.#O(r),indexes:r=>e.#S(r),rindexes:r=>e.#E(r),isStale:r=>e.#p(r)}}get max(){return this.#u}get maxSize(){return this.#f}get calculatedSize(){return this.#b}get size(){return this.#i}get fetchMethod(){return this.#R}get memoMethod(){return this.#k}get dispose(){return this.#y}get disposeAfter(){return this.#g}constructor(e){let{max:r=0,ttl:n,ttlResolution:i=1,ttlAutopurge:s,updateAgeOnGet:o,updateAgeOnHas:a,allowStale:c,dispose:f,disposeAfter:h,noDisposeOnSet:d,noUpdateTTL:w,maxSize:y=0,maxEntrySize:P=0,sizeCalculation:E,fetchMethod:b,memoMethod:m,noDeleteOnFetchRejection:v,noDeleteOnStaleGet:R,allowStaleOnFetchRejection:T,allowStaleOnFetchAbort:g,ignoreFetchAbort:O}=e;if(r!==0&&!lr(r))throw new TypeError("max option must be a nonnegative integer");let _=r?_y(r):Array;if(!_)throw new Error("invalid max value: "+r);if(this.#u=r,this.#f=y,this.maxEntrySize=P||this.#f,this.sizeCalculation=E,this.sizeCalculation){if(!this.#f&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(m!==void 0&&typeof m!="function")throw new TypeError("memoMethod must be a function if defined");if(this.#k=m,b!==void 0&&typeof b!="function")throw new TypeError("fetchMethod must be a function if specified");if(this.#R=b,this.#x=!!b,this.#n=new Map,this.#r=new Array(r).fill(void 0),this.#e=new Array(r).fill(void 0),this.#l=new _(r),this.#h=new _(r),this.#a=0,this.#o=0,this.#w=$a.create(r),this.#i=0,this.#b=0,typeof f=="function"&&(this.#y=f),typeof h=="function"?(this.#g=h,this.#s=[]):(this.#g=void 0,this.#s=void 0),this.#v=!!this.#y,this.#c=!!this.#g,this.noDisposeOnSet=!!d,this.noUpdateTTL=!!w,this.noDeleteOnFetchRejection=!!v,this.allowStaleOnFetchRejection=!!T,this.allowStaleOnFetchAbort=!!g,this.ignoreFetchAbort=!!O,this.maxEntrySize!==0){if(this.#f!==0&&!lr(this.#f))throw new TypeError("maxSize must be a positive integer if specified");if(!lr(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");this.#F()}if(this.allowStale=!!c,this.noDeleteOnStaleGet=!!R,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!a,this.ttlResolution=lr(i)||i===0?i:1,this.ttlAutopurge=!!s,this.ttl=n||0,this.ttl){if(!lr(this.ttl))throw new TypeError("ttl must be a positive integer if specified");this.#N()}if(this.#u===0&&this.ttl===0&&this.#f===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!this.#u&&!this.#f){let q="LRU_CACHE_UNBOUNDED";NE(q)&&(wy.add(q),my("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",q,t))}}getRemainingTTL(e){return this.#n.has(e)?1/0:0}#N(){let e=new bn(this.#u),r=new bn(this.#u);this.#d=e,this.#_=r,this.#q=(s,o,a=gn.now())=>{if(r[s]=o!==0?a:0,e[s]=o,o!==0&&this.ttlAutopurge){let c=setTimeout(()=>{this.#p(s)&&this.#A(this.#r[s],"expire")},o+1);c.unref&&c.unref()}},this.#T=s=>{r[s]=e[s]!==0?gn.now():0},this.#I=(s,o)=>{if(e[o]){let a=e[o],c=r[o];if(!a||!c)return;s.ttl=a,s.start=c,s.now=n||i();let f=s.now-c;s.remainingTTL=a-f}};let n=0,i=()=>{let s=gn.now();if(this.ttlResolution>0){n=s;let o=setTimeout(()=>n=0,this.ttlResolution);o.unref&&o.unref()}return s};this.getRemainingTTL=s=>{let o=this.#n.get(s);if(o===void 0)return 0;let a=e[o],c=r[o];if(!a||!c)return 1/0;let f=(n||i())-c;return a-f},this.#p=s=>{let o=r[s],a=e[s];return!!a&&!!o&&(n||i())-o>a}}#T=()=>{};#I=()=>{};#q=()=>{};#p=()=>!1;#F(){let e=new bn(this.#u);this.#b=0,this.#m=e,this.#P=r=>{this.#b-=e[r],e[r]=0},this.#L=(r,n,i,s)=>{if(this.#t(n))return 0;if(!lr(i))if(s){if(typeof s!="function")throw new TypeError("sizeCalculation must be a function");if(i=s(n,r),!lr(i))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return i},this.#C=(r,n,i)=>{if(e[r]=n,this.#f){let s=this.#f-e[r];for(;this.#b>s;)this.#B(!0)}this.#b+=e[r],i&&(i.entrySize=n,i.totalCalculatedSize=this.#b)}}#P=e=>{};#C=(e,r,n)=>{};#L=(e,r,n,i)=>{if(n||i)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0};*#S({allowStale:e=this.allowStale}={}){if(this.#i)for(let r=this.#o;!(!this.#U(r)||((e||!this.#p(r))&&(yield r),r===this.#a));)r=this.#h[r]}*#E({allowStale:e=this.allowStale}={}){if(this.#i)for(let r=this.#a;!(!this.#U(r)||((e||!this.#p(r))&&(yield r),r===this.#o));)r=this.#l[r]}#U(e){return e!==void 0&&this.#n.get(this.#r[e])===e}*entries(){for(let e of this.#S())this.#e[e]!==void 0&&this.#r[e]!==void 0&&!this.#t(this.#e[e])&&(yield[this.#r[e],this.#e[e]])}*rentries(){for(let e of this.#E())this.#e[e]!==void 0&&this.#r[e]!==void 0&&!this.#t(this.#e[e])&&(yield[this.#r[e],this.#e[e]])}*keys(){for(let e of this.#S()){let r=this.#r[e];r!==void 0&&!this.#t(this.#e[e])&&(yield r)}}*rkeys(){for(let e of this.#E()){let r=this.#r[e];r!==void 0&&!this.#t(this.#e[e])&&(yield r)}}*values(){for(let e of this.#S())this.#e[e]!==void 0&&!this.#t(this.#e[e])&&(yield this.#e[e])}*rvalues(){for(let e of this.#E())this.#e[e]!==void 0&&!this.#t(this.#e[e])&&(yield this.#e[e])}[Symbol.iterator](){return this.entries()}[Symbol.toStringTag]="LRUCache";find(e,r={}){for(let n of this.#S()){let i=this.#e[n],s=this.#t(i)?i.__staleWhileFetching:i;if(s!==void 0&&e(s,this.#r[n],this))return this.get(this.#r[n],r)}}forEach(e,r=this){for(let n of this.#S()){let i=this.#e[n],s=this.#t(i)?i.__staleWhileFetching:i;s!==void 0&&e.call(r,s,this.#r[n],this)}}rforEach(e,r=this){for(let n of this.#E()){let i=this.#e[n],s=this.#t(i)?i.__staleWhileFetching:i;s!==void 0&&e.call(r,s,this.#r[n],this)}}purgeStale(){let e=!1;for(let r of this.#E({allowStale:!0}))this.#p(r)&&(this.#A(this.#r[r],"expire"),e=!0);return e}info(e){let r=this.#n.get(e);if(r===void 0)return;let n=this.#e[r],i=this.#t(n)?n.__staleWhileFetching:n;if(i===void 0)return;let s={value:i};if(this.#d&&this.#_){let o=this.#d[r],a=this.#_[r];if(o&&a){let c=o-(gn.now()-a);s.ttl=c,s.start=Date.now()}}return this.#m&&(s.size=this.#m[r]),s}dump(){let e=[];for(let r of this.#S({allowStale:!0})){let n=this.#r[r],i=this.#e[r],s=this.#t(i)?i.__staleWhileFetching:i;if(s===void 0||n===void 0)continue;let o={value:s};if(this.#d&&this.#_){o.ttl=this.#d[r];let a=gn.now()-this.#_[r];o.start=Math.floor(Date.now()-a)}this.#m&&(o.size=this.#m[r]),e.unshift([n,o])}return e}load(e){this.clear();for(let[r,n]of e){if(n.start){let i=Date.now()-n.start;n.start=gn.now()-i}this.set(r,n.value,n)}}set(e,r,n={}){if(r===void 0)return this.delete(e),this;let{ttl:i=this.ttl,start:s,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:a=this.sizeCalculation,status:c}=n,{noUpdateTTL:f=this.noUpdateTTL}=n,h=this.#L(e,r,n.size||0,a);if(this.maxEntrySize&&h>this.maxEntrySize)return c&&(c.set="miss",c.maxEntrySizeExceeded=!0),this.#A(e,"set"),this;let d=this.#i===0?void 0:this.#n.get(e);if(d===void 0)d=this.#i===0?this.#o:this.#w.length!==0?this.#w.pop():this.#i===this.#u?this.#B(!1):this.#i,this.#r[d]=e,this.#e[d]=r,this.#n.set(e,d),this.#l[this.#o]=d,this.#h[d]=this.#o,this.#o=d,this.#i++,this.#C(d,h,c),c&&(c.set="add"),f=!1;else{this.#O(d);let w=this.#e[d];if(r!==w){if(this.#x&&this.#t(w)){w.__abortController.abort(new Error("replaced"));let{__staleWhileFetching:y}=w;y!==void 0&&!o&&(this.#v&&this.#y?.(y,e,"set"),this.#c&&this.#s?.push([y,e,"set"]))}else o||(this.#v&&this.#y?.(w,e,"set"),this.#c&&this.#s?.push([w,e,"set"]));if(this.#P(d),this.#C(d,h,c),this.#e[d]=r,c){c.set="replace";let y=w&&this.#t(w)?w.__staleWhileFetching:w;y!==void 0&&(c.oldValue=y)}}else c&&(c.set="update")}if(i!==0&&!this.#d&&this.#N(),this.#d&&(f||this.#q(d,i,s),c&&this.#I(c,d)),!o&&this.#c&&this.#s){let w=this.#s,y;for(;y=w?.shift();)this.#g?.(...y)}return this}pop(){try{for(;this.#i;){let e=this.#e[this.#a];if(this.#B(!0),this.#t(e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(this.#c&&this.#s){let e=this.#s,r;for(;r=e?.shift();)this.#g?.(...r)}}}#B(e){let r=this.#a,n=this.#r[r],i=this.#e[r];return this.#x&&this.#t(i)?i.__abortController.abort(new Error("evicted")):(this.#v||this.#c)&&(this.#v&&this.#y?.(i,n,"evict"),this.#c&&this.#s?.push([i,n,"evict"])),this.#P(r),e&&(this.#r[r]=void 0,this.#e[r]=void 0,this.#w.push(r)),this.#i===1?(this.#a=this.#o=0,this.#w.length=0):this.#a=this.#l[r],this.#n.delete(n),this.#i--,r}has(e,r={}){let{updateAgeOnHas:n=this.updateAgeOnHas,status:i}=r,s=this.#n.get(e);if(s!==void 0){let o=this.#e[s];if(this.#t(o)&&o.__staleWhileFetching===void 0)return!1;if(this.#p(s))i&&(i.has="stale",this.#I(i,s));else return n&&this.#T(s),i&&(i.has="hit",this.#I(i,s)),!0}else i&&(i.has="miss");return!1}peek(e,r={}){let{allowStale:n=this.allowStale}=r,i=this.#n.get(e);if(i===void 0||!n&&this.#p(i))return;let s=this.#e[i];return this.#t(s)?s.__staleWhileFetching:s}#M(e,r,n,i){let s=r===void 0?void 0:this.#e[r];if(this.#t(s))return s;let o=new mo,{signal:a}=n;a?.addEventListener("abort",()=>o.abort(a.reason),{signal:o.signal});let c={signal:o.signal,options:n,context:i},f=(E,b=!1)=>{let{aborted:m}=o.signal,v=n.ignoreFetchAbort&&E!==void 0;if(n.status&&(m&&!b?(n.status.fetchAborted=!0,n.status.fetchError=o.signal.reason,v&&(n.status.fetchAbortIgnored=!0)):n.status.fetchResolved=!0),m&&!v&&!b)return d(o.signal.reason);let R=y;return this.#e[r]===y&&(E===void 0?R.__staleWhileFetching?this.#e[r]=R.__staleWhileFetching:this.#A(e,"fetch"):(n.status&&(n.status.fetchUpdated=!0),this.set(e,E,c.options))),E},h=E=>(n.status&&(n.status.fetchRejected=!0,n.status.fetchError=E),d(E)),d=E=>{let{aborted:b}=o.signal,m=b&&n.allowStaleOnFetchAbort,v=m||n.allowStaleOnFetchRejection,R=v||n.noDeleteOnFetchRejection,T=y;if(this.#e[r]===y&&(!R||T.__staleWhileFetching===void 0?this.#A(e,"fetch"):m||(this.#e[r]=T.__staleWhileFetching)),v)return n.status&&T.__staleWhileFetching!==void 0&&(n.status.returnedStale=!0),T.__staleWhileFetching;if(T.__returned===T)throw E},w=(E,b)=>{let m=this.#R?.(e,s,c);m&&m instanceof Promise&&m.then(v=>E(v===void 0?void 0:v),b),o.signal.addEventListener("abort",()=>{(!n.ignoreFetchAbort||n.allowStaleOnFetchAbort)&&(E(void 0),n.allowStaleOnFetchAbort&&(E=v=>f(v,!0)))})};n.status&&(n.status.fetchDispatched=!0);let y=new Promise(w).then(f,h),P=Object.assign(y,{__abortController:o,__staleWhileFetching:s,__returned:void 0});return r===void 0?(this.set(e,P,{...c.options,status:void 0}),r=this.#n.get(e)):this.#e[r]=P,P}#t(e){if(!this.#x)return!1;let r=e;return!!r&&r instanceof Promise&&r.hasOwnProperty("__staleWhileFetching")&&r.__abortController instanceof mo}async fetch(e,r={}){let{allowStale:n=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:s=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:a=this.noDisposeOnSet,size:c=0,sizeCalculation:f=this.sizeCalculation,noUpdateTTL:h=this.noUpdateTTL,noDeleteOnFetchRejection:d=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:w=this.allowStaleOnFetchRejection,ignoreFetchAbort:y=this.ignoreFetchAbort,allowStaleOnFetchAbort:P=this.allowStaleOnFetchAbort,context:E,forceRefresh:b=!1,status:m,signal:v}=r;if(!this.#x)return m&&(m.fetch="get"),this.get(e,{allowStale:n,updateAgeOnGet:i,noDeleteOnStaleGet:s,status:m});let R={allowStale:n,updateAgeOnGet:i,noDeleteOnStaleGet:s,ttl:o,noDisposeOnSet:a,size:c,sizeCalculation:f,noUpdateTTL:h,noDeleteOnFetchRejection:d,allowStaleOnFetchRejection:w,allowStaleOnFetchAbort:P,ignoreFetchAbort:y,status:m,signal:v},T=this.#n.get(e);if(T===void 0){m&&(m.fetch="miss");let g=this.#M(e,T,R,E);return g.__returned=g}else{let g=this.#e[T];if(this.#t(g)){let j=n&&g.__staleWhileFetching!==void 0;return m&&(m.fetch="inflight",j&&(m.returnedStale=!0)),j?g.__staleWhileFetching:g.__returned=g}let O=this.#p(T);if(!b&&!O)return m&&(m.fetch="hit"),this.#O(T),i&&this.#T(T),m&&this.#I(m,T),g;let _=this.#M(e,T,R,E),$=_.__staleWhileFetching!==void 0&&n;return m&&(m.fetch=O?"stale":"refresh",$&&O&&(m.returnedStale=!0)),$?_.__staleWhileFetching:_.__returned=_}}async forceFetch(e,r={}){let n=await this.fetch(e,r);if(n===void 0)throw new Error("fetch() returned undefined");return n}memo(e,r={}){let n=this.#k;if(!n)throw new Error("no memoMethod provided to constructor");let{context:i,forceRefresh:s,...o}=r,a=this.get(e,o);if(!s&&a!==void 0)return a;let c=n(e,a,{options:o,context:i});return this.set(e,c,o),c}get(e,r={}){let{allowStale:n=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:s=this.noDeleteOnStaleGet,status:o}=r,a=this.#n.get(e);if(a!==void 0){let c=this.#e[a],f=this.#t(c);return o&&this.#I(o,a),this.#p(a)?(o&&(o.get="stale"),f?(o&&n&&c.__staleWhileFetching!==void 0&&(o.returnedStale=!0),n?c.__staleWhileFetching:void 0):(s||this.#A(e,"expire"),o&&n&&(o.returnedStale=!0),n?c:void 0)):(o&&(o.get="hit"),f?c.__staleWhileFetching:(this.#O(a),i&&this.#T(a),c))}else o&&(o.get="miss")}#D(e,r){this.#h[r]=e,this.#l[e]=r}#O(e){e!==this.#o&&(e===this.#a?this.#a=this.#l[e]:this.#D(this.#h[e],this.#l[e]),this.#D(this.#o,e),this.#o=e)}delete(e){return this.#A(e,"delete")}#A(e,r){let n=!1;if(this.#i!==0){let i=this.#n.get(e);if(i!==void 0)if(n=!0,this.#i===1)this.#j(r);else{this.#P(i);let s=this.#e[i];if(this.#t(s)?s.__abortController.abort(new Error("deleted")):(this.#v||this.#c)&&(this.#v&&this.#y?.(s,e,r),this.#c&&this.#s?.push([s,e,r])),this.#n.delete(e),this.#r[i]=void 0,this.#e[i]=void 0,i===this.#o)this.#o=this.#h[i];else if(i===this.#a)this.#a=this.#l[i];else{let o=this.#h[i];this.#l[o]=this.#l[i];let a=this.#l[i];this.#h[a]=this.#h[i]}this.#i--,this.#w.push(i)}}if(this.#c&&this.#s?.length){let i=this.#s,s;for(;s=i?.shift();)this.#g?.(...s)}return n}clear(){return this.#j("delete")}#j(e){for(let r of this.#E({allowStale:!0})){let n=this.#e[r];if(this.#t(n))n.__abortController.abort(new Error("deleted"));else{let i=this.#r[r];this.#v&&this.#y?.(n,i,e),this.#c&&this.#s?.push([n,i,e])}}if(this.#n.clear(),this.#e.fill(void 0),this.#r.fill(void 0),this.#d&&this.#_&&(this.#d.fill(0),this.#_.fill(0)),this.#m&&this.#m.fill(0),this.#a=0,this.#o=0,this.#w.length=0,this.#b=0,this.#i=0,this.#c&&this.#s){let r=this.#s,n;for(;n=r?.shift();)this.#g?.(...n)}}};_o.LRUCache=Ha});var It=D(cr=>{"use strict";A();x();I();Object.defineProperty(cr,"t",{value:!0});cr.ContainerIterator=cr.Container=cr.Base=void 0;var Va=class{constructor(e=0){this.iteratorType=e}equals(e){return this.o===e.o}};cr.ContainerIterator=Va;var vo=class{constructor(){this.i=0}get length(){return this.i}size(){return this.i}empty(){return this.i===0}};cr.Base=vo;var Ga=class extends vo{};cr.Container=Ga});var Sy=D(So=>{"use strict";A();x();I();Object.defineProperty(So,"t",{value:!0});So.default=void 0;var qE=It(),za=class extends qE.Base{constructor(e=[]){super(),this.S=[];let r=this;e.forEach(function(n){r.push(n)})}clear(){this.i=0,this.S=[]}push(e){return this.S.push(e),this.i+=1,this.i}pop(){if(this.i!==0)return this.i-=1,this.S.pop()}top(){return this.S[this.i-1]}},LE=za;So.default=LE});var Ey=D(Eo=>{"use strict";A();x();I();Object.defineProperty(Eo,"t",{value:!0});Eo.default=void 0;var UE=It(),Ka=class extends UE.Base{constructor(e=[]){super(),this.j=0,this.q=[];let r=this;e.forEach(function(n){r.push(n)})}clear(){this.q=[],this.i=this.j=0}push(e){let r=this.q.length;if(this.j/r>.5&&this.j+this.i>=r&&r>4096){let n=this.i;for(let i=0;i<n;++i)this.q[i]=this.q[this.j+i];this.j=0,this.q[this.i]=e}else this.q[this.j+this.i]=e;return++this.i}pop(){if(this.i===0)return;let e=this.q[this.j++];return this.i-=1,e}front(){if(this.i!==0)return this.q[this.j]}},DE=Ka;Eo.default=DE});var Ay=D(Ao=>{"use strict";A();x();I();Object.defineProperty(Ao,"t",{value:!0});Ao.default=void 0;var jE=It(),Qa=class extends jE.Base{constructor(e=[],r=function(i,s){return i>s?-1:i<s?1:0},n=!0){if(super(),this.v=r,Array.isArray(e))this.C=n?[...e]:e;else{this.C=[];let s=this;e.forEach(function(o){s.C.push(o)})}this.i=this.C.length;let i=this.i>>1;for(let s=this.i-1>>1;s>=0;--s)this.k(s,i)}m(e){let r=this.C[e];for(;e>0;){let n=e-1>>1,i=this.C[n];if(this.v(i,r)<=0)break;this.C[e]=i,e=n}this.C[e]=r}k(e,r){let n=this.C[e];for(;e<r;){let i=e<<1|1,s=i+1,o=this.C[i];if(s<this.i&&this.v(o,this.C[s])>0&&(i=s,o=this.C[s]),this.v(o,n)>=0)break;this.C[e]=o,e=i}this.C[e]=n}clear(){this.i=0,this.C.length=0}push(e){this.C.push(e),this.m(this.i),this.i+=1}pop(){if(this.i===0)return;let e=this.C[0],r=this.C.pop();return this.i-=1,this.i&&(this.C[0]=r,this.k(0,this.i>>1)),e}top(){return this.C[0]}find(e){return this.C.indexOf(e)>=0}remove(e){let r=this.C.indexOf(e);return r<0?!1:(r===0?this.pop():r===this.i-1?(this.C.pop(),this.i-=1):(this.C.splice(r,1,this.C.pop()),this.i-=1,this.m(r),this.k(r,this.i>>1)),!0)}updateItem(e){let r=this.C.indexOf(e);return r<0?!1:(this.m(r),this.k(r,this.i>>1),!0)}toArray(){return[...this.C]}},FE=Qa;Ao.default=FE});var xo=D(Io=>{"use strict";A();x();I();Object.defineProperty(Io,"t",{value:!0});Io.default=void 0;var WE=It(),Ya=class extends WE.Container{},$E=Ya;Io.default=$E});var xt=D(Ja=>{"use strict";A();x();I();Object.defineProperty(Ja,"t",{value:!0});Ja.throwIteratorAccessError=HE;function HE(){throw new RangeError("Iterator access denied!")}});var Za=D(Po=>{"use strict";A();x();I();Object.defineProperty(Po,"t",{value:!0});Po.RandomIterator=void 0;var VE=It(),To=xt(),Xa=class extends VE.ContainerIterator{constructor(e,r){super(r),this.o=e,this.iteratorType===0?(this.pre=function(){return this.o===0&&(0,To.throwIteratorAccessError)(),this.o-=1,this},this.next=function(){return this.o===this.container.size()&&(0,To.throwIteratorAccessError)(),this.o+=1,this}):(this.pre=function(){return this.o===this.container.size()-1&&(0,To.throwIteratorAccessError)(),this.o+=1,this},this.next=function(){return this.o===-1&&(0,To.throwIteratorAccessError)(),this.o-=1,this})}get pointer(){return this.container.getElementByPos(this.o)}set pointer(e){this.container.setElementByPos(this.o,e)}};Po.RandomIterator=Xa});var Iy=D(Oo=>{"use strict";A();x();I();Object.defineProperty(Oo,"t",{value:!0});Oo.default=void 0;var GE=KE(xo()),zE=Za();function KE(t){return t&&t.t?t:{default:t}}var Nr=class t extends zE.RandomIterator{constructor(e,r,n){super(e,n),this.container=r}copy(){return new t(this.o,this.container,this.iteratorType)}},eu=class extends GE.default{constructor(e=[],r=!0){if(super(),Array.isArray(e))this.J=r?[...e]:e,this.i=e.length;else{this.J=[];let n=this;e.forEach(function(i){n.pushBack(i)})}}clear(){this.i=0,this.J.length=0}begin(){return new Nr(0,this)}end(){return new Nr(this.i,this)}rBegin(){return new Nr(this.i-1,this,1)}rEnd(){return new Nr(-1,this,1)}front(){return this.J[0]}back(){return this.J[this.i-1]}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;return this.J[e]}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;return this.J.splice(e,1),this.i-=1,this.i}eraseElementByValue(e){let r=0;for(let n=0;n<this.i;++n)this.J[n]!==e&&(this.J[r++]=this.J[n]);return this.i=this.J.length=r,this.i}eraseElementByIterator(e){let r=e.o;return e=e.next(),this.eraseElementByPos(r),e}pushBack(e){return this.J.push(e),this.i+=1,this.i}popBack(){if(this.i!==0)return this.i-=1,this.J.pop()}setElementByPos(e,r){if(e<0||e>this.i-1)throw new RangeError;this.J[e]=r}insert(e,r,n=1){if(e<0||e>this.i)throw new RangeError;return this.J.splice(e,0,...new Array(n).fill(r)),this.i+=n,this.i}find(e){for(let r=0;r<this.i;++r)if(this.J[r]===e)return new Nr(r,this);return this.end()}reverse(){this.J.reverse()}unique(){let e=1;for(let r=1;r<this.i;++r)this.J[r]!==this.J[r-1]&&(this.J[e++]=this.J[r]);return this.i=this.J.length=e,this.i}sort(e){this.J.sort(e)}forEach(e){for(let r=0;r<this.i;++r)e(this.J[r],r,this)}[Symbol.iterator](){return function*(){yield*this.J}.bind(this)()}},QE=eu;Oo.default=QE});var xy=D(Ro=>{"use strict";A();x();I();Object.defineProperty(Ro,"t",{value:!0});Ro.default=void 0;var YE=XE(xo()),JE=It(),qr=xt();function XE(t){return t&&t.t?t:{default:t}}var Lr=class t extends JE.ContainerIterator{constructor(e,r,n,i){super(i),this.o=e,this.h=r,this.container=n,this.iteratorType===0?(this.pre=function(){return this.o.L===this.h&&(0,qr.throwIteratorAccessError)(),this.o=this.o.L,this},this.next=function(){return this.o===this.h&&(0,qr.throwIteratorAccessError)(),this.o=this.o.B,this}):(this.pre=function(){return this.o.B===this.h&&(0,qr.throwIteratorAccessError)(),this.o=this.o.B,this},this.next=function(){return this.o===this.h&&(0,qr.throwIteratorAccessError)(),this.o=this.o.L,this})}get pointer(){return this.o===this.h&&(0,qr.throwIteratorAccessError)(),this.o.l}set pointer(e){this.o===this.h&&(0,qr.throwIteratorAccessError)(),this.o.l=e}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},tu=class extends YE.default{constructor(e=[]){super(),this.h={},this.p=this._=this.h.L=this.h.B=this.h;let r=this;e.forEach(function(n){r.pushBack(n)})}V(e){let{L:r,B:n}=e;r.B=n,n.L=r,e===this.p&&(this.p=n),e===this._&&(this._=r),this.i-=1}G(e,r){let n=r.B,i={l:e,L:r,B:n};r.B=i,n.L=i,r===this.h&&(this.p=i),n===this.h&&(this._=i),this.i+=1}clear(){this.i=0,this.p=this._=this.h.L=this.h.B=this.h}begin(){return new Lr(this.p,this.h,this)}end(){return new Lr(this.h,this.h,this)}rBegin(){return new Lr(this._,this.h,this,1)}rEnd(){return new Lr(this.h,this.h,this,1)}front(){return this.p.l}back(){return this._.l}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return r.l}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return this.V(r),this.i}eraseElementByValue(e){let r=this.p;for(;r!==this.h;)r.l===e&&this.V(r),r=r.B;return this.i}eraseElementByIterator(e){let r=e.o;return r===this.h&&(0,qr.throwIteratorAccessError)(),e=e.next(),this.V(r),e}pushBack(e){return this.G(e,this._),this.i}popBack(){if(this.i===0)return;let e=this._.l;return this.V(this._),e}pushFront(e){return this.G(e,this.h),this.i}popFront(){if(this.i===0)return;let e=this.p.l;return this.V(this.p),e}setElementByPos(e,r){if(e<0||e>this.i-1)throw new RangeError;let n=this.p;for(;e--;)n=n.B;n.l=r}insert(e,r,n=1){if(e<0||e>this.i)throw new RangeError;if(n<=0)return this.i;if(e===0)for(;n--;)this.pushFront(r);else if(e===this.i)for(;n--;)this.pushBack(r);else{let i=this.p;for(let o=1;o<e;++o)i=i.B;let s=i.B;for(this.i+=n;n--;)i.B={l:r,L:i},i.B.L=i,i=i.B;i.B=s,s.L=i}return this.i}find(e){let r=this.p;for(;r!==this.h;){if(r.l===e)return new Lr(r,this.h,this);r=r.B}return this.end()}reverse(){if(this.i<=1)return;let e=this.p,r=this._,n=0;for(;n<<1<this.i;){let i=e.l;e.l=r.l,r.l=i,e=e.B,r=r.L,n+=1}}unique(){if(this.i<=1)return this.i;let e=this.p;for(;e!==this.h;){let r=e;for(;r.B!==this.h&&r.l===r.B.l;)r=r.B,this.i-=1;e.B=r.B,e.B.L=e,e=e.B}return this.i}sort(e){if(this.i<=1)return;let r=[];this.forEach(function(i){r.push(i)}),r.sort(e);let n=this.p;r.forEach(function(i){n.l=i,n=n.B})}merge(e){let r=this;if(this.i===0)e.forEach(function(n){r.pushBack(n)});else{let n=this.p;e.forEach(function(i){for(;n!==r.h&&n.l<=i;)n=n.B;r.G(i,n.L)})}return this.i}forEach(e){let r=this.p,n=0;for(;r!==this.h;)e(r.l,n++,this),r=r.B}[Symbol.iterator](){return function*(){if(this.i===0)return;let e=this.p;for(;e!==this.h;)yield e.l,e=e.B}.bind(this)()}},ZE=tu;Ro.default=ZE});var Ty=D(ko=>{"use strict";A();x();I();Object.defineProperty(ko,"t",{value:!0});ko.default=void 0;var e1=r1(xo()),t1=Za();function r1(t){return t&&t.t?t:{default:t}}var Ur=class t extends t1.RandomIterator{constructor(e,r,n){super(e,n),this.container=r}copy(){return new t(this.o,this.container,this.iteratorType)}},ru=class extends e1.default{constructor(e=[],r=4096){super(),this.j=0,this.D=0,this.R=0,this.N=0,this.P=0,this.A=[];let n=(()=>{if(typeof e.length=="number")return e.length;if(typeof e.size=="number")return e.size;if(typeof e.size=="function")return e.size();throw new TypeError("Cannot get the length or size of the container")})();this.F=r,this.P=Math.max(Math.ceil(n/this.F),1);for(let o=0;o<this.P;++o)this.A.push(new Array(this.F));let i=Math.ceil(n/this.F);this.j=this.R=(this.P>>1)-(i>>1),this.D=this.N=this.F-n%this.F>>1;let s=this;e.forEach(function(o){s.pushBack(o)})}T(){let e=[],r=Math.max(this.P>>1,1);for(let n=0;n<r;++n)e[n]=new Array(this.F);for(let n=this.j;n<this.P;++n)e[e.length]=this.A[n];for(let n=0;n<this.R;++n)e[e.length]=this.A[n];e[e.length]=[...this.A[this.R]],this.j=r,this.R=e.length-1;for(let n=0;n<r;++n)e[e.length]=new Array(this.F);this.A=e,this.P=e.length}O(e){let r=this.D+e+1,n=r%this.F,i=n-1,s=this.j+(r-n)/this.F;return n===0&&(s-=1),s%=this.P,i<0&&(i+=this.F),{curNodeBucketIndex:s,curNodePointerIndex:i}}clear(){this.A=[new Array(this.F)],this.P=1,this.j=this.R=this.i=0,this.D=this.N=this.F>>1}begin(){return new Ur(0,this)}end(){return new Ur(this.i,this)}rBegin(){return new Ur(this.i-1,this,1)}rEnd(){return new Ur(-1,this,1)}front(){if(this.i!==0)return this.A[this.j][this.D]}back(){if(this.i!==0)return this.A[this.R][this.N]}pushBack(e){return this.i&&(this.N<this.F-1?this.N+=1:this.R<this.P-1?(this.R+=1,this.N=0):(this.R=0,this.N=0),this.R===this.j&&this.N===this.D&&this.T()),this.i+=1,this.A[this.R][this.N]=e,this.i}popBack(){if(this.i===0)return;let e=this.A[this.R][this.N];return this.i!==1&&(this.N>0?this.N-=1:this.R>0?(this.R-=1,this.N=this.F-1):(this.R=this.P-1,this.N=this.F-1)),this.i-=1,e}pushFront(e){return this.i&&(this.D>0?this.D-=1:this.j>0?(this.j-=1,this.D=this.F-1):(this.j=this.P-1,this.D=this.F-1),this.j===this.R&&this.D===this.N&&this.T()),this.i+=1,this.A[this.j][this.D]=e,this.i}popFront(){if(this.i===0)return;let e=this.A[this.j][this.D];return this.i!==1&&(this.D<this.F-1?this.D+=1:this.j<this.P-1?(this.j+=1,this.D=0):(this.j=0,this.D=0)),this.i-=1,e}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let{curNodeBucketIndex:r,curNodePointerIndex:n}=this.O(e);return this.A[r][n]}setElementByPos(e,r){if(e<0||e>this.i-1)throw new RangeError;let{curNodeBucketIndex:n,curNodePointerIndex:i}=this.O(e);this.A[n][i]=r}insert(e,r,n=1){if(e<0||e>this.i)throw new RangeError;if(e===0)for(;n--;)this.pushFront(r);else if(e===this.i)for(;n--;)this.pushBack(r);else{let i=[];for(let s=e;s<this.i;++s)i.push(this.getElementByPos(s));this.cut(e-1);for(let s=0;s<n;++s)this.pushBack(r);for(let s=0;s<i.length;++s)this.pushBack(i[s])}return this.i}cut(e){if(e<0)return this.clear(),0;let{curNodeBucketIndex:r,curNodePointerIndex:n}=this.O(e);return this.R=r,this.N=n,this.i=e+1,this.i}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;if(e===0)this.popFront();else if(e===this.i-1)this.popBack();else{let r=[];for(let i=e+1;i<this.i;++i)r.push(this.getElementByPos(i));this.cut(e),this.popBack();let n=this;r.forEach(function(i){n.pushBack(i)})}return this.i}eraseElementByValue(e){if(this.i===0)return 0;let r=[];for(let i=0;i<this.i;++i){let s=this.getElementByPos(i);s!==e&&r.push(s)}let n=r.length;for(let i=0;i<n;++i)this.setElementByPos(i,r[i]);return this.cut(n-1)}eraseElementByIterator(e){let r=e.o;return this.eraseElementByPos(r),e=e.next(),e}find(e){for(let r=0;r<this.i;++r)if(this.getElementByPos(r)===e)return new Ur(r,this);return this.end()}reverse(){let e=0,r=this.i-1;for(;e<r;){let n=this.getElementByPos(e);this.setElementByPos(e,this.getElementByPos(r)),this.setElementByPos(r,n),e+=1,r-=1}}unique(){if(this.i<=1)return this.i;let e=1,r=this.getElementByPos(0);for(let n=1;n<this.i;++n){let i=this.getElementByPos(n);i!==r&&(r=i,this.setElementByPos(e++,i))}for(;this.i>e;)this.popBack();return this.i}sort(e){let r=[];for(let n=0;n<this.i;++n)r.push(this.getElementByPos(n));r.sort(e);for(let n=0;n<this.i;++n)this.setElementByPos(n,r[n])}shrinkToFit(){if(this.i===0)return;let e=[];this.forEach(function(r){e.push(r)}),this.P=Math.max(Math.ceil(this.i/this.F),1),this.i=this.j=this.R=this.D=this.N=0,this.A=[];for(let r=0;r<this.P;++r)this.A.push(new Array(this.F));for(let r=0;r<e.length;++r)this.pushBack(e[r])}forEach(e){for(let r=0;r<this.i;++r)e(this.getElementByPos(r),r,this)}[Symbol.iterator](){return function*(){for(let e=0;e<this.i;++e)yield this.getElementByPos(e)}.bind(this)()}},n1=ru;ko.default=n1});var Py=D(wn=>{"use strict";A();x();I();Object.defineProperty(wn,"t",{value:!0});wn.TreeNodeEnableIndex=wn.TreeNode=void 0;var Co=class{constructor(e,r){this.ee=1,this.u=void 0,this.l=void 0,this.U=void 0,this.W=void 0,this.tt=void 0,this.u=e,this.l=r}L(){let e=this;if(e.ee===1&&e.tt.tt===e)e=e.W;else if(e.U)for(e=e.U;e.W;)e=e.W;else{let r=e.tt;for(;r.U===e;)e=r,r=e.tt;e=r}return e}B(){let e=this;if(e.W){for(e=e.W;e.U;)e=e.U;return e}else{let r=e.tt;for(;r.W===e;)e=r,r=e.tt;return e.W!==r?r:e}}te(){let e=this.tt,r=this.W,n=r.U;return e.tt===this?e.tt=r:e.U===this?e.U=r:e.W=r,r.tt=e,r.U=this,this.tt=r,this.W=n,n&&(n.tt=this),r}se(){let e=this.tt,r=this.U,n=r.W;return e.tt===this?e.tt=r:e.U===this?e.U=r:e.W=r,r.tt=e,r.W=this,this.tt=r,this.U=n,n&&(n.tt=this),r}};wn.TreeNode=Co;var nu=class extends Co{constructor(){super(...arguments),this.rt=1}te(){let e=super.te();return this.ie(),e.ie(),e}se(){let e=super.se();return this.ie(),e.ie(),e}ie(){this.rt=1,this.U&&(this.rt+=this.U.rt),this.W&&(this.rt+=this.W.rt)}};wn.TreeNodeEnableIndex=nu});var ou=D(Bo=>{"use strict";A();x();I();Object.defineProperty(Bo,"t",{value:!0});Bo.default=void 0;var Oy=Py(),i1=It(),Ry=xt(),iu=class extends i1.Container{constructor(e=function(n,i){return n<i?-1:n>i?1:0},r=!1){super(),this.Y=void 0,this.v=e,r?(this.re=Oy.TreeNodeEnableIndex,this.M=function(n,i,s){let o=this.ne(n,i,s);if(o){let a=o.tt;for(;a!==this.h;)a.rt+=1,a=a.tt;let c=this.he(o);if(c){let{parentNode:f,grandParent:h,curNode:d}=c;f.ie(),h.ie(),d.ie()}}return this.i},this.V=function(n){let i=this.fe(n);for(;i!==this.h;)i.rt-=1,i=i.tt}):(this.re=Oy.TreeNode,this.M=function(n,i,s){let o=this.ne(n,i,s);return o&&this.he(o),this.i},this.V=this.fe),this.h=new this.re}X(e,r){let n=this.h;for(;e;){let i=this.v(e.u,r);if(i<0)e=e.W;else if(i>0)n=e,e=e.U;else return e}return n}Z(e,r){let n=this.h;for(;e;)this.v(e.u,r)<=0?e=e.W:(n=e,e=e.U);return n}$(e,r){let n=this.h;for(;e;){let i=this.v(e.u,r);if(i<0)n=e,e=e.W;else if(i>0)e=e.U;else return e}return n}rr(e,r){let n=this.h;for(;e;)this.v(e.u,r)<0?(n=e,e=e.W):e=e.U;return n}ue(e){for(;;){let r=e.tt;if(r===this.h)return;if(e.ee===1){e.ee=0;return}if(e===r.U){let n=r.W;if(n.ee===1)n.ee=0,r.ee=1,r===this.Y?this.Y=r.te():r.te();else if(n.W&&n.W.ee===1){n.ee=r.ee,r.ee=0,n.W.ee=0,r===this.Y?this.Y=r.te():r.te();return}else n.U&&n.U.ee===1?(n.ee=1,n.U.ee=0,n.se()):(n.ee=1,e=r)}else{let n=r.U;if(n.ee===1)n.ee=0,r.ee=1,r===this.Y?this.Y=r.se():r.se();else if(n.U&&n.U.ee===1){n.ee=r.ee,r.ee=0,n.U.ee=0,r===this.Y?this.Y=r.se():r.se();return}else n.W&&n.W.ee===1?(n.ee=1,n.W.ee=0,n.te()):(n.ee=1,e=r)}}}fe(e){if(this.i===1)return this.clear(),this.h;let r=e;for(;r.U||r.W;){if(r.W)for(r=r.W;r.U;)r=r.U;else r=r.U;[e.u,r.u]=[r.u,e.u],[e.l,r.l]=[r.l,e.l],e=r}this.h.U===r?this.h.U=r.tt:this.h.W===r&&(this.h.W=r.tt),this.ue(r);let n=r.tt;return r===n.U?n.U=void 0:n.W=void 0,this.i-=1,this.Y.ee=0,n}oe(e,r){return e===void 0?!1:this.oe(e.U,r)||r(e)?!0:this.oe(e.W,r)}he(e){for(;;){let r=e.tt;if(r.ee===0)return;let n=r.tt;if(r===n.U){let i=n.W;if(i&&i.ee===1){if(i.ee=r.ee=0,n===this.Y)return;n.ee=1,e=n;continue}else if(e===r.W){if(e.ee=0,e.U&&(e.U.tt=r),e.W&&(e.W.tt=n),r.W=e.U,n.U=e.W,e.U=r,e.W=n,n===this.Y)this.Y=e,this.h.tt=e;else{let s=n.tt;s.U===n?s.U=e:s.W=e}return e.tt=n.tt,r.tt=e,n.tt=e,n.ee=1,{parentNode:r,grandParent:n,curNode:e}}else r.ee=0,n===this.Y?this.Y=n.se():n.se(),n.ee=1}else{let i=n.U;if(i&&i.ee===1){if(i.ee=r.ee=0,n===this.Y)return;n.ee=1,e=n;continue}else if(e===r.U){if(e.ee=0,e.U&&(e.U.tt=n),e.W&&(e.W.tt=r),n.W=e.U,r.U=e.W,e.U=n,e.W=r,n===this.Y)this.Y=e,this.h.tt=e;else{let s=n.tt;s.U===n?s.U=e:s.W=e}return e.tt=n.tt,r.tt=e,n.tt=e,n.ee=1,{parentNode:r,grandParent:n,curNode:e}}else r.ee=0,n===this.Y?this.Y=n.te():n.te(),n.ee=1}return}}ne(e,r,n){if(this.Y===void 0){this.i+=1,this.Y=new this.re(e,r),this.Y.ee=0,this.Y.tt=this.h,this.h.tt=this.Y,this.h.U=this.Y,this.h.W=this.Y;return}let i,s=this.h.U,o=this.v(s.u,e);if(o===0){s.l=r;return}else if(o>0)s.U=new this.re(e,r),s.U.tt=s,i=s.U,this.h.U=i;else{let a=this.h.W,c=this.v(a.u,e);if(c===0){a.l=r;return}else if(c<0)a.W=new this.re(e,r),a.W.tt=a,i=a.W,this.h.W=i;else{if(n!==void 0){let f=n.o;if(f!==this.h){let h=this.v(f.u,e);if(h===0){f.l=r;return}else if(h>0){let d=f.L(),w=this.v(d.u,e);if(w===0){d.l=r;return}else w<0&&(i=new this.re(e,r),d.W===void 0?(d.W=i,i.tt=d):(f.U=i,i.tt=f))}}}if(i===void 0)for(i=this.Y;;){let f=this.v(i.u,e);if(f>0){if(i.U===void 0){i.U=new this.re(e,r),i.U.tt=i,i=i.U;break}i=i.U}else if(f<0){if(i.W===void 0){i.W=new this.re(e,r),i.W.tt=i,i=i.W;break}i=i.W}else{i.l=r;return}}}}return this.i+=1,i}I(e,r){for(;e;){let n=this.v(e.u,r);if(n<0)e=e.W;else if(n>0)e=e.U;else return e}return e||this.h}clear(){this.i=0,this.Y=void 0,this.h.tt=void 0,this.h.U=this.h.W=void 0}updateKeyByIterator(e,r){let n=e.o;if(n===this.h&&(0,Ry.throwIteratorAccessError)(),this.i===1)return n.u=r,!0;if(n===this.h.U)return this.v(n.B().u,r)>0?(n.u=r,!0):!1;if(n===this.h.W)return this.v(n.L().u,r)<0?(n.u=r,!0):!1;let i=n.L().u;if(this.v(i,r)>=0)return!1;let s=n.B().u;return this.v(s,r)<=0?!1:(n.u=r,!0)}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=0,n=this;return this.oe(this.Y,function(i){return e===r?(n.V(i),!0):(r+=1,!1)}),this.i}eraseElementByKey(e){if(this.i===0)return!1;let r=this.I(this.Y,e);return r===this.h?!1:(this.V(r),!0)}eraseElementByIterator(e){let r=e.o;r===this.h&&(0,Ry.throwIteratorAccessError)();let n=r.W===void 0;return e.iteratorType===0?n&&e.next():(!n||r.U===void 0)&&e.next(),this.V(r),e}forEach(e){let r=0;for(let n of this)e(n,r++,this)}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r,n=0;for(let i of this){if(n===e){r=i;break}n+=1}return r}getHeight(){if(this.i===0)return 0;let e=function(r){return r?Math.max(e(r.U),e(r.W))+1:0};return e(this.Y)}},o1=iu;Bo.default=o1});var au=D(No=>{"use strict";A();x();I();Object.defineProperty(No,"t",{value:!0});No.default=void 0;var s1=It(),Mo=xt(),su=class extends s1.ContainerIterator{constructor(e,r,n){super(n),this.o=e,this.h=r,this.iteratorType===0?(this.pre=function(){return this.o===this.h.U&&(0,Mo.throwIteratorAccessError)(),this.o=this.o.L(),this},this.next=function(){return this.o===this.h&&(0,Mo.throwIteratorAccessError)(),this.o=this.o.B(),this}):(this.pre=function(){return this.o===this.h.W&&(0,Mo.throwIteratorAccessError)(),this.o=this.o.B(),this},this.next=function(){return this.o===this.h&&(0,Mo.throwIteratorAccessError)(),this.o=this.o.L(),this})}get index(){let e=this.o,r=this.h.tt;if(e===this.h)return r?r.rt-1:0;let n=0;for(e.U&&(n+=e.U.rt);e!==r;){let i=e.tt;e===i.W&&(n+=1,i.U&&(n+=i.U.rt)),e=i}return n}},a1=su;No.default=a1});var Cy=D(qo=>{"use strict";A();x();I();Object.defineProperty(qo,"t",{value:!0});qo.default=void 0;var u1=ky(ou()),l1=ky(au()),c1=xt();function ky(t){return t&&t.t?t:{default:t}}var dt=class t extends l1.default{constructor(e,r,n,i){super(e,r,i),this.container=n}get pointer(){return this.o===this.h&&(0,c1.throwIteratorAccessError)(),this.o.u}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},uu=class extends u1.default{constructor(e=[],r,n){super(r,n);let i=this;e.forEach(function(s){i.insert(s)})}*K(e){e!==void 0&&(yield*this.K(e.U),yield e.u,yield*this.K(e.W))}begin(){return new dt(this.h.U||this.h,this.h,this)}end(){return new dt(this.h,this.h,this)}rBegin(){return new dt(this.h.W||this.h,this.h,this,1)}rEnd(){return new dt(this.h,this.h,this,1)}front(){return this.h.U?this.h.U.u:void 0}back(){return this.h.W?this.h.W.u:void 0}insert(e,r){return this.M(e,void 0,r)}find(e){let r=this.I(this.Y,e);return new dt(r,this.h,this)}lowerBound(e){let r=this.X(this.Y,e);return new dt(r,this.h,this)}upperBound(e){let r=this.Z(this.Y,e);return new dt(r,this.h,this)}reverseLowerBound(e){let r=this.$(this.Y,e);return new dt(r,this.h,this)}reverseUpperBound(e){let r=this.rr(this.Y,e);return new dt(r,this.h,this)}union(e){let r=this;return e.forEach(function(n){r.insert(n)}),this.i}[Symbol.iterator](){return this.K(this.Y)}},f1=uu;qo.default=f1});var My=D(Lo=>{"use strict";A();x();I();Object.defineProperty(Lo,"t",{value:!0});Lo.default=void 0;var h1=By(ou()),d1=By(au()),p1=xt();function By(t){return t&&t.t?t:{default:t}}var pt=class t extends d1.default{constructor(e,r,n,i){super(e,r,i),this.container=n}get pointer(){this.o===this.h&&(0,p1.throwIteratorAccessError)();let e=this;return new Proxy([],{get(r,n){if(n==="0")return e.o.u;if(n==="1")return e.o.l},set(r,n,i){if(n!=="1")throw new TypeError("props must be 1");return e.o.l=i,!0}})}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},lu=class extends h1.default{constructor(e=[],r,n){super(r,n);let i=this;e.forEach(function(s){i.setElement(s[0],s[1])})}*K(e){e!==void 0&&(yield*this.K(e.U),yield[e.u,e.l],yield*this.K(e.W))}begin(){return new pt(this.h.U||this.h,this.h,this)}end(){return new pt(this.h,this.h,this)}rBegin(){return new pt(this.h.W||this.h,this.h,this,1)}rEnd(){return new pt(this.h,this.h,this,1)}front(){if(this.i===0)return;let e=this.h.U;return[e.u,e.l]}back(){if(this.i===0)return;let e=this.h.W;return[e.u,e.l]}lowerBound(e){let r=this.X(this.Y,e);return new pt(r,this.h,this)}upperBound(e){let r=this.Z(this.Y,e);return new pt(r,this.h,this)}reverseLowerBound(e){let r=this.$(this.Y,e);return new pt(r,this.h,this)}reverseUpperBound(e){let r=this.rr(this.Y,e);return new pt(r,this.h,this)}setElement(e,r,n){return this.M(e,r,n)}find(e){let r=this.I(this.Y,e);return new pt(r,this.h,this)}getElementByKey(e){return this.I(this.Y,e).l}union(e){let r=this;return e.forEach(function(n){r.setElement(n[0],n[1])}),this.i}[Symbol.iterator](){return this.K(this.Y)}},y1=lu;Lo.default=y1});var fu=D(cu=>{"use strict";A();x();I();Object.defineProperty(cu,"t",{value:!0});cu.default=g1;function g1(t){let e=typeof t;return e==="object"&&t!==null||e==="function"}});var yu=D(mn=>{"use strict";A();x();I();Object.defineProperty(mn,"t",{value:!0});mn.HashContainerIterator=mn.HashContainer=void 0;var Ny=It(),hu=b1(fu()),Kn=xt();function b1(t){return t&&t.t?t:{default:t}}var du=class extends Ny.ContainerIterator{constructor(e,r,n){super(n),this.o=e,this.h=r,this.iteratorType===0?(this.pre=function(){return this.o.L===this.h&&(0,Kn.throwIteratorAccessError)(),this.o=this.o.L,this},this.next=function(){return this.o===this.h&&(0,Kn.throwIteratorAccessError)(),this.o=this.o.B,this}):(this.pre=function(){return this.o.B===this.h&&(0,Kn.throwIteratorAccessError)(),this.o=this.o.B,this},this.next=function(){return this.o===this.h&&(0,Kn.throwIteratorAccessError)(),this.o=this.o.L,this})}};mn.HashContainerIterator=du;var pu=class extends Ny.Container{constructor(){super(),this.H=[],this.g={},this.HASH_TAG=Symbol("@@HASH_TAG"),Object.setPrototypeOf(this.g,null),this.h={},this.h.L=this.h.B=this.p=this._=this.h}V(e){let{L:r,B:n}=e;r.B=n,n.L=r,e===this.p&&(this.p=n),e===this._&&(this._=r),this.i-=1}M(e,r,n){n===void 0&&(n=(0,hu.default)(e));let i;if(n){let s=e[this.HASH_TAG];if(s!==void 0)return this.H[s].l=r,this.i;Object.defineProperty(e,this.HASH_TAG,{value:this.H.length,configurable:!0}),i={u:e,l:r,L:this._,B:this.h},this.H.push(i)}else{let s=this.g[e];if(s)return s.l=r,this.i;i={u:e,l:r,L:this._,B:this.h},this.g[e]=i}return this.i===0?(this.p=i,this.h.B=i):this._.B=i,this._=i,this.h.L=i,++this.i}I(e,r){if(r===void 0&&(r=(0,hu.default)(e)),r){let n=e[this.HASH_TAG];return n===void 0?this.h:this.H[n]}else return this.g[e]||this.h}clear(){let e=this.HASH_TAG;this.H.forEach(function(r){delete r.u[e]}),this.H=[],this.g={},Object.setPrototypeOf(this.g,null),this.i=0,this.p=this._=this.h.L=this.h.B=this.h}eraseElementByKey(e,r){let n;if(r===void 0&&(r=(0,hu.default)(e)),r){let i=e[this.HASH_TAG];if(i===void 0)return!1;delete e[this.HASH_TAG],n=this.H[i],delete this.H[i]}else{if(n=this.g[e],n===void 0)return!1;delete this.g[e]}return this.V(n),!0}eraseElementByIterator(e){let r=e.o;return r===this.h&&(0,Kn.throwIteratorAccessError)(),this.V(r),e.next()}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return this.V(r),this.i}};mn.HashContainer=pu});var Ly=D(Uo=>{"use strict";A();x();I();Object.defineProperty(Uo,"t",{value:!0});Uo.default=void 0;var qy=yu(),w1=xt(),Dr=class t extends qy.HashContainerIterator{constructor(e,r,n,i){super(e,r,i),this.container=n}get pointer(){return this.o===this.h&&(0,w1.throwIteratorAccessError)(),this.o.u}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},gu=class extends qy.HashContainer{constructor(e=[]){super();let r=this;e.forEach(function(n){r.insert(n)})}begin(){return new Dr(this.p,this.h,this)}end(){return new Dr(this.h,this.h,this)}rBegin(){return new Dr(this._,this.h,this,1)}rEnd(){return new Dr(this.h,this.h,this,1)}front(){return this.p.u}back(){return this._.u}insert(e,r){return this.M(e,void 0,r)}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return r.u}find(e,r){let n=this.I(e,r);return new Dr(n,this.h,this)}forEach(e){let r=0,n=this.p;for(;n!==this.h;)e(n.u,r++,this),n=n.B}[Symbol.iterator](){return function*(){let e=this.p;for(;e!==this.h;)yield e.u,e=e.B}.bind(this)()}},m1=gu;Uo.default=m1});var Dy=D(Do=>{"use strict";A();x();I();Object.defineProperty(Do,"t",{value:!0});Do.default=void 0;var Uy=yu(),_1=S1(fu()),v1=xt();function S1(t){return t&&t.t?t:{default:t}}var jr=class t extends Uy.HashContainerIterator{constructor(e,r,n,i){super(e,r,i),this.container=n}get pointer(){this.o===this.h&&(0,v1.throwIteratorAccessError)();let e=this;return new Proxy([],{get(r,n){if(n==="0")return e.o.u;if(n==="1")return e.o.l},set(r,n,i){if(n!=="1")throw new TypeError("props must be 1");return e.o.l=i,!0}})}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},bu=class extends Uy.HashContainer{constructor(e=[]){super();let r=this;e.forEach(function(n){r.setElement(n[0],n[1])})}begin(){return new jr(this.p,this.h,this)}end(){return new jr(this.h,this.h,this)}rBegin(){return new jr(this._,this.h,this,1)}rEnd(){return new jr(this.h,this.h,this,1)}front(){if(this.i!==0)return[this.p.u,this.p.l]}back(){if(this.i!==0)return[this._.u,this._.l]}setElement(e,r,n){return this.M(e,r,n)}getElementByKey(e,r){if(r===void 0&&(r=(0,_1.default)(e)),r){let i=e[this.HASH_TAG];return i!==void 0?this.H[i].l:void 0}let n=this.g[e];return n?n.l:void 0}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return[r.u,r.l]}find(e,r){let n=this.I(e,r);return new jr(n,this.h,this)}forEach(e){let r=0,n=this.p;for(;n!==this.h;)e([n.u,n.l],r++,this),n=n.B}[Symbol.iterator](){return function*(){let e=this.p;for(;e!==this.h;)yield[e.u,e.l],e=e.B}.bind(this)()}},E1=bu;Do.default=E1});var jy=D(it=>{"use strict";A();x();I();Object.defineProperty(it,"t",{value:!0});Object.defineProperty(it,"Deque",{enumerable:!0,get:function(){return O1.default}});Object.defineProperty(it,"HashMap",{enumerable:!0,get:function(){return B1.default}});Object.defineProperty(it,"HashSet",{enumerable:!0,get:function(){return C1.default}});Object.defineProperty(it,"LinkList",{enumerable:!0,get:function(){return P1.default}});Object.defineProperty(it,"OrderedMap",{enumerable:!0,get:function(){return k1.default}});Object.defineProperty(it,"OrderedSet",{enumerable:!0,get:function(){return R1.default}});Object.defineProperty(it,"PriorityQueue",{enumerable:!0,get:function(){return x1.default}});Object.defineProperty(it,"Queue",{enumerable:!0,get:function(){return I1.default}});Object.defineProperty(it,"Stack",{enumerable:!0,get:function(){return A1.default}});Object.defineProperty(it,"Vector",{enumerable:!0,get:function(){return T1.default}});var A1=Tt(Sy()),I1=Tt(Ey()),x1=Tt(Ay()),T1=Tt(Iy()),P1=Tt(xy()),O1=Tt(Ty()),R1=Tt(Cy()),k1=Tt(My()),C1=Tt(Ly()),B1=Tt(Dy());function Tt(t){return t&&t.t?t:{default:t}}});var Wy=D((cj,Fy)=>{"use strict";A();x();I();var M1=jy().OrderedSet,Pt=At()("number-allocator:trace"),N1=At()("number-allocator:error");function Ve(t,e){this.low=t,this.high=e}Ve.prototype.equals=function(t){return this.low===t.low&&this.high===t.high};Ve.prototype.compare=function(t){return this.low<t.low&&this.high<t.low?-1:t.low<this.low&&t.high<this.low?1:0};function Ot(t,e){if(!(this instanceof Ot))return new Ot(t,e);this.min=t,this.max=e,this.ss=new M1([],(r,n)=>r.compare(n)),Pt("Create"),this.clear()}Ot.prototype.firstVacant=function(){return this.ss.size()===0?null:this.ss.front().low};Ot.prototype.alloc=function(){if(this.ss.size()===0)return Pt("alloc():empty"),null;let t=this.ss.begin(),e=t.pointer.low,r=t.pointer.high,n=e;return n+1<=r?this.ss.updateKeyByIterator(t,new Ve(e+1,r)):this.ss.eraseElementByPos(0),Pt("alloc():"+n),n};Ot.prototype.use=function(t){let e=new Ve(t,t),r=this.ss.lowerBound(e);if(!r.equals(this.ss.end())){let n=r.pointer.low,i=r.pointer.high;return r.pointer.equals(e)?(this.ss.eraseElementByIterator(r),Pt("use():"+t),!0):n>t?!1:n===t?(this.ss.updateKeyByIterator(r,new Ve(n+1,i)),Pt("use():"+t),!0):i===t?(this.ss.updateKeyByIterator(r,new Ve(n,i-1)),Pt("use():"+t),!0):(this.ss.updateKeyByIterator(r,new Ve(t+1,i)),this.ss.insert(new Ve(n,t-1)),Pt("use():"+t),!0)}return Pt("use():failed"),!1};Ot.prototype.free=function(t){if(t<this.min||t>this.max){N1("free():"+t+" is out of range");return}let e=new Ve(t,t),r=this.ss.upperBound(e);if(r.equals(this.ss.end())){if(r.equals(this.ss.begin())){this.ss.insert(e);return}r.pre();let n=r.pointer.high;r.pointer.high+1===t?this.ss.updateKeyByIterator(r,new Ve(n,t)):this.ss.insert(e)}else if(r.equals(this.ss.begin()))if(t+1===r.pointer.low){let n=r.pointer.high;this.ss.updateKeyByIterator(r,new Ve(t,n))}else this.ss.insert(e);else{let n=r.pointer.low,i=r.pointer.high;r.pre();let s=r.pointer.low;r.pointer.high+1===t?t+1===n?(this.ss.eraseElementByIterator(r),this.ss.updateKeyByIterator(r,new Ve(s,i))):this.ss.updateKeyByIterator(r,new Ve(s,t)):t+1===n?(this.ss.eraseElementByIterator(r.next()),this.ss.insert(new Ve(t,i))):this.ss.insert(e)}Pt("free():"+t)};Ot.prototype.clear=function(){Pt("clear()"),this.ss.clear(),this.ss.insert(new Ve(this.min,this.max))};Ot.prototype.intervalCount=function(){return this.ss.size()};Ot.prototype.dump=function(){console.log("length:"+this.ss.size());for(let t of this.ss)console.log(t)};Fy.exports=Ot});var wu=D((_j,$y)=>{A();x();I();var q1=Wy();$y.exports.NumberAllocator=q1});var Hy=D(_u=>{"use strict";A();x();I();Object.defineProperty(_u,"__esModule",{value:!0});var L1=vy(),U1=wu(),mu=class{aliasToTopic;topicToAlias;max;numberAllocator;length;constructor(e){e>0&&(this.aliasToTopic=new L1.LRUCache({max:e}),this.topicToAlias={},this.numberAllocator=new U1.NumberAllocator(1,e),this.max=e,this.length=0)}put(e,r){if(r===0||r>this.max)return!1;let n=this.aliasToTopic.get(r);return n&&delete this.topicToAlias[n],this.aliasToTopic.set(r,e),this.topicToAlias[e]=r,this.numberAllocator.use(r),this.length=this.aliasToTopic.size,!0}getTopicByAlias(e){return this.aliasToTopic.get(e)}getAliasByTopic(e){let r=this.topicToAlias[e];return typeof r<"u"&&this.aliasToTopic.get(r),r}clear(){this.aliasToTopic.clear(),this.topicToAlias={},this.numberAllocator.clear(),this.length=0}getLruAlias(){let e=this.numberAllocator.firstVacant();return e||[...this.aliasToTopic.keys()][this.aliasToTopic.size-1]}};_u.default=mu});var Vy=D(Qn=>{"use strict";A();x();I();var D1=Qn&&Qn.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Qn,"__esModule",{value:!0});var j1=zn(),F1=D1(Hy()),W1=Mr(),$1=(t,e)=>{t.log("_handleConnack");let{options:r}=t,i=r.protocolVersion===5?e.reasonCode:e.returnCode;if(clearTimeout(t.connackTimer),delete t.topicAliasSend,e.properties){if(e.properties.topicAliasMaximum){if(e.properties.topicAliasMaximum>65535){t.emit("error",new Error("topicAliasMaximum from broker is out of range"));return}e.properties.topicAliasMaximum>0&&(t.topicAliasSend=new F1.default(e.properties.topicAliasMaximum))}e.properties.serverKeepAlive&&r.keepalive&&(r.keepalive=e.properties.serverKeepAlive),e.properties.maximumPacketSize&&(r.properties||(r.properties={}),r.properties.maximumPacketSize=e.properties.maximumPacketSize)}if(i===0)t.reconnecting=!1,t._onConnect(e);else if(i>0){let s=new W1.ErrorWithReasonCode(`Connection refused: ${j1.ReasonCodes[i]}`,i);t.emit("error",s),t.options.reconnectOnConnackError&&t._cleanUp(!0)}};Qn.default=$1});var Gy=D(vu=>{"use strict";A();x();I();Object.defineProperty(vu,"__esModule",{value:!0});var H1=(t,e,r)=>{t.log("handling pubrel packet");let n=typeof r<"u"?r:t.noop,{messageId:i}=e,s={cmd:"pubcomp",messageId:i};t.incomingStore.get(e,(o,a)=>{o?t._sendPacket(s,n):(t.emit("message",a.topic,a.payload,a),t.handleMessage(a,c=>{if(c)return n(c);t.incomingStore.del(a,t.noop),t._sendPacket(s,n)}))})};vu.default=H1});var zy=D(Yn=>{"use strict";A();x();I();var Jn=Yn&&Yn.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Yn,"__esModule",{value:!0});var V1=Jn(hy()),G1=Jn(gy()),z1=Jn(Vy()),K1=Jn(zn()),Q1=Jn(Gy()),Y1=(t,e,r)=>{let{options:n}=t;if(n.protocolVersion===5&&n.properties&&n.properties.maximumPacketSize&&n.properties.maximumPacketSize<e.length)return t.emit("error",new Error(`exceeding packets size ${e.cmd}`)),t.end({reasonCode:149,properties:{reasonString:"Maximum packet size was exceeded"}}),t;switch(t.log("_handlePacket :: emitting packetreceive"),t.emit("packetreceive",e),e.cmd){case"publish":(0,V1.default)(t,e,r);break;case"puback":case"pubrec":case"pubcomp":case"suback":case"unsuback":t.reschedulePing(),(0,K1.default)(t,e),r();break;case"pubrel":t.reschedulePing(),(0,Q1.default)(t,e,r);break;case"connack":(0,z1.default)(t,e),r();break;case"auth":t.reschedulePing(),(0,G1.default)(t,e),r();break;case"pingresp":t.log("_handlePacket :: received pingresp"),t.reschedulePing(!0),r();break;case"disconnect":t.emit("disconnect",e),r();break;default:t.log("_handlePacket :: unknown command"),r();break}};Yn.default=Y1});var Au=D(Eu=>{"use strict";A();x();I();Object.defineProperty(Eu,"__esModule",{value:!0});var Su=class{nextId;constructor(){this.nextId=Math.max(1,Math.floor(Math.random()*65535))}allocate(){let e=this.nextId++;return this.nextId===65536&&(this.nextId=1),e}getLastAllocated(){return this.nextId===1?65535:this.nextId-1}register(e){return!0}deallocate(e){}clear(){}};Eu.default=Su});var Ky=D(xu=>{"use strict";A();x();I();Object.defineProperty(xu,"__esModule",{value:!0});var Iu=class{aliasToTopic;max;length;constructor(e){this.aliasToTopic={},this.max=e}put(e,r){return r===0||r>this.max?!1:(this.aliasToTopic[r]=e,this.length=Object.keys(this.aliasToTopic).length,!0)}getTopicByAlias(e){return this.aliasToTopic[e]}clear(){this.aliasToTopic={}}};xu.default=Iu});var Qy=D(_n=>{"use strict";A();x();I();var J1=_n&&_n.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(_n,"__esModule",{value:!0});_n.TypedEventEmitter=void 0;var X1=J1((er(),me(Zt))),Z1=Mr(),jo=class{};_n.TypedEventEmitter=jo;(0,Z1.applyMixin)(jo,X1.default)});var Xn=D((GF,Wt)=>{A();x();I();function Tu(t){"@babel/helpers - typeof";return Wt.exports=Tu=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Wt.exports.__esModule=!0,Wt.exports.default=Wt.exports,Tu(t)}Wt.exports=Tu,Wt.exports.__esModule=!0,Wt.exports.default=Wt.exports});var Jy=D((r8,Zn)=>{A();x();I();var Yy=Xn().default;function eA(t,e){if(Yy(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Yy(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}Zn.exports=eA,Zn.exports.__esModule=!0,Zn.exports.default=Zn.exports});var Xy=D((h8,ei)=>{A();x();I();var tA=Xn().default,rA=Jy();function nA(t){var e=rA(t,"string");return tA(e)=="symbol"?e:e+""}ei.exports=nA,ei.exports.__esModule=!0,ei.exports.default=ei.exports});var Zy=D((S8,ti)=>{A();x();I();var iA=Xy();function oA(t,e,r){return(e=iA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}ti.exports=oA,ti.exports.__esModule=!0,ti.exports.default=ti.exports});var eg=D((C8,ri)=>{A();x();I();function sA(t){if(Array.isArray(t))return t}ri.exports=sA,ri.exports.__esModule=!0,ri.exports.default=ri.exports});var tg=D((W8,ni)=>{A();x();I();function aA(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,s,o,a=[],c=!0,f=!1;try{if(s=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=s.call(r)).done)&&(a.push(n.value),a.length!==e);c=!0);}catch(h){f=!0,i=h}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(f)throw i}}return a}}ni.exports=aA,ni.exports.__esModule=!0,ni.exports.default=ni.exports});var rg=D((X8,ii)=>{A();x();I();function uA(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}ii.exports=uA,ii.exports.__esModule=!0,ii.exports.default=ii.exports});var ig=D((u6,oi)=>{A();x();I();var ng=rg();function lA(t,e){if(t){if(typeof t=="string")return ng(t,e);var r={}.toString.call(t).slice(8,-1);return r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set"?Array.from(t):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ng(t,e):void 0}}oi.exports=lA,oi.exports.__esModule=!0,oi.exports.default=oi.exports});var og=D((w6,si)=>{A();x();I();function cA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}si.exports=cA,si.exports.__esModule=!0,si.exports.default=si.exports});var sg=D((P6,ai)=>{A();x();I();var fA=eg(),hA=tg(),dA=ig(),pA=og();function yA(t,e){return fA(t)||hA(t,e)||dA(t,e)||pA()}ai.exports=yA,ai.exports.__esModule=!0,ai.exports.default=ai.exports});var Pu=D((Fo,ag)=>{A();x();I();(function(t,e){typeof Fo=="object"&&typeof ag<"u"?e(Fo):typeof define=="function"&&define.amd?define(["exports"],e):(t=typeof globalThis<"u"?globalThis:t||self,e(t.fastUniqueNumbers={}))})(Fo,function(t){"use strict";var e=function(w){return function(y){var P=w(y);return y.add(P),P}},r=function(w){return function(y,P){return w.set(y,P),P}},n=Number.MAX_SAFE_INTEGER===void 0?9007199254740991:Number.MAX_SAFE_INTEGER,i=536870912,s=i*2,o=function(w,y){return function(P){var E=y.get(P),b=E===void 0?P.size:E<s?E+1:0;if(!P.has(b))return w(P,b);if(P.size<i){for(;P.has(b);)b=Math.floor(Math.random()*s);return w(P,b)}if(P.size>n)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;P.has(b);)b=Math.floor(Math.random()*n);return w(P,b)}},a=new WeakMap,c=r(a),f=o(c,a),h=e(f);t.addUniqueNumber=h,t.generateUniqueNumber=f})});var lg=D((z6,ui)=>{A();x();I();function ug(t,e,r,n,i,s,o){try{var a=t[s](o),c=a.value}catch(f){return void r(f)}a.done?e(c):Promise.resolve(c).then(n,i)}function gA(t){return function(){var e=this,r=arguments;return new Promise(function(n,i){var s=t.apply(e,r);function o(c){ug(s,n,i,o,a,"next",c)}function a(c){ug(s,n,i,o,a,"throw",c)}o(void 0)})}}ui.exports=gA,ui.exports.__esModule=!0,ui.exports.default=ui.exports});var Ou=D((nW,li)=>{A();x();I();function bA(t,e){this.v=t,this.k=e}li.exports=bA,li.exports.__esModule=!0,li.exports.default=li.exports});var Ru=D((dW,$t)=>{A();x();I();function Wo(t,e,r,n){var i=Object.defineProperty;try{i({},"",{})}catch{i=0}$t.exports=Wo=function(o,a,c,f){function h(d,w){Wo(o,d,function(y){return this._invoke(d,w,y)})}a?i?i(o,a,{value:c,enumerable:!f,configurable:!f,writable:!f}):o[a]=c:(h("next",0),h("throw",1),h("return",2))},$t.exports.__esModule=!0,$t.exports.default=$t.exports,Wo(t,e,r,n)}$t.exports=Wo,$t.exports.__esModule=!0,$t.exports.default=$t.exports});var ku=D((EW,Ht)=>{A();x();I();var Rt=Ru();function cg(){var t,e,r=typeof Symbol=="function"?Symbol:{},n=r.iterator||"@@iterator",i=r.toStringTag||"@@toStringTag";function s(y,P,E,b){var m=P&&P.prototype instanceof a?P:a,v=Object.create(m.prototype);return Rt(v,"_invoke",function(R,T,g){var O,_,q,$=0,j=g||[],J=!1,W={p:0,n:0,v:t,a:H,f:H.bind(t,4),d:function(K,X){return O=K,_=0,q=t,W.n=X,o}};function H(G,K){for(_=G,q=K,e=0;!J&&$&&!X&&e<j.length;e++){var X,Q=j[e],V=W.p,te=Q[2];G>3?(X=te===K)&&(q=Q[(_=Q[4])?5:(_=3,3)],Q[4]=Q[5]=t):Q[0]<=V&&((X=G<2&&V<Q[1])?(_=0,W.v=K,W.n=Q[1]):V<te&&(X=G<3||Q[0]>K||K>te)&&(Q[4]=G,Q[5]=K,W.n=te,_=0))}if(X||G>1)return o;throw J=!0,K}return function(G,K,X){if($>1)throw TypeError("Generator is already running");for(J&&K===1&&H(K,X),_=K,q=X;(e=_<2?t:q)||!J;){O||(_?_<3?(_>1&&(W.n=-1),H(_,q)):W.n=q:W.v=q);try{if($=2,O){if(_||(G="next"),e=O[G]){if(!(e=e.call(O,q)))throw TypeError("iterator result is not an object");if(!e.done)return e;q=e.value,_<2&&(_=0)}else _===1&&(e=O.return)&&e.call(O),_<2&&(q=TypeError("The iterator does not provide a '"+G+"' method"),_=1);O=t}else if((e=(J=W.n<0)?q:R.call(T,W))!==o)break}catch(Q){O=t,_=1,q=Q}finally{$=1}}return{value:e,done:J}}}(y,E,b),!0),v}var o={};function a(){}function c(){}function f(){}e=Object.getPrototypeOf;var h=[][n]?e(e([][n]())):(Rt(e={},n,function(){return this}),e),d=f.prototype=a.prototype=Object.create(h);function w(y){return Object.setPrototypeOf?Object.setPrototypeOf(y,f):(y.__proto__=f,Rt(y,i,"GeneratorFunction")),y.prototype=Object.create(d),y}return c.prototype=f,Rt(d,"constructor",f),Rt(f,"constructor",c),c.displayName="GeneratorFunction",Rt(f,i,"GeneratorFunction"),Rt(d),Rt(d,i,"Generator"),Rt(d,n,function(){return this}),Rt(d,"toString",function(){return"[object Generator]"}),(Ht.exports=cg=function(){return{w:s,m:w}},Ht.exports.__esModule=!0,Ht.exports.default=Ht.exports)()}Ht.exports=cg,Ht.exports.__esModule=!0,Ht.exports.default=Ht.exports});var Mu=D((BW,ci)=>{A();x();I();var wA=Ou(),Cu=Ru();function Bu(t,e){function r(i,s,o,a){try{var c=t[i](s),f=c.value;return f instanceof wA?e.resolve(f.v).then(function(h){r("next",h,o,a)},function(h){r("throw",h,o,a)}):e.resolve(f).then(function(h){c.value=h,o(c)},function(h){return r("throw",h,o,a)})}catch(h){a(h)}}var n;this.next||(Cu(Bu.prototype),Cu(Bu.prototype,typeof Symbol=="function"&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),Cu(this,"_invoke",function(i,s,o){function a(){return new e(function(c,f){r(i,o,c,f)})}return n=n?n.then(a,a):a()},!0)}ci.exports=Bu,ci.exports.__esModule=!0,ci.exports.default=ci.exports});var Nu=D(($W,fi)=>{A();x();I();var mA=ku(),_A=Mu();function vA(t,e,r,n,i){return new _A(mA().w(t,e,r,n),i||Promise)}fi.exports=vA,fi.exports.__esModule=!0,fi.exports.default=fi.exports});var fg=D((ZW,hi)=>{A();x();I();var SA=Nu();function EA(t,e,r,n,i){var s=SA(t,e,r,n,i);return s.next().then(function(o){return o.done?o.value:s.next()})}hi.exports=EA,hi.exports.__esModule=!0,hi.exports.default=hi.exports});var hg=D((l4,di)=>{A();x();I();function AA(t){var e=Object(t),r=[];for(var n in e)r.unshift(n);return function i(){for(;r.length;)if((n=r.pop())in e)return i.value=n,i.done=!1,i;return i.done=!0,i}}di.exports=AA,di.exports.__esModule=!0,di.exports.default=di.exports});var dg=D((m4,pi)=>{A();x();I();var IA=Xn().default;function xA(t){if(t!=null){var e=t[typeof Symbol=="function"&&Symbol.iterator||"@@iterator"],r=0;if(e)return e.call(t);if(typeof t.next=="function")return t;if(!isNaN(t.length))return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}}throw new TypeError(IA(t)+" is not iterable")}pi.exports=xA,pi.exports.__esModule=!0,pi.exports.default=pi.exports});var yg=D((O4,Vt)=>{A();x();I();var TA=Ou(),PA=ku(),OA=fg(),RA=Nu(),kA=Mu(),CA=hg(),pg=dg();function qu(){"use strict";var t=PA(),e=t.m(qu),r=(Object.getPrototypeOf?Object.getPrototypeOf(e):e.__proto__).constructor;function n(o){var a=typeof o=="function"&&o.constructor;return!!a&&(a===r||(a.displayName||a.name)==="GeneratorFunction")}var i={throw:1,return:2,break:3,continue:3};function s(o){var a,c;return function(f){a||(a={stop:function(){return c(f.a,2)},catch:function(){return f.v},abrupt:function(d,w){return c(f.a,i[d],w)},delegateYield:function(d,w,y){return a.resultName=w,c(f.d,pg(d),y)},finish:function(d){return c(f.f,d)}},c=function(d,w,y){f.p=a.prev,f.n=a.next;try{return d(w,y)}finally{a.next=f.n}}),a.resultName&&(a[a.resultName]=f.v,a.resultName=void 0),a.sent=f.v,a.next=f.n;try{return o.call(this,a)}finally{f.p=a.prev,f.n=a.next}}}return(Vt.exports=qu=function(){return{wrap:function(c,f,h,d){return t.w(s(c),f,h,d&&d.reverse())},isGeneratorFunction:n,mark:t.m,awrap:function(c,f){return new TA(c,f)},AsyncIterator:kA,async:function(c,f,h,d,w){return(n(f)?RA:OA)(s(c),f,h,d,w)},keys:CA,values:pg}},Vt.exports.__esModule=!0,Vt.exports.default=Vt.exports)()}Vt.exports=qu,Vt.exports.__esModule=!0,Vt.exports.default=Vt.exports});var bg=D((D4,gg)=>{A();x();I();var $o=yg()();gg.exports=$o;try{regeneratorRuntime=$o}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=$o:Function("r","regeneratorRuntime = r")($o)}});var mg=D((Ho,wg)=>{A();x();I();(function(t,e){typeof Ho=="object"&&typeof wg<"u"?e(Ho,Zy(),sg(),Pu(),lg(),bg()):typeof define=="function"&&define.amd?define(["exports","@babel/runtime/helpers/defineProperty","@babel/runtime/helpers/slicedToArray","fast-unique-numbers","@babel/runtime/helpers/asyncToGenerator","@babel/runtime/regenerator"],e):(t=typeof globalThis<"u"?globalThis:t||self,e(t.brokerFactory={},t._defineProperty,t._slicedToArray,t.fastUniqueNumbers,t._asyncToGenerator,t._regeneratorRuntime))})(Ho,function(t,e,r,n,i,s){"use strict";var o=function(m){return typeof m.start=="function"},a=new WeakMap;function c(b,m){var v=Object.keys(b);if(Object.getOwnPropertySymbols){var R=Object.getOwnPropertySymbols(b);m&&(R=R.filter(function(T){return Object.getOwnPropertyDescriptor(b,T).enumerable})),v.push.apply(v,R)}return v}function f(b){for(var m=1;m<arguments.length;m++){var v=arguments[m]!=null?arguments[m]:{};m%2?c(Object(v),!0).forEach(function(R){e(b,R,v[R])}):Object.getOwnPropertyDescriptors?Object.defineProperties(b,Object.getOwnPropertyDescriptors(v)):c(Object(v)).forEach(function(R){Object.defineProperty(b,R,Object.getOwnPropertyDescriptor(v,R))})}return b}var h=function(m){return f(f({},m),{},{connect:function(R){var T=R.call;return i(s.mark(function g(){var O,_,q,$;return s.wrap(function(j){for(;;)switch(j.prev=j.next){case 0:return O=new MessageChannel,_=O.port1,q=O.port2,j.next=1,T("connect",{port:_},[_]);case 1:return $=j.sent,a.set(q,$),j.abrupt("return",q);case 2:case"end":return j.stop()}},g)}))},disconnect:function(R){var T=R.call;return function(){var g=i(s.mark(function O(_){var q;return s.wrap(function($){for(;;)switch($.prev=$.next){case 0:if(q=a.get(_),q!==void 0){$.next=1;break}throw new Error("The given port is not connected.");case 1:return $.next=2,T("disconnect",{portId:q});case 2:case"end":return $.stop()}},O)}));return function(O){return g.apply(this,arguments)}}()},isSupported:function(R){var T=R.call;return function(){return T("isSupported")}}})};function d(b,m){var v=Object.keys(b);if(Object.getOwnPropertySymbols){var R=Object.getOwnPropertySymbols(b);m&&(R=R.filter(function(T){return Object.getOwnPropertyDescriptor(b,T).enumerable})),v.push.apply(v,R)}return v}function w(b){for(var m=1;m<arguments.length;m++){var v=arguments[m]!=null?arguments[m]:{};m%2?d(Object(v),!0).forEach(function(R){e(b,R,v[R])}):Object.getOwnPropertyDescriptors?Object.defineProperties(b,Object.getOwnPropertyDescriptors(v)):d(Object(v)).forEach(function(R){Object.defineProperty(b,R,Object.getOwnPropertyDescriptor(v,R))})}return b}var y=new WeakMap,P=function(m){if(y.has(m))return y.get(m);var v=new Map;return y.set(m,v),v},E=function(m){var v=h(m);return function(R){var T=P(R);R.addEventListener("message",function(H){var G=H.data,K=G.id;if(K!==null&&T.has(K)){var X=T.get(K),Q=X.reject,V=X.resolve;T.delete(K),G.error===void 0?V(G.result):Q(new Error(G.error.message))}}),o(R)&&R.start();for(var g=function(G){var K=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,X=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];return new Promise(function(Q,V){var te=n.generateUniqueNumber(T);T.set(te,{reject:V,resolve:Q}),K===null?R.postMessage({id:te,method:G},X):R.postMessage({id:te,method:G,params:K},X)})},O=function(G,K){var X=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];R.postMessage({id:null,method:G,params:K},X)},_={},q=0,$=Object.entries(v);q<$.length;q++){var j=r($[q],2),J=j[0],W=j[1];_=w(w({},_),{},e({},J,W({call:g,notify:O})))}return w({},_)}};t.createBroker=E})});var vg=D((Vo,_g)=>{A();x();I();(function(t,e){typeof Vo=="object"&&typeof _g<"u"?e(Vo,Xn(),mg(),Pu()):typeof define=="function"&&define.amd?define(["exports","@babel/runtime/helpers/typeof","broker-factory","fast-unique-numbers"],e):(t=typeof globalThis<"u"?globalThis:t||self,e(t.workerTimersBroker={},t._typeof,t.brokerFactory,t.fastUniqueNumbers))})(Vo,function(t,e,r,n){"use strict";var i=new Map([[0,null]]),s=new Map([[0,null]]),o=r.createBroker({clearInterval:function(f){var h=f.call;return function(d){e(i.get(d))==="symbol"&&(i.set(d,null),h("clear",{timerId:d,timerType:"interval"}).then(function(){i.delete(d)}))}},clearTimeout:function(f){var h=f.call;return function(d){e(s.get(d))==="symbol"&&(s.set(d,null),h("clear",{timerId:d,timerType:"timeout"}).then(function(){s.delete(d)}))}},setInterval:function(f){var h=f.call;return function(d){for(var w=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,y=arguments.length,P=new Array(y>2?y-2:0),E=2;E<y;E++)P[E-2]=arguments[E];var b=Symbol(),m=n.generateUniqueNumber(i);i.set(m,b);var v=function(){return h("set",{delay:w,now:performance.timeOrigin+performance.now(),timerId:m,timerType:"interval"}).then(function(){var T=i.get(m);if(T===void 0)throw new Error("The timer is in an undefined state.");T===b&&(d.apply(void 0,P),i.get(m)===b&&v())})};return v(),m}},setTimeout:function(f){var h=f.call;return function(d){for(var w=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,y=arguments.length,P=new Array(y>2?y-2:0),E=2;E<y;E++)P[E-2]=arguments[E];var b=Symbol(),m=n.generateUniqueNumber(s);return s.set(m,b),h("set",{delay:w,now:performance.timeOrigin+performance.now(),timerId:m,timerType:"timeout"}).then(function(){var v=s.get(m);if(v===void 0)throw new Error("The timer is in an undefined state.");v===b&&(s.delete(m),d.apply(void 0,P))}),m}}}),a=function(f){var h=new Worker(f);return o(h)};t.load=a,t.wrap=o})});var Eg=D((Go,Sg)=>{A();x();I();(function(t,e){typeof Go=="object"&&typeof Sg<"u"?e(Go,vg()):typeof define=="function"&&define.amd?define(["exports","worker-timers-broker"],e):(t=typeof globalThis<"u"?globalThis:t||self,e(t.workerTimers={},t.workerTimersBroker))})(Go,function(t,e){"use strict";var r=function(h,d){var w=null;return function(){if(w!==null)return w;var y=new Blob([d],{type:"application/javascript; charset=utf-8"}),P=URL.createObjectURL(y);return w=h(P),setTimeout(function(){return URL.revokeObjectURL(P)}),w}},n=`(()=>{var e={45:(e,t,r)=>{var n=r(738).default;e.exports=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},79:e=>{e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},122:(e,t,r)=>{var n=r(79);e.exports=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},156:e=>{e.exports=function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,u,a,i=[],s=!0,c=!1;try{if(u=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=u.call(r)).done)&&(i.push(n.value),i.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return i}},e.exports.__esModule=!0,e.exports.default=e.exports},172:e=>{e.exports=function(e,t){this.v=e,this.k=t},e.exports.__esModule=!0,e.exports.default=e.exports},293:e=>{function t(e,t,r,n,o,u,a){try{var i=e[u](a),s=i.value}catch(e){return void r(e)}i.done?t(s):Promise.resolve(s).then(n,o)}e.exports=function(e){return function(){var r=this,n=arguments;return new Promise((function(o,u){var a=e.apply(r,n);function i(e){t(a,o,u,i,s,"next",e)}function s(e){t(a,o,u,i,s,"throw",e)}i(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports},373:e=>{e.exports=function(e){var t=Object(e),r=[];for(var n in t)r.unshift(n);return function e(){for(;r.length;)if((n=r.pop())in t)return e.value=n,e.done=!1,e;return e.done=!0,e}},e.exports.__esModule=!0,e.exports.default=e.exports},389:function(e,t){!function(e){"use strict";var t=function(e){return function(t){var r=e(t);return t.add(r),r}},r=function(e){return function(t,r){return e.set(t,r),r}},n=void 0===Number.MAX_SAFE_INTEGER?9007199254740991:Number.MAX_SAFE_INTEGER,o=536870912,u=2*o,a=function(e,t){return function(r){var a=t.get(r),i=void 0===a?r.size:a<u?a+1:0;if(!r.has(i))return e(r,i);if(r.size<o){for(;r.has(i);)i=Math.floor(Math.random()*u);return e(r,i)}if(r.size>n)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;r.has(i);)i=Math.floor(Math.random()*n);return e(r,i)}},i=new WeakMap,s=r(i),c=a(s,i),f=t(c);e.addUniqueNumber=f,e.generateUniqueNumber=c}(t)},472:function(e,t,r){!function(e,t,r,n){"use strict";var o=function(e,t){return function(r){var o=t.get(r);if(void 0===o)return Promise.resolve(!1);var u=n(o,2),a=u[0],i=u[1];return e(a),t.delete(r),i(!1),Promise.resolve(!0)}},u=function(e,t){var r=function(n,o,u,a){var i=n-e.now();i>0?o.set(a,[t(r,i,n,o,u,a),u]):(o.delete(a),u(!0))};return r},a=function(e,t,r,n){return function(o,u,a){var i=o+u-t.timeOrigin,s=i-t.now();return new Promise((function(t){e.set(a,[r(n,s,i,e,t,a),t])}))}},i=new Map,s=o(globalThis.clearTimeout,i),c=new Map,f=o(globalThis.clearTimeout,c),l=u(performance,globalThis.setTimeout),p=a(i,performance,globalThis.setTimeout,l),d=a(c,performance,globalThis.setTimeout,l);r.createWorker(self,{clear:function(){var r=e(t.mark((function e(r){var n,o,u;return t.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.timerId,o=r.timerType,e.next=1,"interval"===o?s(n):f(n);case 1:return u=e.sent,e.abrupt("return",{result:u});case 2:case"end":return e.stop()}}),e)})));function n(e){return r.apply(this,arguments)}return n}(),set:function(){var r=e(t.mark((function e(r){var n,o,u,a,i;return t.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.delay,o=r.now,u=r.timerId,a=r.timerType,e.next=1,("interval"===a?p:d)(n,o,u);case 1:return i=e.sent,e.abrupt("return",{result:i});case 2:case"end":return e.stop()}}),e)})));function n(e){return r.apply(this,arguments)}return n}()})}(r(293),r(756),r(623),r(715))},546:e=>{function t(r,n,o,u){var a=Object.defineProperty;try{a({},"",{})}catch(r){a=0}e.exports=t=function(e,r,n,o){if(r)a?a(e,r,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[r]=n;else{var u=function(r,n){t(e,r,(function(e){return this._invoke(r,n,e)}))};u("next",0),u("throw",1),u("return",2)}},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,n,o,u)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},579:(e,t,r)=>{var n=r(738).default;e.exports=function(e){if(null!=e){var t=e["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],r=0;if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}}}throw new TypeError(n(e)+" is not iterable")},e.exports.__esModule=!0,e.exports.default=e.exports},623:function(e,t,r){!function(e,t,r,n,o){"use strict";var u={INTERNAL_ERROR:-32603,INVALID_PARAMS:-32602,METHOD_NOT_FOUND:-32601},a=function(e,t){return Object.assign(new Error(e),{status:t})},i=function(e){return a('The requested method called "'.concat(e,'" is not supported.'),u.METHOD_NOT_FOUND)},s=function(e){return a('The handler of the method called "'.concat(e,'" returned no required result.'),u.INTERNAL_ERROR)},c=function(e){return a('The handler of the method called "'.concat(e,'" returned an unexpected result.'),u.INTERNAL_ERROR)},f=function(e){return a('The specified parameter called "portId" with the given value "'.concat(e,'" does not identify a port connected to this worker.'),u.INVALID_PARAMS)},l=function(e,n){return function(){var o=t(r.mark((function t(o){var u,a,f,l,p,d,v,x,y,b,h,m,_,g,w;return r.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(u=o.data,a=u.id,f=u.method,l=u.params,p=n[f],t.prev=1,void 0!==p){t.next=2;break}throw i(f);case 2:if(void 0!==(d=void 0===l?p():p(l))){t.next=3;break}throw s(f);case 3:if(!(d instanceof Promise)){t.next=5;break}return t.next=4,d;case 4:g=t.sent,t.next=6;break;case 5:g=d;case 6:if(v=g,null!==a){t.next=8;break}if(void 0===v.result){t.next=7;break}throw c(f);case 7:t.next=10;break;case 8:if(void 0!==v.result){t.next=9;break}throw c(f);case 9:x=v.result,y=v.transferables,b=void 0===y?[]:y,e.postMessage({id:a,result:x},b);case 10:t.next=12;break;case 11:t.prev=11,w=t.catch(1),h=w.message,m=w.status,_=void 0===m?-32603:m,e.postMessage({error:{code:_,message:h},id:a});case 12:case"end":return t.stop()}}),t,null,[[1,11]])})));return function(e){return o.apply(this,arguments)}}()},p=function(){return new Promise((function(e){var t=new ArrayBuffer(0),r=new MessageChannel,n=r.port1,o=r.port2;n.onmessage=function(t){var r=t.data;return e(null!==r)},o.postMessage(t,[t])}))};function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){n(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var x=new Map,y=function(e,n,u){return v(v({},n),{},{connect:function(t){var r=t.port;r.start();var u=e(r,n),a=o.generateUniqueNumber(x);return x.set(a,(function(){u(),r.close(),x.delete(a)})),{result:a}},disconnect:function(e){var t=e.portId,r=x.get(t);if(void 0===r)throw f(t);return r(),{result:null}},isSupported:function(){var e=t(r.mark((function e(){var t,n,o;return r.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=1,p();case 1:if(!e.sent){e.next=5;break}if(!((t=u())instanceof Promise)){e.next=3;break}return e.next=2,t;case 2:o=e.sent,e.next=4;break;case 3:o=t;case 4:return n=o,e.abrupt("return",{result:n});case 5:return e.abrupt("return",{result:!1});case 6:case"end":return e.stop()}}),e)})));function n(){return e.apply(this,arguments)}return n}()})},b=function(e,t){var r=y(b,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0}),n=l(e,r);return e.addEventListener("message",n),function(){return e.removeEventListener("message",n)}};e.createWorker=b,e.isSupported=p}(t,r(293),r(756),r(693),r(389))},633:(e,t,r)=>{var n=r(172),o=r(993),u=r(869),a=r(887),i=r(791),s=r(373),c=r(579);function f(){"use strict";var t=o(),r=t.m(f),l=(Object.getPrototypeOf?Object.getPrototypeOf(r):r.__proto__).constructor;function p(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===l||"GeneratorFunction"===(t.displayName||t.name))}var d={throw:1,return:2,break:3,continue:3};function v(e){var t,r;return function(n){t||(t={stop:function(){return r(n.a,2)},catch:function(){return n.v},abrupt:function(e,t){return r(n.a,d[e],t)},delegateYield:function(e,o,u){return t.resultName=o,r(n.d,c(e),u)},finish:function(e){return r(n.f,e)}},r=function(e,r,o){n.p=t.prev,n.n=t.next;try{return e(r,o)}finally{t.next=n.n}}),t.resultName&&(t[t.resultName]=n.v,t.resultName=void 0),t.sent=n.v,t.next=n.n;try{return e.call(this,t)}finally{n.p=t.prev,n.n=t.next}}}return(e.exports=f=function(){return{wrap:function(e,r,n,o){return t.w(v(e),r,n,o&&o.reverse())},isGeneratorFunction:p,mark:t.m,awrap:function(e,t){return new n(e,t)},AsyncIterator:i,async:function(e,t,r,n,o){return(p(t)?a:u)(v(e),t,r,n,o)},keys:s,values:c}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=f,e.exports.__esModule=!0,e.exports.default=e.exports},693:(e,t,r)=>{var n=r(736);e.exports=function(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},715:(e,t,r)=>{var n=r(987),o=r(156),u=r(122),a=r(752);e.exports=function(e,t){return n(e)||o(e,t)||u(e,t)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},736:(e,t,r)=>{var n=r(738).default,o=r(45);e.exports=function(e){var t=o(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},738:e=>{function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},752:e=>{e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},756:(e,t,r)=>{var n=r(633)();e.exports=n;try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},791:(e,t,r)=>{var n=r(172),o=r(546);e.exports=function e(t,r){function u(e,o,a,i){try{var s=t[e](o),c=s.value;return c instanceof n?r.resolve(c.v).then((function(e){u("next",e,a,i)}),(function(e){u("throw",e,a,i)})):r.resolve(c).then((function(e){s.value=e,a(s)}),(function(e){return u("throw",e,a,i)}))}catch(e){i(e)}}var a;this.next||(o(e.prototype),o(e.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",(function(){return this}))),o(this,"_invoke",(function(e,t,n){function o(){return new r((function(t,r){u(e,n,t,r)}))}return a=a?a.then(o,o):o()}),!0)},e.exports.__esModule=!0,e.exports.default=e.exports},869:(e,t,r)=>{var n=r(887);e.exports=function(e,t,r,o,u){var a=n(e,t,r,o,u);return a.next().then((function(e){return e.done?e.value:a.next()}))},e.exports.__esModule=!0,e.exports.default=e.exports},887:(e,t,r)=>{var n=r(993),o=r(791);e.exports=function(e,t,r,u,a){return new o(n().w(e,t,r,u),a||Promise)},e.exports.__esModule=!0,e.exports.default=e.exports},987:e=>{e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},993:(e,t,r)=>{var n=r(546);function o(){var t,r,u="function"==typeof Symbol?Symbol:{},a=u.iterator||"@@iterator",i=u.toStringTag||"@@toStringTag";function s(e,o,u,a){var i=o&&o.prototype instanceof f?o:f,s=Object.create(i.prototype);return n(s,"_invoke",function(e,n,o){var u,a,i,s=0,f=o||[],l=!1,p={p:0,n:0,v:t,a:d,f:d.bind(t,4),d:function(e,r){return u=e,a=0,i=t,p.n=r,c}};function d(e,n){for(a=e,i=n,r=0;!l&&s&&!o&&r<f.length;r++){var o,u=f[r],d=p.p,v=u[2];e>3?(o=v===n)&&(i=u[(a=u[4])?5:(a=3,3)],u[4]=u[5]=t):u[0]<=d&&((o=e<2&&d<u[1])?(a=0,p.v=n,p.n=u[1]):d<v&&(o=e<3||u[0]>n||n>v)&&(u[4]=e,u[5]=n,p.n=v,a=0))}if(o||e>1)return c;throw l=!0,n}return function(o,f,v){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&d(f,v),a=f,i=v;(r=a<2?t:i)||!l;){u||(a?a<3?(a>1&&(p.n=-1),d(a,i)):p.n=i:p.v=i);try{if(s=2,u){if(a||(o="next"),r=u[o]){if(!(r=r.call(u,i)))throw TypeError("iterator result is not an object");if(!r.done)return r;i=r.value,a<2&&(a=0)}else 1===a&&(r=u.return)&&r.call(u),a<2&&(i=TypeError("The iterator does not provide a '"+o+"' method"),a=1);u=t}else if((r=(l=p.n<0)?i:e.call(n,p))!==c)break}catch(e){u=t,a=1,i=e}finally{s=1}}return{value:r,done:l}}}(e,u,a),!0),s}var c={};function f(){}function l(){}function p(){}r=Object.getPrototypeOf;var d=[][a]?r(r([][a]())):(n(r={},a,(function(){return this})),r),v=p.prototype=f.prototype=Object.create(d);function x(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,n(e,i,"GeneratorFunction")),e.prototype=Object.create(v),e}return l.prototype=p,n(v,"constructor",p),n(p,"constructor",l),l.displayName="GeneratorFunction",n(p,i,"GeneratorFunction"),n(v),n(v,i,"Generator"),n(v,a,(function(){return this})),n(v,"toString",(function(){return"[object Generator]"})),(e.exports=o=function(){return{w:s,m:x}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var u=t[n]={exports:{}};return e[n].call(u.exports,u,u.exports,r),u.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";r(472)})()})();`,i=r(e.load,n),s=function(h){return i().clearInterval(h)},o=function(h){return i().clearTimeout(h)},a=function(){var h;return(h=i()).setInterval.apply(h,arguments)},c=function(){var h;return(h=i()).setTimeout.apply(h,arguments)};t.clearInterval=s,t.clearTimeout=o,t.setInterval=a,t.setTimeout=c})});var yi=D(Fr=>{"use strict";A();x();I();Object.defineProperty(Fr,"__esModule",{value:!0});Fr.isReactNativeBrowser=Fr.isWebWorker=void 0;var BA=()=>typeof window<"u"?typeof navigator<"u"&&navigator.userAgent?.toLowerCase().indexOf(" electron/")>-1&&M?.versions?!Object.prototype.hasOwnProperty.call(M.versions,"electron"):typeof window.document<"u":!1,Ag=()=>!!(typeof self=="object"&&self?.constructor?.name?.includes("WorkerGlobalScope")&&typeof Deno>"u"),Ig=()=>typeof navigator<"u"&&navigator.product==="ReactNative",MA=BA()||Ag()||Ig();Fr.isWebWorker=Ag();Fr.isReactNativeBrowser=Ig();Fr.default=MA});var Og=D(Gt=>{"use strict";A();x();I();var NA=Gt&&Gt.__createBinding||(Object.create?function(t,e,r,n){n===void 0&&(n=r);var i=Object.getOwnPropertyDescriptor(e,r);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,i)}:function(t,e,r,n){n===void 0&&(n=r),t[n]=e[r]}),qA=Gt&&Gt.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),LA=Gt&&Gt.__importStar||function(){var t=function(e){return t=Object.getOwnPropertyNames||function(r){var n=[];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[n.length]=i);return n},t(e)};return function(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var n=t(e),i=0;i<n.length;i++)n[i]!=="default"&&NA(r,e,n[i]);return qA(r,e),r}}();Object.defineProperty(Gt,"__esModule",{value:!0});var xg=Eg(),Lu=LA(yi()),Tg={set:xg.setInterval,clear:xg.clearInterval},Pg={set:(t,e)=>setInterval(t,e),clear:t=>clearInterval(t)},UA=t=>{switch(t){case"native":return Pg;case"worker":return Tg;case"auto":default:return Lu.default&&!Lu.isWebWorker&&!Lu.isReactNativeBrowser?Tg:Pg}};Gt.default=UA});var Du=D(gi=>{"use strict";A();x();I();var DA=gi&&gi.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(gi,"__esModule",{value:!0});var jA=DA(Og()),Uu=class{_keepalive;timerId;timer;destroyed=!1;counter;client;_keepaliveTimeoutTimestamp;_intervalEvery;get keepaliveTimeoutTimestamp(){return this._keepaliveTimeoutTimestamp}get intervalEvery(){return this._intervalEvery}get keepalive(){return this._keepalive}constructor(e,r){this.client=e,this.timer=typeof r=="object"&&"set"in r&&"clear"in r?r:(0,jA.default)(r),this.setKeepalive(e.options.keepalive)}clear(){this.timerId&&(this.timer.clear(this.timerId),this.timerId=null)}setKeepalive(e){if(e*=1e3,isNaN(e)||e<=0||e>**********)throw new Error(`Keepalive value must be an integer between 0 and **********. Provided value is ${e}`);this._keepalive=e,this.reschedule(),this.client.log(`KeepaliveManager: set keepalive to ${e}ms`)}destroy(){this.clear(),this.destroyed=!0}reschedule(){if(this.destroyed)return;this.clear(),this.counter=0;let e=Math.ceil(this._keepalive*1.5);this._keepaliveTimeoutTimestamp=Date.now()+e,this._intervalEvery=Math.ceil(this._keepalive/2),this.timerId=this.timer.set(()=>{this.destroyed||(this.counter+=1,this.counter===2?this.client.sendPing():this.counter>2&&this.client.onKeepaliveTimeout())},this._intervalEvery)}};gi.default=Uu});var zo=D(yt=>{"use strict";A();x();I();var FA=yt&&yt.__createBinding||(Object.create?function(t,e,r,n){n===void 0&&(n=r);var i=Object.getOwnPropertyDescriptor(e,r);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,i)}:function(t,e,r,n){n===void 0&&(n=r),t[n]=e[r]}),WA=yt&&yt.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Mg=yt&&yt.__importStar||function(){var t=function(e){return t=Object.getOwnPropertyNames||function(r){var n=[];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[n.length]=i);return n},t(e)};return function(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var n=t(e),i=0;i<n.length;i++)n[i]!=="default"&&FA(r,e,n[i]);return WA(r,e),r}}(),fr=yt&&yt.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(yt,"__esModule",{value:!0});var ju=fr(iy()),$A=or(),Rg=fr(uy()),HA=fr(At()),kg=Mg(cy()),vn=fr(La()),VA=fr(zy()),GA=fr(Au()),zA=fr(Ky()),bi=Mr(),KA=Qy(),QA=fr(Du()),Cg=Mg(yi()),Fu=globalThis.setImmediate||((...t)=>{let e=t.shift();(0,bi.nextTick)(()=>{e(...t)})}),Bg={keepalive:60,reschedulePings:!0,protocolId:"MQTT",protocolVersion:4,reconnectPeriod:1e3,connectTimeout:30*1e3,clean:!0,resubscribe:!0,subscribeBatchSize:null,writeCache:!0,timerVariant:"auto"},Wu=class t extends KA.TypedEventEmitter{static VERSION=bi.MQTTJS_VERSION;connected;disconnecting;disconnected;reconnecting;incomingStore;outgoingStore;options;queueQoSZero;_reconnectCount;log;messageIdProvider;outgoing;messageIdToTopic;noop;keepaliveManager;stream;queue;streamBuilder;_resubscribeTopics;connackTimer;reconnectTimer;_storeProcessing;_packetIdsDuringStoreProcessing;_storeProcessingQueue;_firstConnection;topicAliasRecv;topicAliasSend;_deferredReconnect;connackPacket;static defaultId(){return`mqttjs_${Math.random().toString(16).substr(2,8)}`}constructor(e,r){super(),this.options=r||{};for(let n in Bg)typeof this.options[n]>"u"?this.options[n]=Bg[n]:this.options[n]=r[n];this.log=this.options.log||(0,HA.default)("mqttjs:client"),this.noop=this._noop.bind(this),this.log("MqttClient :: version:",t.VERSION),Cg.isWebWorker?this.log("MqttClient :: environment","webworker"):this.log("MqttClient :: environment",Cg.default?"browser":"node"),this.log("MqttClient :: options.protocol",r.protocol),this.log("MqttClient :: options.protocolVersion",r.protocolVersion),this.log("MqttClient :: options.username",r.username),this.log("MqttClient :: options.keepalive",r.keepalive),this.log("MqttClient :: options.reconnectPeriod",r.reconnectPeriod),this.log("MqttClient :: options.rejectUnauthorized",r.rejectUnauthorized),this.log("MqttClient :: options.properties.topicAliasMaximum",r.properties?r.properties.topicAliasMaximum:void 0),this.options.clientId=typeof r.clientId=="string"?r.clientId:t.defaultId(),this.log("MqttClient :: clientId",this.options.clientId),this.options.customHandleAcks=r.protocolVersion===5&&r.customHandleAcks?r.customHandleAcks:(...n)=>{n[3](null,0)},this.options.writeCache||(ju.default.writeToStream.cacheNumbers=!1),this.streamBuilder=e,this.messageIdProvider=typeof this.options.messageIdProvider>"u"?new GA.default:this.options.messageIdProvider,this.outgoingStore=r.outgoingStore||new vn.default,this.incomingStore=r.incomingStore||new vn.default,this.queueQoSZero=r.queueQoSZero===void 0?!0:r.queueQoSZero,this._resubscribeTopics={},this.messageIdToTopic={},this.keepaliveManager=null,this.connected=!1,this.disconnecting=!1,this.reconnecting=!1,this.queue=[],this.connackTimer=null,this.reconnectTimer=null,this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={},this._storeProcessingQueue=[],this.outgoing={},this._firstConnection=!0,r.properties&&r.properties.topicAliasMaximum>0&&(r.properties.topicAliasMaximum>65535?this.log("MqttClient :: options.properties.topicAliasMaximum is out of range"):this.topicAliasRecv=new zA.default(r.properties.topicAliasMaximum)),this.on("connect",()=>{let{queue:n}=this,i=()=>{let s=n.shift();this.log("deliver :: entry %o",s);let o=null;if(!s){this._resubscribe();return}o=s.packet,this.log("deliver :: call _sendPacket for %o",o);let a=!0;o.messageId&&o.messageId!==0&&(this.messageIdProvider.register(o.messageId)||(a=!1)),a?this._sendPacket(o,c=>{s.cb&&s.cb(c),i()}):(this.log("messageId: %d has already used. The message is skipped and removed.",o.messageId),i())};this.log("connect :: sending queued packets"),i()}),this.on("close",()=>{this.log("close :: connected set to `false`"),this.connected=!1,this.log("close :: clearing connackTimer"),clearTimeout(this.connackTimer),this._destroyKeepaliveManager(),this.topicAliasRecv&&this.topicAliasRecv.clear(),this.log("close :: calling _setupReconnect"),this._setupReconnect()}),this.options.manualConnect||(this.log("MqttClient :: setting up stream"),this.connect())}handleAuth(e,r){r()}handleMessage(e,r){r()}_nextId(){return this.messageIdProvider.allocate()}getLastMessageId(){return this.messageIdProvider.getLastAllocated()}connect(){let e=new $A.Writable,r=ju.default.parser(this.options),n=null,i=[];this.log("connect :: calling method to clear reconnect"),this._clearReconnect(),this.disconnected&&!this.reconnecting&&(this.incomingStore=this.options.incomingStore||new vn.default,this.outgoingStore=this.options.outgoingStore||new vn.default,this.disconnecting=!1,this.disconnected=!1),this.log("connect :: using streamBuilder provided to client to create stream"),this.stream=this.streamBuilder(this),r.on("packet",f=>{this.log("parser :: on packet push to packets array."),i.push(f)});let s=()=>{this.log("work :: getting next packet in queue");let f=i.shift();if(f)this.log("work :: packet pulled from queue"),(0,VA.default)(this,f,o);else{this.log("work :: no packets in queue");let h=n;n=null,this.log("work :: done flag is %s",!!h),h&&h()}},o=()=>{if(i.length)(0,bi.nextTick)(s);else{let f=n;n=null,f()}};e._write=(f,h,d)=>{n=d,this.log("writable stream :: parsing buffer"),r.parse(f),s()};let a=f=>{this.log("streamErrorHandler :: error",f.message),f.code?(this.log("streamErrorHandler :: emitting error"),this.emit("error",f)):this.noop(f)};this.log("connect :: pipe stream to writable stream"),this.stream.pipe(e),this.stream.on("error",a),this.stream.on("close",()=>{this.log("(%s)stream :: on close",this.options.clientId),this._flushVolatile(),this.log("stream: emit close to MqttClient"),this.emit("close")}),this.log("connect: sending packet `connect`");let c={cmd:"connect",protocolId:this.options.protocolId,protocolVersion:this.options.protocolVersion,clean:this.options.clean,clientId:this.options.clientId,keepalive:this.options.keepalive,username:this.options.username,password:this.options.password,properties:this.options.properties};if(this.options.will&&(c.will={...this.options.will,payload:this.options.will?.payload}),this.topicAliasRecv&&(c.properties||(c.properties={}),this.topicAliasRecv&&(c.properties.topicAliasMaximum=this.topicAliasRecv.max)),this._writePacket(c),r.on("error",this.emit.bind(this,"error")),this.options.properties){if(!this.options.properties.authenticationMethod&&this.options.properties.authenticationData)return this.end(()=>this.emit("error",new Error("Packet has no Authentication Method"))),this;if(this.options.properties.authenticationMethod&&this.options.authPacket&&typeof this.options.authPacket=="object"){let f={cmd:"auth",reasonCode:0,...this.options.authPacket};this._writePacket(f)}}return this.stream.setMaxListeners(1e3),clearTimeout(this.connackTimer),this.connackTimer=setTimeout(()=>{this.log("!!connectTimeout hit!! Calling _cleanUp with force `true`"),this.emit("error",new Error("connack timeout")),this._cleanUp(!0)},this.options.connectTimeout),this}publish(e,r,n,i){this.log("publish :: message `%s` to topic `%s`",r,e);let{options:s}=this;typeof n=="function"&&(i=n,n=null),n=n||{},n={...{qos:0,retain:!1,dup:!1},...n};let{qos:a,retain:c,dup:f,properties:h,cbStorePut:d}=n;if(this._checkDisconnecting(i))return this;let w=()=>{let y=0;if((a===1||a===2)&&(y=this._nextId(),y===null))return this.log("No messageId left"),!1;let P={cmd:"publish",topic:e,payload:r,qos:a,retain:c,messageId:y,dup:f};switch(s.protocolVersion===5&&(P.properties=h),this.log("publish :: qos",a),a){case 1:case 2:this.outgoing[P.messageId]={volatile:!1,cb:i||this.noop},this.log("MqttClient:publish: packet cmd: %s",P.cmd),this._sendPacket(P,void 0,d);break;default:this.log("MqttClient:publish: packet cmd: %s",P.cmd),this._sendPacket(P,i,d);break}return!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!w())&&this._storeProcessingQueue.push({invoke:w,cbStorePut:n.cbStorePut,callback:i}),this}publishAsync(e,r,n){return new Promise((i,s)=>{this.publish(e,r,n,(o,a)=>{o?s(o):i(a)})})}subscribe(e,r,n){let i=this.options.protocolVersion;typeof r=="function"&&(n=r),n=n||this.noop;let s=!1,o=[];typeof e=="string"?(e=[e],o=e):Array.isArray(e)?o=e:typeof e=="object"&&(s=e.resubscribe,delete e.resubscribe,o=Object.keys(e));let a=kg.validateTopics(o);if(a!==null)return Fu(n,new Error(`Invalid topic ${a}`)),this;if(this._checkDisconnecting(n))return this.log("subscribe: discconecting true"),this;let c={qos:0};i===5&&(c.nl=!1,c.rap=!1,c.rh=0),r={...c,...r};let{properties:f}=r,h=[],d=(P,E)=>{if(E=E||r,!Object.prototype.hasOwnProperty.call(this._resubscribeTopics,P)||this._resubscribeTopics[P].qos<E.qos||s){let b={topic:P,qos:E.qos};i===5&&(b.nl=E.nl,b.rap=E.rap,b.rh=E.rh,b.properties=f),this.log("subscribe: pushing topic `%s` and qos `%s` to subs list",b.topic,b.qos),h.push(b)}};if(Array.isArray(e)?e.forEach(P=>{this.log("subscribe: array topic %s",P),d(P)}):Object.keys(e).forEach(P=>{this.log("subscribe: object topic %s, %o",P,e[P]),d(P,e[P])}),!h.length)return n(null,[]),this;let w=(P,E)=>{let b={cmd:"subscribe",subscriptions:P,messageId:E};if(f&&(b.properties=f),this.options.resubscribe){this.log("subscribe :: resubscribe true");let v=[];P.forEach(R=>{if(this.options.reconnectPeriod>0){let T={qos:R.qos};i===5&&(T.nl=R.nl||!1,T.rap=R.rap||!1,T.rh=R.rh||0,T.properties=R.properties),this._resubscribeTopics[R.topic]=T,v.push(R.topic)}}),this.messageIdToTopic[b.messageId]=v}let m=new Promise((v,R)=>{this.outgoing[b.messageId]={volatile:!0,cb(T,g){if(!T){let{granted:O}=g;for(let _=0;_<O.length;_+=1)P[_].qos=O[_]}T?R(new bi.ErrorWithSubackPacket(T.message,g)):v(g)}}});return this.log("subscribe :: call _sendPacket"),this._sendPacket(b),m},y=()=>{let P=this.options.subscribeBatchSize??h.length,E=[];for(let b=0;b<h.length;b+=P){let m=h.slice(b,b+P),v=this._nextId();if(v===null)return this.log("No messageId left"),!1;E.push(w(m,v))}return Promise.all(E).then(b=>{n(null,h,b.at(-1))}).catch(b=>{n(b,h,b.packet)}),!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!y())&&this._storeProcessingQueue.push({invoke:y,callback:n}),this}subscribeAsync(e,r){return new Promise((n,i)=>{this.subscribe(e,r,(s,o)=>{s?i(s):n(o)})})}unsubscribe(e,r,n){typeof e=="string"&&(e=[e]),typeof r=="function"&&(n=r),n=n||this.noop;let i=kg.validateTopics(e);if(i!==null)return Fu(n,new Error(`Invalid topic ${i}`)),this;if(this._checkDisconnecting(n))return this;let s=()=>{let o=this._nextId();if(o===null)return this.log("No messageId left"),!1;let a={cmd:"unsubscribe",messageId:o,unsubscriptions:[]};return typeof e=="string"?a.unsubscriptions=[e]:Array.isArray(e)&&(a.unsubscriptions=e),this.options.resubscribe&&a.unsubscriptions.forEach(c=>{delete this._resubscribeTopics[c]}),typeof r=="object"&&r.properties&&(a.properties=r.properties),this.outgoing[a.messageId]={volatile:!0,cb:n},this.log("unsubscribe: call _sendPacket"),this._sendPacket(a),!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!s())&&this._storeProcessingQueue.push({invoke:s,callback:n}),this}unsubscribeAsync(e,r){return new Promise((n,i)=>{this.unsubscribe(e,r,(s,o)=>{s?i(s):n(o)})})}end(e,r,n){this.log("end :: (%s)",this.options.clientId),(e==null||typeof e!="boolean")&&(n=n||r,r=e,e=!1),typeof r!="object"&&(n=n||r,r=null),this.log("end :: cb? %s",!!n),(!n||typeof n!="function")&&(n=this.noop);let i=()=>{this.log("end :: closeStores: closing incoming and outgoing stores"),this.disconnected=!0,this.incomingStore.close(o=>{this.outgoingStore.close(a=>{if(this.log("end :: closeStores: emitting end"),this.emit("end"),n){let c=o||a;this.log("end :: closeStores: invoking callback with args"),n(c)}})}),this._deferredReconnect?this._deferredReconnect():(this.options.reconnectPeriod===0||this.options.manualConnect)&&(this.disconnecting=!1)},s=()=>{this.log("end :: (%s) :: finish :: calling _cleanUp with force %s",this.options.clientId,e),this._cleanUp(e,()=>{this.log("end :: finish :: calling process.nextTick on closeStores"),(0,bi.nextTick)(i)},r)};return this.disconnecting?(n(),this):(this._clearReconnect(),this.disconnecting=!0,!e&&Object.keys(this.outgoing).length>0?(this.log("end :: (%s) :: calling finish in 10ms once outgoing is empty",this.options.clientId),this.once("outgoingEmpty",setTimeout.bind(null,s,10))):(this.log("end :: (%s) :: immediately calling finish",this.options.clientId),s()),this)}endAsync(e,r){return new Promise((n,i)=>{this.end(e,r,s=>{s?i(s):n()})})}removeOutgoingMessage(e){if(this.outgoing[e]){let{cb:r}=this.outgoing[e];this._removeOutgoingAndStoreMessage(e,()=>{r(new Error("Message removed"))})}return this}reconnect(e){this.log("client reconnect");let r=()=>{e?(this.options.incomingStore=e.incomingStore,this.options.outgoingStore=e.outgoingStore):(this.options.incomingStore=null,this.options.outgoingStore=null),this.incomingStore=this.options.incomingStore||new vn.default,this.outgoingStore=this.options.outgoingStore||new vn.default,this.disconnecting=!1,this.disconnected=!1,this._deferredReconnect=null,this._reconnect()};return this.disconnecting&&!this.disconnected?this._deferredReconnect=r:r(),this}_flushVolatile(){this.outgoing&&(this.log("_flushVolatile :: deleting volatile messages from the queue and setting their callbacks as error function"),Object.keys(this.outgoing).forEach(e=>{this.outgoing[e].volatile&&typeof this.outgoing[e].cb=="function"&&(this.outgoing[e].cb(new Error("Connection closed")),delete this.outgoing[e])}))}_flush(){this.outgoing&&(this.log("_flush: queue exists? %b",!!this.outgoing),Object.keys(this.outgoing).forEach(e=>{typeof this.outgoing[e].cb=="function"&&(this.outgoing[e].cb(new Error("Connection closed")),delete this.outgoing[e])}))}_removeTopicAliasAndRecoverTopicName(e){let r;e.properties&&(r=e.properties.topicAlias);let n=e.topic.toString();if(this.log("_removeTopicAliasAndRecoverTopicName :: alias %d, topic %o",r,n),n.length===0){if(typeof r>"u")return new Error("Unregistered Topic Alias");if(n=this.topicAliasSend.getTopicByAlias(r),typeof n>"u")return new Error("Unregistered Topic Alias");e.topic=n}r&&delete e.properties.topicAlias}_checkDisconnecting(e){return this.disconnecting&&(e&&e!==this.noop?e(new Error("client disconnecting")):this.emit("error",new Error("client disconnecting"))),this.disconnecting}_reconnect(){this.log("_reconnect: emitting reconnect to client"),this.emit("reconnect"),this.connected?(this.end(()=>{this.connect()}),this.log("client already connected. disconnecting first.")):(this.log("_reconnect: calling connect"),this.connect())}_setupReconnect(){!this.disconnecting&&!this.reconnectTimer&&this.options.reconnectPeriod>0?(this.reconnecting||(this.log("_setupReconnect :: emit `offline` state"),this.emit("offline"),this.log("_setupReconnect :: set `reconnecting` to `true`"),this.reconnecting=!0),this.log("_setupReconnect :: setting reconnectTimer for %d ms",this.options.reconnectPeriod),this.reconnectTimer=setInterval(()=>{this.log("reconnectTimer :: reconnect triggered!"),this._reconnect()},this.options.reconnectPeriod)):this.log("_setupReconnect :: doing nothing...")}_clearReconnect(){this.log("_clearReconnect : clearing reconnect timer"),this.reconnectTimer&&(clearInterval(this.reconnectTimer),this.reconnectTimer=null)}_cleanUp(e,r,n={}){if(r&&(this.log("_cleanUp :: done callback provided for on stream close"),this.stream.on("close",r)),this.log("_cleanUp :: forced? %s",e),e)this.options.reconnectPeriod===0&&this.options.clean&&this._flush(),this.log("_cleanUp :: (%s) :: destroying stream",this.options.clientId),this.stream.destroy();else{let i={cmd:"disconnect",...n};this.log("_cleanUp :: (%s) :: call _sendPacket with disconnect packet",this.options.clientId),this._sendPacket(i,()=>{this.log("_cleanUp :: (%s) :: destroying stream",this.options.clientId),Fu(()=>{this.stream.end(()=>{this.log("_cleanUp :: (%s) :: stream destroyed",this.options.clientId)})})})}!this.disconnecting&&!this.reconnecting&&(this.log("_cleanUp :: client not disconnecting/reconnecting. Clearing and resetting reconnect."),this._clearReconnect(),this._setupReconnect()),this._destroyKeepaliveManager(),r&&!this.connected&&(this.log("_cleanUp :: (%s) :: removing stream `done` callback `close` listener",this.options.clientId),this.stream.removeListener("close",r),r())}_storeAndSend(e,r,n){this.log("storeAndSend :: store packet with cmd %s to outgoingStore",e.cmd);let i=e,s;if(i.cmd==="publish"&&(i=(0,Rg.default)(e),s=this._removeTopicAliasAndRecoverTopicName(i),s))return r&&r(s);this.outgoingStore.put(i,o=>{if(o)return r&&r(o);n(),this._writePacket(e,r)})}_applyTopicAlias(e){if(this.options.protocolVersion===5&&e.cmd==="publish"){let r;e.properties&&(r=e.properties.topicAlias);let n=e.topic.toString();if(this.topicAliasSend)if(r){if(n.length!==0&&(this.log("applyTopicAlias :: register topic: %s - alias: %d",n,r),!this.topicAliasSend.put(n,r)))return this.log("applyTopicAlias :: error out of range. topic: %s - alias: %d",n,r),new Error("Sending Topic Alias out of range")}else n.length!==0&&(this.options.autoAssignTopicAlias?(r=this.topicAliasSend.getAliasByTopic(n),r?(e.topic="",e.properties={...e.properties,topicAlias:r},this.log("applyTopicAlias :: auto assign(use) topic: %s - alias: %d",n,r)):(r=this.topicAliasSend.getLruAlias(),this.topicAliasSend.put(n,r),e.properties={...e.properties,topicAlias:r},this.log("applyTopicAlias :: auto assign topic: %s - alias: %d",n,r))):this.options.autoUseTopicAlias&&(r=this.topicAliasSend.getAliasByTopic(n),r&&(e.topic="",e.properties={...e.properties,topicAlias:r},this.log("applyTopicAlias :: auto use topic: %s - alias: %d",n,r))));else if(r)return this.log("applyTopicAlias :: error out of range. topic: %s - alias: %d",n,r),new Error("Sending Topic Alias out of range")}}_noop(e){this.log("noop ::",e)}_writePacket(e,r){this.log("_writePacket :: packet: %O",e),this.log("_writePacket :: emitting `packetsend`"),this.emit("packetsend",e),this.log("_writePacket :: writing to stream");let n=ju.default.writeToStream(e,this.stream,this.options);this.log("_writePacket :: writeToStream result %s",n),!n&&r&&r!==this.noop?(this.log("_writePacket :: handle events on `drain` once through callback."),this.stream.once("drain",r)):r&&(this.log("_writePacket :: invoking cb"),r())}_sendPacket(e,r,n,i){this.log("_sendPacket :: (%s) ::  start",this.options.clientId),n=n||this.noop,r=r||this.noop;let s=this._applyTopicAlias(e);if(s){r(s);return}if(!this.connected){if(e.cmd==="auth"){this._writePacket(e,r);return}this.log("_sendPacket :: client not connected. Storing packet offline."),this._storePacket(e,r,n);return}if(i){this._writePacket(e,r);return}switch(e.cmd){case"publish":break;case"pubrel":this._storeAndSend(e,r,n);return;default:this._writePacket(e,r);return}switch(e.qos){case 2:case 1:this._storeAndSend(e,r,n);break;case 0:default:this._writePacket(e,r);break}this.log("_sendPacket :: (%s) ::  end",this.options.clientId)}_storePacket(e,r,n){this.log("_storePacket :: packet: %o",e),this.log("_storePacket :: cb? %s",!!r),n=n||this.noop;let i=e;if(i.cmd==="publish"){i=(0,Rg.default)(e);let o=this._removeTopicAliasAndRecoverTopicName(i);if(o)return r&&r(o)}let s=i.qos||0;s===0&&this.queueQoSZero||i.cmd!=="publish"?this.queue.push({packet:i,cb:r}):s>0?(r=this.outgoing[i.messageId]?this.outgoing[i.messageId].cb:null,this.outgoingStore.put(i,o=>{if(o)return r&&r(o);n()})):r&&r(new Error("No connection to broker"))}_setupKeepaliveManager(){this.log("_setupKeepaliveManager :: keepalive %d (seconds)",this.options.keepalive),!this.keepaliveManager&&this.options.keepalive&&(this.keepaliveManager=new QA.default(this,this.options.timerVariant))}_destroyKeepaliveManager(){this.keepaliveManager&&(this.log("_destroyKeepaliveManager :: destroying keepalive manager"),this.keepaliveManager.destroy(),this.keepaliveManager=null)}reschedulePing(e=!1){this.keepaliveManager&&this.options.keepalive&&(e||this.options.reschedulePings)&&this._reschedulePing()}_reschedulePing(){this.log("_reschedulePing :: rescheduling ping"),this.keepaliveManager.reschedule()}sendPing(){this.log("_sendPing :: sending pingreq"),this._sendPacket({cmd:"pingreq"})}onKeepaliveTimeout(){this.emit("error",new Error("Keepalive timeout")),this.log("onKeepaliveTimeout :: calling _cleanUp with force true"),this._cleanUp(!0)}_resubscribe(){this.log("_resubscribe");let e=Object.keys(this._resubscribeTopics);if(!this._firstConnection&&(this.options.clean||this.options.protocolVersion>=4&&!this.connackPacket.sessionPresent)&&e.length>0)if(this.options.resubscribe)if(this.options.protocolVersion===5){this.log("_resubscribe: protocolVersion 5");for(let r=0;r<e.length;r++){let n={};n[e[r]]=this._resubscribeTopics[e[r]],n.resubscribe=!0,this.subscribe(n,{properties:n[e[r]].properties})}}else this._resubscribeTopics.resubscribe=!0,this.subscribe(this._resubscribeTopics);else this._resubscribeTopics={};this._firstConnection=!1}_onConnect(e){if(this.disconnected){this.emit("connect",e);return}this.connackPacket=e,this.messageIdProvider.clear(),this._setupKeepaliveManager(),this.connected=!0;let r=()=>{let n=this.outgoingStore.createStream(),i=()=>{n.destroy(),n=null,this._flushStoreProcessingQueue(),s()},s=()=>{this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={}};this.once("close",i),n.on("error",a=>{s(),this._flushStoreProcessingQueue(),this.removeListener("close",i),this.emit("error",a)});let o=()=>{if(!n)return;let a=n.read(1),c;if(!a){n.once("readable",o);return}if(this._storeProcessing=!0,this._packetIdsDuringStoreProcessing[a.messageId]){o();return}!this.disconnecting&&!this.reconnectTimer?(c=this.outgoing[a.messageId]?this.outgoing[a.messageId].cb:null,this.outgoing[a.messageId]={volatile:!1,cb(f,h){c&&c(f,h),o()}},this._packetIdsDuringStoreProcessing[a.messageId]=!0,this.messageIdProvider.register(a.messageId)?this._sendPacket(a,void 0,void 0,!0):this.log("messageId: %d has already used.",a.messageId)):n.destroy&&n.destroy()};n.on("end",()=>{let a=!0;for(let c in this._packetIdsDuringStoreProcessing)if(!this._packetIdsDuringStoreProcessing[c]){a=!1;break}this.removeListener("close",i),a?(s(),this._invokeAllStoreProcessingQueue(),this.emit("connect",e)):r()}),o()};r()}_invokeStoreProcessingQueue(){if(!this._storeProcessing&&this._storeProcessingQueue.length>0){let e=this._storeProcessingQueue[0];if(e&&e.invoke())return this._storeProcessingQueue.shift(),!0}return!1}_invokeAllStoreProcessingQueue(){for(;this._invokeStoreProcessingQueue(););}_flushStoreProcessingQueue(){for(let e of this._storeProcessingQueue)e.cbStorePut&&e.cbStorePut(new Error("Connection closed")),e.callback&&e.callback(new Error("Connection closed"));this._storeProcessingQueue.splice(0)}_removeOutgoingAndStoreMessage(e,r){delete this.outgoing[e],this.outgoingStore.del({messageId:e},(n,i)=>{r(n,i),this.messageIdProvider.deallocate(e),this._invokeStoreProcessingQueue()})}};yt.default=Wu});var Ng=D(Hu=>{"use strict";A();x();I();Object.defineProperty(Hu,"__esModule",{value:!0});var YA=wu(),$u=class{numberAllocator;lastId;constructor(){this.numberAllocator=new YA.NumberAllocator(1,65535)}allocate(){return this.lastId=this.numberAllocator.alloc(),this.lastId}getLastAllocated(){return this.lastId}register(e){return this.numberAllocator.use(e)}deallocate(e){this.numberAllocator.free(e)}clear(){this.numberAllocator.clear()}};Hu.default=$u});function JA(){if(qg)return Vu;qg=!0;let t=**********,e=36,r=1,n=26,i=38,s=700,o=72,a=128,c="-",f=/^xn--/,h=/[^\0-\x7F]/,d=/[\x2E\u3002\uFF0E\uFF61]/g,w={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},y=e-r,P=Math.floor,E=String.fromCharCode;function b(H){throw new RangeError(w[H])}function m(H,G){let K=[],X=H.length;for(;X--;)K[X]=G(H[X]);return K}function v(H,G){let K=H.split("@"),X="";K.length>1&&(X=K[0]+"@",H=K[1]),H=H.replace(d,".");let Q=H.split("."),V=m(Q,G).join(".");return X+V}function R(H){let G=[],K=0,X=H.length;for(;K<X;){let Q=H.charCodeAt(K++);if(Q>=55296&&Q<=56319&&K<X){let V=H.charCodeAt(K++);(V&64512)==56320?G.push(((Q&1023)<<10)+(V&1023)+65536):(G.push(Q),K--)}else G.push(Q)}return G}let T=H=>String.fromCodePoint(...H),g=function(H){return H>=48&&H<58?26+(H-48):H>=65&&H<91?H-65:H>=97&&H<123?H-97:e},O=function(H,G){return H+22+75*(H<26)-((G!=0)<<5)},_=function(H,G,K){let X=0;for(H=K?P(H/s):H>>1,H+=P(H/G);H>y*n>>1;X+=e)H=P(H/y);return P(X+(y+1)*H/(H+i))},q=function(H){let G=[],K=H.length,X=0,Q=a,V=o,te=H.lastIndexOf(c);te<0&&(te=0);for(let Y=0;Y<te;++Y)H.charCodeAt(Y)>=128&&b("not-basic"),G.push(H.charCodeAt(Y));for(let Y=te>0?te+1:0;Y<K;){let ee=X;for(let Z=1,ne=e;;ne+=e){Y>=K&&b("invalid-input");let fe=g(H.charCodeAt(Y++));fe>=e&&b("invalid-input"),fe>P((t-X)/Z)&&b("overflow"),X+=fe*Z;let ge=ne<=V?r:ne>=V+n?n:ne-V;if(fe<ge)break;let ye=e-ge;Z>P(t/ye)&&b("overflow"),Z*=ye}let pe=G.length+1;V=_(X-ee,pe,ee==0),P(X/pe)>t-Q&&b("overflow"),Q+=P(X/pe),X%=pe,G.splice(X++,0,Q)}return String.fromCodePoint(...G)},$=function(H){let G=[];H=R(H);let K=H.length,X=a,Q=0,V=o;for(let ee of H)ee<128&&G.push(E(ee));let te=G.length,Y=te;for(te&&G.push(c);Y<K;){let ee=t;for(let Z of H)Z>=X&&Z<ee&&(ee=Z);let pe=Y+1;ee-X>P((t-Q)/pe)&&b("overflow"),Q+=(ee-X)*pe,X=ee;for(let Z of H)if(Z<X&&++Q>t&&b("overflow"),Z===X){let ne=Q;for(let fe=e;;fe+=e){let ge=fe<=V?r:fe>=V+n?n:fe-V;if(ne<ge)break;let ye=ne-ge,Ae=e-ge;G.push(E(O(ge+ye%Ae,0))),ne=P(ye/Ae)}G.push(E(O(ne,0))),V=_(Q,pe,Y===te),Q=0,++Y}++Q,++X}return G.join("")};return Vu={version:"2.3.1",ucs2:{decode:R,encode:T},decode:q,encode:$,toASCII:function(H){return v(H,function(G){return h.test(G)?"xn--"+$(G):G})},toUnicode:function(H){return v(H,function(G){return f.test(G)?q(G.slice(4).toLowerCase()):G})}},Vu}var Vu,qg,hr,w5,m5,_5,v5,S5,E5,Lg=ze(()=>{A();x();I();Vu={},qg=!1;hr=JA(),w5=hr.decode,m5=hr.encode,_5=hr.toASCII,v5=hr.toUnicode,S5=hr.ucs2,E5=hr.version});function XA(){return Ug||(Ug=!0,Gu=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var e={},r=Symbol("test"),n=Object(r);if(typeof r=="string"||Object.prototype.toString.call(r)!=="[object Symbol]"||Object.prototype.toString.call(n)!=="[object Symbol]")return!1;var i=42;e[r]=i;for(r in e)return!1;if(typeof Object.keys=="function"&&Object.keys(e).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(e).length!==0)return!1;var s=Object.getOwnPropertySymbols(e);if(s.length!==1||s[0]!==r||!Object.prototype.propertyIsEnumerable.call(e,r))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var o=Object.getOwnPropertyDescriptor(e,r);if(o.value!==i||o.enumerable!==!0)return!1}return!0}),Gu}function ZA(){return Dg||(Dg=!0,zu=Error),zu}function eI(){return jg||(jg=!0,Ku=EvalError),Ku}function tI(){return Fg||(Fg=!0,Qu=RangeError),Qu}function rI(){return Wg||(Wg=!0,Yu=ReferenceError),Yu}function ob(){return $g||($g=!0,Ju=SyntaxError),Ju}function Sn(){return Hg||(Hg=!0,Xu=TypeError),Xu}function nI(){return Vg||(Vg=!0,Zu=URIError),Zu}function iI(){if(Gg)return el;Gg=!0;var t=typeof Symbol<"u"&&Symbol,e=XA();return el=function(){return typeof t!="function"||typeof Symbol!="function"||typeof t("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:e()},el}function oI(){if(zg)return tl;zg=!0;var t={__proto__:null,foo:{}},e=Object;return tl=function(){return{__proto__:t}.foo===t.foo&&!(t instanceof e)},tl}function sI(){if(Kg)return rl;Kg=!0;var t="Function.prototype.bind called on incompatible ",e=Object.prototype.toString,r=Math.max,n="[object Function]",i=function(c,f){for(var h=[],d=0;d<c.length;d+=1)h[d]=c[d];for(var w=0;w<f.length;w+=1)h[w+c.length]=f[w];return h},s=function(c,f){for(var h=[],d=f,w=0;d<c.length;d+=1,w+=1)h[w]=c[d];return h},o=function(a,c){for(var f="",h=0;h<a.length;h+=1)f+=a[h],h+1<a.length&&(f+=c);return f};return rl=function(c){var f=this;if(typeof f!="function"||e.apply(f)!==n)throw new TypeError(t+f);for(var h=s(arguments,1),d,w=function(){if(this instanceof d){var m=f.apply(this,i(h,arguments));return Object(m)===m?m:this}return f.apply(c,i(h,arguments))},y=r(0,f.length-h.length),P=[],E=0;E<y;E++)P[E]="$"+E;if(d=Function("binder","return function ("+o(P,",")+"){ return binder.apply(this,arguments); }")(w),f.prototype){var b=function(){};b.prototype=f.prototype,d.prototype=new b,b.prototype=null}return d},rl}function hl(){if(Qg)return nl;Qg=!0;var t=sI();return nl=Function.prototype.bind||t,nl}function aI(){if(Yg)return il;Yg=!0;var t=Function.prototype.call,e=Object.prototype.hasOwnProperty,r=hl();return il=r.call(t,e),il}function Wr(){if(Jg)return ol;Jg=!0;var t,e=ZA(),r=eI(),n=tI(),i=rI(),s=ob(),o=Sn(),a=nI(),c=Function,f=function(Q){try{return c('"use strict"; return ('+Q+").constructor;")()}catch{}},h=Object.getOwnPropertyDescriptor;if(h)try{h({},"")}catch{h=null}var d=function(){throw new o},w=h?function(){try{return arguments.callee,d}catch{try{return h(arguments,"callee").get}catch{return d}}}():d,y=iI()(),P=oI()(),E=Object.getPrototypeOf||(P?function(Q){return Q.__proto__}:null),b={},m=typeof Uint8Array>"u"||!E?t:E(Uint8Array),v={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?t:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?t:ArrayBuffer,"%ArrayIteratorPrototype%":y&&E?E([][Symbol.iterator]()):t,"%AsyncFromSyncIteratorPrototype%":t,"%AsyncFunction%":b,"%AsyncGenerator%":b,"%AsyncGeneratorFunction%":b,"%AsyncIteratorPrototype%":b,"%Atomics%":typeof Atomics>"u"?t:Atomics,"%BigInt%":typeof BigInt>"u"?t:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?t:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?t:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?t:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":e,"%eval%":eval,"%EvalError%":r,"%Float32Array%":typeof Float32Array>"u"?t:Float32Array,"%Float64Array%":typeof Float64Array>"u"?t:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?t:FinalizationRegistry,"%Function%":c,"%GeneratorFunction%":b,"%Int8Array%":typeof Int8Array>"u"?t:Int8Array,"%Int16Array%":typeof Int16Array>"u"?t:Int16Array,"%Int32Array%":typeof Int32Array>"u"?t:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":y&&E?E(E([][Symbol.iterator]())):t,"%JSON%":typeof JSON=="object"?JSON:t,"%Map%":typeof Map>"u"?t:Map,"%MapIteratorPrototype%":typeof Map>"u"||!y||!E?t:E(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?t:Promise,"%Proxy%":typeof Proxy>"u"?t:Proxy,"%RangeError%":n,"%ReferenceError%":i,"%Reflect%":typeof Reflect>"u"?t:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?t:Set,"%SetIteratorPrototype%":typeof Set>"u"||!y||!E?t:E(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?t:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":y&&E?E(""[Symbol.iterator]()):t,"%Symbol%":y?Symbol:t,"%SyntaxError%":s,"%ThrowTypeError%":w,"%TypedArray%":m,"%TypeError%":o,"%Uint8Array%":typeof Uint8Array>"u"?t:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?t:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?t:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?t:Uint32Array,"%URIError%":a,"%WeakMap%":typeof WeakMap>"u"?t:WeakMap,"%WeakRef%":typeof WeakRef>"u"?t:WeakRef,"%WeakSet%":typeof WeakSet>"u"?t:WeakSet};if(E)try{null.error}catch(Q){var R=E(E(Q));v["%Error.prototype%"]=R}var T=function Q(V){var te;if(V==="%AsyncFunction%")te=f("async function () {}");else if(V==="%GeneratorFunction%")te=f("function* () {}");else if(V==="%AsyncGeneratorFunction%")te=f("async function* () {}");else if(V==="%AsyncGenerator%"){var Y=Q("%AsyncGeneratorFunction%");Y&&(te=Y.prototype)}else if(V==="%AsyncIteratorPrototype%"){var ee=Q("%AsyncGenerator%");ee&&E&&(te=E(ee.prototype))}return v[V]=te,te},g={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},O=hl(),_=aI(),q=O.call(Function.call,Array.prototype.concat),$=O.call(Function.apply,Array.prototype.splice),j=O.call(Function.call,String.prototype.replace),J=O.call(Function.call,String.prototype.slice),W=O.call(Function.call,RegExp.prototype.exec),H=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,G=/\\(\\)?/g,K=function(V){var te=J(V,0,1),Y=J(V,-1);if(te==="%"&&Y!=="%")throw new s("invalid intrinsic syntax, expected closing `%`");if(Y==="%"&&te!=="%")throw new s("invalid intrinsic syntax, expected opening `%`");var ee=[];return j(V,H,function(pe,Z,ne,fe){ee[ee.length]=ne?j(fe,G,"$1"):Z||pe}),ee},X=function(V,te){var Y=V,ee;if(_(g,Y)&&(ee=g[Y],Y="%"+ee[0]+"%"),_(v,Y)){var pe=v[Y];if(pe===b&&(pe=T(Y)),typeof pe>"u"&&!te)throw new o("intrinsic "+V+" exists, but is not available. Please file an issue!");return{alias:ee,name:Y,value:pe}}throw new s("intrinsic "+V+" does not exist!")};return ol=function(V,te){if(typeof V!="string"||V.length===0)throw new o("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof te!="boolean")throw new o('"allowMissing" argument must be a boolean');if(W(/^%?[^%]*%?$/,V)===null)throw new s("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var Y=K(V),ee=Y.length>0?Y[0]:"",pe=X("%"+ee+"%",te),Z=pe.name,ne=pe.value,fe=!1,ge=pe.alias;ge&&(ee=ge[0],$(Y,q([0,1],ge)));for(var ye=1,Ae=!0;ye<Y.length;ye+=1){var _e=Y[ye],ve=J(_e,0,1),Oe=J(_e,-1);if((ve==='"'||ve==="'"||ve==="`"||Oe==='"'||Oe==="'"||Oe==="`")&&ve!==Oe)throw new s("property names with quotes must have matching quotes");if((_e==="constructor"||!Ae)&&(fe=!0),ee+="."+_e,Z="%"+ee+"%",_(v,Z))ne=v[Z];else if(ne!=null){if(!(_e in ne)){if(!te)throw new o("base intrinsic for "+V+" exists, but the property is not available.");return}if(h&&ye+1>=Y.length){var xe=h(ne,_e);Ae=!!xe,Ae&&"get"in xe&&!("originalValue"in xe.get)?ne=xe.get:ne=ne[_e]}else Ae=_(ne,_e),ne=ne[_e];Ae&&!fe&&(v[Z]=ne)}}return ne},ol}function dl(){if(Xg)return sl;Xg=!0;var t=Wr(),e=t("%Object.defineProperty%",!0)||!1;if(e)try{e({},"a",{value:1})}catch{e=!1}return sl=e,sl}function sb(){if(Zg)return al;Zg=!0;var t=Wr(),e=t("%Object.getOwnPropertyDescriptor%",!0);if(e)try{e([],"length")}catch{e=null}return al=e,al}function uI(){if(eb)return ul;eb=!0;var t=dl(),e=ob(),r=Sn(),n=sb();return ul=function(s,o,a){if(!s||typeof s!="object"&&typeof s!="function")throw new r("`obj` must be an object or a function`");if(typeof o!="string"&&typeof o!="symbol")throw new r("`property` must be a string or a symbol`");if(arguments.length>3&&typeof arguments[3]!="boolean"&&arguments[3]!==null)throw new r("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&typeof arguments[4]!="boolean"&&arguments[4]!==null)throw new r("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&typeof arguments[5]!="boolean"&&arguments[5]!==null)throw new r("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&typeof arguments[6]!="boolean")throw new r("`loose`, if provided, must be a boolean");var c=arguments.length>3?arguments[3]:null,f=arguments.length>4?arguments[4]:null,h=arguments.length>5?arguments[5]:null,d=arguments.length>6?arguments[6]:!1,w=!!n&&n(s,o);if(t)t(s,o,{configurable:h===null&&w?w.configurable:!h,enumerable:c===null&&w?w.enumerable:!c,value:a,writable:f===null&&w?w.writable:!f});else if(d||!c&&!f&&!h)s[o]=a;else throw new e("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.")},ul}function lI(){if(tb)return ll;tb=!0;var t=dl(),e=function(){return!!t};return e.hasArrayLengthDefineBug=function(){if(!t)return null;try{return t([],"length",{value:1}).length!==1}catch{return!0}},ll=e,ll}function cI(){if(rb)return cl;rb=!0;var t=Wr(),e=uI(),r=lI()(),n=sb(),i=Sn(),s=t("%Math.floor%");return cl=function(a,c){if(typeof a!="function")throw new i("`fn` is not a function");if(typeof c!="number"||c<0||c>4294967295||s(c)!==c)throw new i("`length` must be a positive 32-bit integer");var f=arguments.length>2&&!!arguments[2],h=!0,d=!0;if("length"in a&&n){var w=n(a,"length");w&&!w.configurable&&(h=!1),w&&!w.writable&&(d=!1)}return(h||d||!f)&&(r?e(a,"length",c,!0,!0):e(a,"length",c)),a},cl}function fI(){if(nb)return wi;nb=!0;var t=hl(),e=Wr(),r=cI(),n=Sn(),i=e("%Function.prototype.apply%"),s=e("%Function.prototype.call%"),o=e("%Reflect.apply%",!0)||t.call(s,i),a=dl(),c=e("%Math.max%");wi=function(d){if(typeof d!="function")throw new n("a function is required");var w=o(t,s,arguments);return r(w,1+c(0,d.length-(arguments.length-1)),!0)};var f=function(){return o(t,i,arguments)};return a?a(wi,"apply",{value:f}):wi.apply=f,wi}function ab(){if(ib)return fl;ib=!0;var t=Wr(),e=fI(),r=e(t("String.prototype.indexOf"));return fl=function(i,s){var o=t(i,!!s);return typeof o=="function"&&r(i,".prototype.")>-1?e(o):o},fl}var Gu,Ug,zu,Dg,Ku,jg,Qu,Fg,Yu,Wg,Ju,$g,Xu,Hg,Zu,Vg,el,Gg,tl,zg,rl,Kg,nl,Qg,il,Yg,ol,Jg,sl,Xg,al,Zg,ul,eb,ll,tb,cl,rb,wi,nb,fl,ib,ub=ze(()=>{A();x();I();Gu={},Ug=!1;zu={},Dg=!1;Ku={},jg=!1;Qu={},Fg=!1;Yu={},Wg=!1;Ju={},$g=!1;Xu={},Hg=!1;Zu={},Vg=!1;el={},Gg=!1;tl={},zg=!1;rl={},Kg=!1;nl={},Qg=!1;il={},Yg=!1;ol={},Jg=!1;sl={},Xg=!1;al={},Zg=!1;ul={},eb=!1;ll={},tb=!1;cl={},rb=!1;wi={},nb=!1;fl={},ib=!1});function bl(t){throw new Error("Node.js process "+t+" is not supported by JSPM core outside of Node.js")}function hI(){!En||!$r||(En=!1,$r.length?zt=$r.concat(zt):Ko=-1,zt.length&&lb())}function lb(){if(!En){var t=setTimeout(hI,0);En=!0;for(var e=zt.length;e;){for($r=zt,zt=[];++Ko<e;)$r&&$r[Ko].run();Ko=-1,e=zt.length}$r=null,En=!1,clearTimeout(t)}}function dI(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];zt.push(new cb(t,e)),zt.length===1&&!En&&setTimeout(lb,0)}function cb(t,e){this.fun=t,this.array=e}function We(){}function RI(t){bl("_linkedBinding")}function MI(t){bl("dlopen")}function NI(){return[]}function qI(){return[]}function VI(t,e){if(!t)throw new Error(e||"assertion error")}function QI(){return!1}function hx(){return dr.now()/1e3}function gl(t){var e=Math.floor((Date.now()-dr.now())*.001),r=dr.now()*.001,n=Math.floor(r)+e,i=Math.floor(r%1*1e9);return t&&(n=n-t[0],i=i-t[1],i<0&&(n--,i+=yl)),[n,i]}function pr(){return ml}function Ix(t){return[]}var zt,En,$r,Ko,pI,yI,gI,bI,wI,mI,_I,vI,SI,EI,AI,II,xI,TI,PI,OI,kI,CI,BI,LI,UI,wl,DI,jI,FI,WI,$I,HI,GI,zI,KI,YI,JI,XI,ZI,ex,tx,rx,nx,ix,ox,sx,ax,ux,lx,cx,fx,dr,pl,yl,dx,px,yx,gx,bx,mx,_x,vx,Sx,Ex,Ax,ml,_l=ze(()=>{A();x();I();zt=[],En=!1,Ko=-1;cb.prototype.run=function(){this.fun.apply(null,this.array)};pI="browser",yI="x64",gI="browser",bI={PATH:"/usr/bin",LANG:navigator.language+".UTF-8",PWD:"/",HOME:"/home",TMP:"/tmp"},wI=["/usr/bin/node"],mI=[],_I="v16.8.0",vI={},SI=function(t,e){console.warn((e?e+": ":"")+t)},EI=function(t){bl("binding")},AI=function(t){return 0},II=function(){return"/"},xI=function(t){},TI={name:"node",sourceUrl:"",headersUrl:"",libUrl:""};PI=We,OI=[];kI={},CI=!1,BI={};LI=We,UI=We,wl=function(){return{}},DI=wl,jI=wl,FI=We,WI=We,$I=We,HI={};GI={inspector:!1,debug:!1,uv:!1,ipv6:!1,tls_alpn:!1,tls_sni:!1,tls_ocsp:!1,tls:!1,cached_builtins:!0},zI=We,KI=We;YI=We,JI=We,XI=We,ZI=We,ex=We,tx=void 0,rx=void 0,nx=void 0,ix=We,ox=2,sx=1,ax="/bin/usr/node",ux=9229,lx="node",cx=[],fx=We,dr={now:typeof performance<"u"?performance.now.bind(performance):void 0,timing:typeof performance<"u"?performance.timing:void 0};dr.now===void 0&&(pl=Date.now(),dr.timing&&dr.timing.navigationStart&&(pl=dr.timing.navigationStart),dr.now=()=>Date.now()-pl);yl=1e9;gl.bigint=function(t){var e=gl(t);return typeof BigInt>"u"?e[0]*yl+e[1]:BigInt(e[0]*yl)+BigInt(e[1])};dx=10,px={},yx=0;gx=pr,bx=pr,mx=pr,_x=pr,vx=pr,Sx=We,Ex=pr,Ax=pr;ml={version:_I,versions:vI,arch:yI,platform:gI,release:TI,_rawDebug:PI,moduleLoadList:OI,binding:EI,_linkedBinding:RI,_events:px,_eventsCount:yx,_maxListeners:dx,on:pr,addListener:gx,once:bx,off:mx,removeListener:_x,removeAllListeners:vx,emit:Sx,prependListener:Ex,prependOnceListener:Ax,listeners:Ix,domain:kI,_exiting:CI,config:BI,dlopen:MI,uptime:hx,_getActiveRequests:NI,_getActiveHandles:qI,reallyExit:LI,_kill:UI,cpuUsage:wl,resourceUsage:DI,memoryUsage:jI,kill:FI,exit:WI,openStdin:$I,allowedNodeEnvironmentFlags:HI,assert:VI,features:GI,_fatalExceptions:zI,setUncaughtExceptionCaptureCallback:KI,hasUncaughtExceptionCaptureCallback:QI,emitWarning:SI,nextTick:dI,_tickCallback:YI,_debugProcess:JI,_debugEnd:XI,_startProfilerIdleNotifier:ZI,_stopProfilerIdleNotifier:ex,stdout:tx,stdin:nx,stderr:rx,abort:ix,umask:AI,chdir:xI,cwd:II,env:bI,title:pI,argv:wI,execArgv:mI,pid:ox,ppid:sx,execPath:ax,debugPort:ux,hrtime:gl,argv0:lx,_preload_modules:cx,setSourceMapsEnabled:fx}});function xx(){if(fb)return vl;fb=!0;var t=ml;function e(s){if(typeof s!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(s))}function r(s,o){for(var a="",c=0,f=-1,h=0,d,w=0;w<=s.length;++w){if(w<s.length)d=s.charCodeAt(w);else{if(d===47)break;d=47}if(d===47){if(!(f===w-1||h===1))if(f!==w-1&&h===2){if(a.length<2||c!==2||a.charCodeAt(a.length-1)!==46||a.charCodeAt(a.length-2)!==46){if(a.length>2){var y=a.lastIndexOf("/");if(y!==a.length-1){y===-1?(a="",c=0):(a=a.slice(0,y),c=a.length-1-a.lastIndexOf("/")),f=w,h=0;continue}}else if(a.length===2||a.length===1){a="",c=0,f=w,h=0;continue}}o&&(a.length>0?a+="/..":a="..",c=2)}else a.length>0?a+="/"+s.slice(f+1,w):a=s.slice(f+1,w),c=w-f-1;f=w,h=0}else d===46&&h!==-1?++h:h=-1}return a}function n(s,o){var a=o.dir||o.root,c=o.base||(o.name||"")+(o.ext||"");return a?a===o.root?a+c:a+s+c:c}var i={resolve:function(){for(var o="",a=!1,c,f=arguments.length-1;f>=-1&&!a;f--){var h;f>=0?h=arguments[f]:(c===void 0&&(c=t.cwd()),h=c),e(h),h.length!==0&&(o=h+"/"+o,a=h.charCodeAt(0)===47)}return o=r(o,!a),a?o.length>0?"/"+o:"/":o.length>0?o:"."},normalize:function(o){if(e(o),o.length===0)return".";var a=o.charCodeAt(0)===47,c=o.charCodeAt(o.length-1)===47;return o=r(o,!a),o.length===0&&!a&&(o="."),o.length>0&&c&&(o+="/"),a?"/"+o:o},isAbsolute:function(o){return e(o),o.length>0&&o.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var o,a=0;a<arguments.length;++a){var c=arguments[a];e(c),c.length>0&&(o===void 0?o=c:o+="/"+c)}return o===void 0?".":i.normalize(o)},relative:function(o,a){if(e(o),e(a),o===a||(o=i.resolve(o),a=i.resolve(a),o===a))return"";for(var c=1;c<o.length&&o.charCodeAt(c)===47;++c);for(var f=o.length,h=f-c,d=1;d<a.length&&a.charCodeAt(d)===47;++d);for(var w=a.length,y=w-d,P=h<y?h:y,E=-1,b=0;b<=P;++b){if(b===P){if(y>P){if(a.charCodeAt(d+b)===47)return a.slice(d+b+1);if(b===0)return a.slice(d+b)}else h>P&&(o.charCodeAt(c+b)===47?E=b:b===0&&(E=0));break}var m=o.charCodeAt(c+b),v=a.charCodeAt(d+b);if(m!==v)break;m===47&&(E=b)}var R="";for(b=c+E+1;b<=f;++b)(b===f||o.charCodeAt(b)===47)&&(R.length===0?R+="..":R+="/..");return R.length>0?R+a.slice(d+E):(d+=E,a.charCodeAt(d)===47&&++d,a.slice(d))},_makeLong:function(o){return o},dirname:function(o){if(e(o),o.length===0)return".";for(var a=o.charCodeAt(0),c=a===47,f=-1,h=!0,d=o.length-1;d>=1;--d)if(a=o.charCodeAt(d),a===47){if(!h){f=d;break}}else h=!1;return f===-1?c?"/":".":c&&f===1?"//":o.slice(0,f)},basename:function(o,a){if(a!==void 0&&typeof a!="string")throw new TypeError('"ext" argument must be a string');e(o);var c=0,f=-1,h=!0,d;if(a!==void 0&&a.length>0&&a.length<=o.length){if(a.length===o.length&&a===o)return"";var w=a.length-1,y=-1;for(d=o.length-1;d>=0;--d){var P=o.charCodeAt(d);if(P===47){if(!h){c=d+1;break}}else y===-1&&(h=!1,y=d+1),w>=0&&(P===a.charCodeAt(w)?--w===-1&&(f=d):(w=-1,f=y))}return c===f?f=y:f===-1&&(f=o.length),o.slice(c,f)}else{for(d=o.length-1;d>=0;--d)if(o.charCodeAt(d)===47){if(!h){c=d+1;break}}else f===-1&&(h=!1,f=d+1);return f===-1?"":o.slice(c,f)}},extname:function(o){e(o);for(var a=-1,c=0,f=-1,h=!0,d=0,w=o.length-1;w>=0;--w){var y=o.charCodeAt(w);if(y===47){if(!h){c=w+1;break}continue}f===-1&&(h=!1,f=w+1),y===46?a===-1?a=w:d!==1&&(d=1):a!==-1&&(d=-1)}return a===-1||f===-1||d===0||d===1&&a===f-1&&a===c+1?"":o.slice(a,f)},format:function(o){if(o===null||typeof o!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof o);return n("/",o)},parse:function(o){e(o);var a={root:"",dir:"",base:"",ext:"",name:""};if(o.length===0)return a;var c=o.charCodeAt(0),f=c===47,h;f?(a.root="/",h=1):h=0;for(var d=-1,w=0,y=-1,P=!0,E=o.length-1,b=0;E>=h;--E){if(c=o.charCodeAt(E),c===47){if(!P){w=E+1;break}continue}y===-1&&(P=!1,y=E+1),c===46?d===-1?d=E:b!==1&&(b=1):d!==-1&&(b=-1)}return d===-1||y===-1||b===0||b===1&&d===y-1&&d===w+1?y!==-1&&(w===0&&f?a.base=a.name=o.slice(1,y):a.base=a.name=o.slice(w,y)):(w===0&&f?(a.name=o.slice(1,d),a.base=o.slice(1,y)):(a.name=o.slice(w,d),a.base=o.slice(w,y)),a.ext=o.slice(d,y)),w>0?a.dir=o.slice(0,w-1):f&&(a.dir="/"),a},sep:"/",delimiter:":",win32:null,posix:null};return i.posix=i,vl=i,vl}var vl,fb,Sl,hb=ze(()=>{A();x();I();_l();vl={},fb=!1;Sl=xx()});var Ab={};Kr(Ab,{URL:()=>jx,Url:()=>Nx,default:()=>Ge,fileURLToPath:()=>Sb,format:()=>qx,parse:()=>Dx,pathToFileURL:()=>Eb,resolve:()=>Lx,resolveObject:()=>Ux});function Px(){if(db)return El;db=!0;var t=typeof Map=="function"&&Map.prototype,e=Object.getOwnPropertyDescriptor&&t?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,r=t&&e&&typeof e.get=="function"?e.get:null,n=t&&Map.prototype.forEach,i=typeof Set=="function"&&Set.prototype,s=Object.getOwnPropertyDescriptor&&i?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,o=i&&s&&typeof s.get=="function"?s.get:null,a=i&&Set.prototype.forEach,c=typeof WeakMap=="function"&&WeakMap.prototype,f=c?WeakMap.prototype.has:null,h=typeof WeakSet=="function"&&WeakSet.prototype,d=h?WeakSet.prototype.has:null,w=typeof WeakRef=="function"&&WeakRef.prototype,y=w?WeakRef.prototype.deref:null,P=Boolean.prototype.valueOf,E=Object.prototype.toString,b=Function.prototype.toString,m=String.prototype.match,v=String.prototype.slice,R=String.prototype.replace,T=String.prototype.toUpperCase,g=String.prototype.toLowerCase,O=RegExp.prototype.test,_=Array.prototype.concat,q=Array.prototype.join,$=Array.prototype.slice,j=Math.floor,J=typeof BigInt=="function"?BigInt.prototype.valueOf:null,W=Object.getOwnPropertySymbols,H=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol.prototype.toString:null,G=typeof Symbol=="function"&&typeof Symbol.iterator=="object",K=typeof Symbol=="function"&&Symbol.toStringTag&&(typeof Symbol.toStringTag===G||!0)?Symbol.toStringTag:null,X=Object.prototype.propertyIsEnumerable,Q=(typeof Reflect=="function"?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(k){return k.__proto__}:null);function V(k,B){if(k===1/0||k===-1/0||k!==k||k&&k>-1e3&&k<1e3||O.call(/e/,B))return B;var re=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if(typeof k=="number"){var ie=k<0?-j(-k):j(k);if(ie!==k){var oe=String(ie),ce=v.call(B,oe.length+1);return R.call(oe,re,"$&_")+"."+R.call(R.call(ce,/([0-9]{3})/g,"$&_"),/_$/,"")}}return R.call(B,re,"$&_")}var te=Tx,Y=te.custom,ee=Oe(Y)?Y:null;El=function k(B,re,ie,oe){var ce=re||{};if(Re(ce,"quoteStyle")&&ce.quoteStyle!=="single"&&ce.quoteStyle!=="double")throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Re(ce,"maxStringLength")&&(typeof ce.maxStringLength=="number"?ce.maxStringLength<0&&ce.maxStringLength!==1/0:ce.maxStringLength!==null))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var Qt=Re(ce,"customInspect")?ce.customInspect:!0;if(typeof Qt!="boolean"&&Qt!=="symbol")throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Re(ce,"indent")&&ce.indent!==null&&ce.indent!=="	"&&!(parseInt(ce.indent,10)===ce.indent&&ce.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Re(ce,"numericSeparator")&&typeof ce.numericSeparator!="boolean")throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var br=ce.numericSeparator;if(typeof B>"u")return"undefined";if(B===null)return"null";if(typeof B=="boolean")return B?"true":"false";if(typeof B=="string")return at(B,ce);if(typeof B=="number"){if(B===0)return 1/0/B>0?"0":"-0";var Ye=String(B);return br?V(B,Ye):Ye}if(typeof B=="bigint"){var Yt=String(B)+"n";return br?V(B,Yt):Yt}var es=typeof ce.depth>"u"?5:ce.depth;if(typeof ie>"u"&&(ie=0),ie>=es&&es>0&&typeof B=="object")return ne(B)?"[Array]":"[Object]";var Gr=C(ce,ie);if(typeof oe>"u")oe=[];else if(Bt(oe,B)>=0)return"[Circular]";function ut(zr,Ti,Qb){if(Ti&&(oe=$.call(oe),oe.push(Ti)),Qb){var nc={depth:ce.depth};return Re(ce,"quoteStyle")&&(nc.quoteStyle=ce.quoteStyle),k(zr,nc,ie+1,oe)}return k(zr,ce,ie+1,oe)}if(typeof B=="function"&&!ge(B)){var Yl=Ct(B),Jl=F(B,ut);return"[Function"+(Yl?": "+Yl:" (anonymous)")+"]"+(Jl.length>0?" { "+q.call(Jl,", ")+" }":"")}if(Oe(B)){var Xl=G?R.call(String(B),/^(Symbol\(.*\))_[^)]*$/,"$1"):H.call(B);return typeof B=="object"&&!G?p(Xl):Xl}if(Xo(B)){for(var Pn="<"+g.call(String(B.nodeName)),ts=B.attributes||[],xi=0;xi<ts.length;xi++)Pn+=" "+ts[xi].name+"="+pe(Z(ts[xi].value),"double",ce);return Pn+=">",B.childNodes&&B.childNodes.length&&(Pn+="..."),Pn+="</"+g.call(String(B.nodeName))+">",Pn}if(ne(B)){if(B.length===0)return"[]";var rs=F(B,ut);return Gr&&!S(rs)?"["+N(rs,Gr)+"]":"[ "+q.call(rs,", ")+" ]"}if(ye(B)){var ns=F(B,ut);return!("cause"in Error.prototype)&&"cause"in B&&!X.call(B,"cause")?"{ ["+String(B)+"] "+q.call(_.call("[cause]: "+ut(B.cause),ns),", ")+" }":ns.length===0?"["+String(B)+"]":"{ ["+String(B)+"] "+q.call(ns,", ")+" }"}if(typeof B=="object"&&Qt){if(ee&&typeof B[ee]=="function"&&te)return te(B,{depth:es-ie});if(Qt!=="symbol"&&typeof B.inspect=="function")return B.inspect()}if(xn(B)){var Zl=[];return n&&n.call(B,function(zr,Ti){Zl.push(ut(Ti,B,!0)+" => "+ut(zr,B))}),l("Map",r.call(B),Zl,Gr)}if(Ze(B)){var ec=[];return a&&a.call(B,function(zr){ec.push(ut(zr,B))}),l("Set",o.call(B),ec,Gr)}if(Ii(B))return u("WeakMap");if(Tn(B))return u("WeakSet");if(Vr(B))return u("WeakRef");if(_e(B))return p(ut(Number(B)));if(xe(B))return p(ut(J.call(B)));if(ve(B))return p(P.call(B));if(Ae(B))return p(ut(String(B)));if(typeof window<"u"&&B===window)return"{ [object Window] }";if(typeof globalThis<"u"&&B===globalThis||typeof Al<"u"&&B===Al)return"{ [object globalThis] }";if(!fe(B)&&!ge(B)){var is=F(B,ut),tc=Q?Q(B)===Object.prototype:B instanceof Object||B.constructor===Object,os=B instanceof Object?"":"null prototype",rc=!tc&&K&&Object(B)===B&&K in B?v.call(Ne(B),8,-1):os?"Object":"",Kb=tc||typeof B.constructor!="function"?"":B.constructor.name?B.constructor.name+" ":"",ss=Kb+(rc||os?"["+q.call(_.call([],rc||[],os||[]),": ")+"] ":"");return is.length===0?ss+"{}":Gr?ss+"{"+N(is,Gr)+"}":ss+"{ "+q.call(is,", ")+" }"}return String(B)};function pe(k,B,re){var ie=(re.quoteStyle||B)==="double"?'"':"'";return ie+k+ie}function Z(k){return R.call(String(k),/"/g,"&quot;")}function ne(k){return Ne(k)==="[object Array]"&&(!K||!(typeof k=="object"&&K in k))}function fe(k){return Ne(k)==="[object Date]"&&(!K||!(typeof k=="object"&&K in k))}function ge(k){return Ne(k)==="[object RegExp]"&&(!K||!(typeof k=="object"&&K in k))}function ye(k){return Ne(k)==="[object Error]"&&(!K||!(typeof k=="object"&&K in k))}function Ae(k){return Ne(k)==="[object String]"&&(!K||!(typeof k=="object"&&K in k))}function _e(k){return Ne(k)==="[object Number]"&&(!K||!(typeof k=="object"&&K in k))}function ve(k){return Ne(k)==="[object Boolean]"&&(!K||!(typeof k=="object"&&K in k))}function Oe(k){if(G)return k&&typeof k=="object"&&k instanceof Symbol;if(typeof k=="symbol")return!0;if(!k||typeof k!="object"||!H)return!1;try{return H.call(k),!0}catch{}return!1}function xe(k){if(!k||typeof k!="object"||!J)return!1;try{return J.call(k),!0}catch{}return!1}var st=Object.prototype.hasOwnProperty||function(k){return k in(this||Al)};function Re(k,B){return st.call(k,B)}function Ne(k){return E.call(k)}function Ct(k){if(k.name)return k.name;var B=m.call(b.call(k),/^function\s*([\w$]+)/);return B?B[1]:null}function Bt(k,B){if(k.indexOf)return k.indexOf(B);for(var re=0,ie=k.length;re<ie;re++)if(k[re]===B)return re;return-1}function xn(k){if(!r||!k||typeof k!="object")return!1;try{r.call(k);try{o.call(k)}catch{return!0}return k instanceof Map}catch{}return!1}function Ii(k){if(!f||!k||typeof k!="object")return!1;try{f.call(k,f);try{d.call(k,d)}catch{return!0}return k instanceof WeakMap}catch{}return!1}function Vr(k){if(!y||!k||typeof k!="object")return!1;try{return y.call(k),!0}catch{}return!1}function Ze(k){if(!o||!k||typeof k!="object")return!1;try{o.call(k);try{r.call(k)}catch{return!0}return k instanceof Set}catch{}return!1}function Tn(k){if(!d||!k||typeof k!="object")return!1;try{d.call(k,d);try{f.call(k,f)}catch{return!0}return k instanceof WeakSet}catch{}return!1}function Xo(k){return!k||typeof k!="object"?!1:typeof HTMLElement<"u"&&k instanceof HTMLElement?!0:typeof k.nodeName=="string"&&typeof k.getAttribute=="function"}function at(k,B){if(k.length>B.maxStringLength){var re=k.length-B.maxStringLength,ie="... "+re+" more character"+(re>1?"s":"");return at(v.call(k,0,B.maxStringLength),B)+ie}var oe=R.call(R.call(k,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,Zo);return pe(oe,"single",B)}function Zo(k){var B=k.charCodeAt(0),re={8:"b",9:"t",10:"n",12:"f",13:"r"}[B];return re?"\\"+re:"\\x"+(B<16?"0":"")+T.call(B.toString(16))}function p(k){return"Object("+k+")"}function u(k){return k+" { ? }"}function l(k,B,re,ie){var oe=ie?N(re,ie):q.call(re,", ");return k+" ("+B+") {"+oe+"}"}function S(k){for(var B=0;B<k.length;B++)if(Bt(k[B],`
`)>=0)return!1;return!0}function C(k,B){var re;if(k.indent==="	")re="	";else if(typeof k.indent=="number"&&k.indent>0)re=q.call(Array(k.indent+1)," ");else return null;return{base:re,prev:q.call(Array(B+1),re)}}function N(k,B){if(k.length===0)return"";var re=`
`+B.prev+B.base;return re+q.call(k,","+re)+`
`+B.prev}function F(k,B){var re=ne(k),ie=[];if(re){ie.length=k.length;for(var oe=0;oe<k.length;oe++)ie[oe]=Re(k,oe)?B(k[oe],k):""}var ce=typeof W=="function"?W(k):[],Qt;if(G){Qt={};for(var br=0;br<ce.length;br++)Qt["$"+ce[br]]=ce[br]}for(var Ye in k)Re(k,Ye)&&(re&&String(Number(Ye))===Ye&&Ye<k.length||G&&Qt["$"+Ye]instanceof Symbol||(O.call(/[^\w$]/,Ye)?ie.push(B(Ye,k)+": "+B(k[Ye],k)):ie.push(Ye+": "+B(k[Ye],k))));if(typeof W=="function")for(var Yt=0;Yt<ce.length;Yt++)X.call(k,ce[Yt])&&ie.push("["+B(ce[Yt])+"]: "+B(k[ce[Yt]],k));return ie}return El}function Ox(){if(pb)return Il;pb=!0;var t=Wr(),e=ab(),r=Px(),n=Sn(),i=t("%WeakMap%",!0),s=t("%Map%",!0),o=e("WeakMap.prototype.get",!0),a=e("WeakMap.prototype.set",!0),c=e("WeakMap.prototype.has",!0),f=e("Map.prototype.get",!0),h=e("Map.prototype.set",!0),d=e("Map.prototype.has",!0),w=function(b,m){for(var v=b,R;(R=v.next)!==null;v=R)if(R.key===m)return v.next=R.next,R.next=b.next,b.next=R,R},y=function(b,m){var v=w(b,m);return v&&v.value},P=function(b,m,v){var R=w(b,m);R?R.value=v:b.next={key:m,next:b.next,value:v}},E=function(b,m){return!!w(b,m)};return Il=function(){var m,v,R,T={assert:function(g){if(!T.has(g))throw new n("Side channel does not contain "+r(g))},get:function(g){if(i&&g&&(typeof g=="object"||typeof g=="function")){if(m)return o(m,g)}else if(s){if(v)return f(v,g)}else if(R)return y(R,g)},has:function(g){if(i&&g&&(typeof g=="object"||typeof g=="function")){if(m)return c(m,g)}else if(s){if(v)return d(v,g)}else if(R)return E(R,g);return!1},set:function(g,O){i&&g&&(typeof g=="object"||typeof g=="function")?(m||(m=new i),a(m,g,O)):s?(v||(v=new s),h(v,g,O)):(R||(R={key:{},next:null}),P(R,g,O))}};return T},Il}function Cl(){if(yb)return xl;yb=!0;var t=String.prototype.replace,e=/%20/g,r={RFC1738:"RFC1738",RFC3986:"RFC3986"};return xl={default:r.RFC3986,formatters:{RFC1738:function(n){return t.call(n,e,"+")},RFC3986:function(n){return String(n)}},RFC1738:r.RFC1738,RFC3986:r.RFC3986},xl}function vb(){if(gb)return Tl;gb=!0;var t=Cl(),e=Object.prototype.hasOwnProperty,r=Array.isArray,n=function(){for(var b=[],m=0;m<256;++m)b.push("%"+((m<16?"0":"")+m.toString(16)).toUpperCase());return b}(),i=function(m){for(;m.length>1;){var v=m.pop(),R=v.obj[v.prop];if(r(R)){for(var T=[],g=0;g<R.length;++g)typeof R[g]<"u"&&T.push(R[g]);v.obj[v.prop]=T}}},s=function(m,v){for(var R=v&&v.plainObjects?Object.create(null):{},T=0;T<m.length;++T)typeof m[T]<"u"&&(R[T]=m[T]);return R},o=function b(m,v,R){if(!v)return m;if(typeof v!="object"){if(r(m))m.push(v);else if(m&&typeof m=="object")(R&&(R.plainObjects||R.allowPrototypes)||!e.call(Object.prototype,v))&&(m[v]=!0);else return[m,v];return m}if(!m||typeof m!="object")return[m].concat(v);var T=m;return r(m)&&!r(v)&&(T=s(m,R)),r(m)&&r(v)?(v.forEach(function(g,O){if(e.call(m,O)){var _=m[O];_&&typeof _=="object"&&g&&typeof g=="object"?m[O]=b(_,g,R):m.push(g)}else m[O]=g}),m):Object.keys(v).reduce(function(g,O){var _=v[O];return e.call(g,O)?g[O]=b(g[O],_,R):g[O]=_,g},T)},a=function(m,v){return Object.keys(v).reduce(function(R,T){return R[T]=v[T],R},m)},c=function(b,m,v){var R=b.replace(/\+/g," ");if(v==="iso-8859-1")return R.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(R)}catch{return R}},f=1024,h=function(m,v,R,T,g){if(m.length===0)return m;var O=m;if(typeof m=="symbol"?O=Symbol.prototype.toString.call(m):typeof m!="string"&&(O=String(m)),R==="iso-8859-1")return escape(O).replace(/%u[0-9a-f]{4}/gi,function(H){return"%26%23"+parseInt(H.slice(2),16)+"%3B"});for(var _="",q=0;q<O.length;q+=f){for(var $=O.length>=f?O.slice(q,q+f):O,j=[],J=0;J<$.length;++J){var W=$.charCodeAt(J);if(W===45||W===46||W===95||W===126||W>=48&&W<=57||W>=65&&W<=90||W>=97&&W<=122||g===t.RFC1738&&(W===40||W===41)){j[j.length]=$.charAt(J);continue}if(W<128){j[j.length]=n[W];continue}if(W<2048){j[j.length]=n[192|W>>6]+n[128|W&63];continue}if(W<55296||W>=57344){j[j.length]=n[224|W>>12]+n[128|W>>6&63]+n[128|W&63];continue}J+=1,W=65536+((W&1023)<<10|$.charCodeAt(J)&1023),j[j.length]=n[240|W>>18]+n[128|W>>12&63]+n[128|W>>6&63]+n[128|W&63]}_+=j.join("")}return _},d=function(m){for(var v=[{obj:{o:m},prop:"o"}],R=[],T=0;T<v.length;++T)for(var g=v[T],O=g.obj[g.prop],_=Object.keys(O),q=0;q<_.length;++q){var $=_[q],j=O[$];typeof j=="object"&&j!==null&&R.indexOf(j)===-1&&(v.push({obj:O,prop:$}),R.push(j))}return i(v),m},w=function(m){return Object.prototype.toString.call(m)==="[object RegExp]"},y=function(m){return!m||typeof m!="object"?!1:!!(m.constructor&&m.constructor.isBuffer&&m.constructor.isBuffer(m))},P=function(m,v){return[].concat(m,v)},E=function(m,v){if(r(m)){for(var R=[],T=0;T<m.length;T+=1)R.push(v(m[T]));return R}return v(m)};return Tl={arrayToObject:s,assign:a,combine:P,compact:d,decode:c,encode:h,isBuffer:y,isRegExp:w,maybeMap:E,merge:o},Tl}function Rx(){if(bb)return Pl;bb=!0;var t=Ox(),e=vb(),r=Cl(),n=Object.prototype.hasOwnProperty,i={brackets:function(b){return b+"[]"},comma:"comma",indices:function(b,m){return b+"["+m+"]"},repeat:function(b){return b}},s=Array.isArray,o=Array.prototype.push,a=function(E,b){o.apply(E,s(b)?b:[b])},c=Date.prototype.toISOString,f=r.default,h={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:e.encode,encodeValuesOnly:!1,format:f,formatter:r.formatters[f],indices:!1,serializeDate:function(b){return c.call(b)},skipNulls:!1,strictNullHandling:!1},d=function(b){return typeof b=="string"||typeof b=="number"||typeof b=="boolean"||typeof b=="symbol"||typeof b=="bigint"},w={},y=function E(b,m,v,R,T,g,O,_,q,$,j,J,W,H,G,K,X,Q){for(var V=b,te=Q,Y=0,ee=!1;(te=te.get(w))!==void 0&&!ee;){var pe=te.get(b);if(Y+=1,typeof pe<"u"){if(pe===Y)throw new RangeError("Cyclic object value");ee=!0}typeof te.get(w)>"u"&&(Y=0)}if(typeof $=="function"?V=$(m,V):V instanceof Date?V=W(V):v==="comma"&&s(V)&&(V=e.maybeMap(V,function(Ne){return Ne instanceof Date?W(Ne):Ne})),V===null){if(g)return q&&!K?q(m,h.encoder,X,"key",H):m;V=""}if(d(V)||e.isBuffer(V)){if(q){var Z=K?m:q(m,h.encoder,X,"key",H);return[G(Z)+"="+G(q(V,h.encoder,X,"value",H))]}return[G(m)+"="+G(String(V))]}var ne=[];if(typeof V>"u")return ne;var fe;if(v==="comma"&&s(V))K&&q&&(V=e.maybeMap(V,q)),fe=[{value:V.length>0?V.join(",")||null:void 0}];else if(s($))fe=$;else{var ge=Object.keys(V);fe=j?ge.sort(j):ge}var ye=_?m.replace(/\./g,"%2E"):m,Ae=R&&s(V)&&V.length===1?ye+"[]":ye;if(T&&s(V)&&V.length===0)return Ae+"[]";for(var _e=0;_e<fe.length;++_e){var ve=fe[_e],Oe=typeof ve=="object"&&typeof ve.value<"u"?ve.value:V[ve];if(!(O&&Oe===null)){var xe=J&&_?ve.replace(/\./g,"%2E"):ve,st=s(V)?typeof v=="function"?v(Ae,xe):Ae:Ae+(J?"."+xe:"["+xe+"]");Q.set(b,Y);var Re=t();Re.set(w,Q),a(ne,E(Oe,st,v,R,T,g,O,_,v==="comma"&&K&&s(V)?null:q,$,j,J,W,H,G,K,X,Re))}}return ne},P=function(b){if(!b)return h;if(typeof b.allowEmptyArrays<"u"&&typeof b.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof b.encodeDotInKeys<"u"&&typeof b.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(b.encoder!==null&&typeof b.encoder<"u"&&typeof b.encoder!="function")throw new TypeError("Encoder has to be a function.");var m=b.charset||h.charset;if(typeof b.charset<"u"&&b.charset!=="utf-8"&&b.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var v=r.default;if(typeof b.format<"u"){if(!n.call(r.formatters,b.format))throw new TypeError("Unknown format option provided.");v=b.format}var R=r.formatters[v],T=h.filter;(typeof b.filter=="function"||s(b.filter))&&(T=b.filter);var g;if(b.arrayFormat in i?g=b.arrayFormat:"indices"in b?g=b.indices?"indices":"repeat":g=h.arrayFormat,"commaRoundTrip"in b&&typeof b.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var O=typeof b.allowDots>"u"?b.encodeDotInKeys===!0?!0:h.allowDots:!!b.allowDots;return{addQueryPrefix:typeof b.addQueryPrefix=="boolean"?b.addQueryPrefix:h.addQueryPrefix,allowDots:O,allowEmptyArrays:typeof b.allowEmptyArrays=="boolean"?!!b.allowEmptyArrays:h.allowEmptyArrays,arrayFormat:g,charset:m,charsetSentinel:typeof b.charsetSentinel=="boolean"?b.charsetSentinel:h.charsetSentinel,commaRoundTrip:b.commaRoundTrip,delimiter:typeof b.delimiter>"u"?h.delimiter:b.delimiter,encode:typeof b.encode=="boolean"?b.encode:h.encode,encodeDotInKeys:typeof b.encodeDotInKeys=="boolean"?b.encodeDotInKeys:h.encodeDotInKeys,encoder:typeof b.encoder=="function"?b.encoder:h.encoder,encodeValuesOnly:typeof b.encodeValuesOnly=="boolean"?b.encodeValuesOnly:h.encodeValuesOnly,filter:T,format:v,formatter:R,serializeDate:typeof b.serializeDate=="function"?b.serializeDate:h.serializeDate,skipNulls:typeof b.skipNulls=="boolean"?b.skipNulls:h.skipNulls,sort:typeof b.sort=="function"?b.sort:null,strictNullHandling:typeof b.strictNullHandling=="boolean"?b.strictNullHandling:h.strictNullHandling}};return Pl=function(E,b){var m=E,v=P(b),R,T;typeof v.filter=="function"?(T=v.filter,m=T("",m)):s(v.filter)&&(T=v.filter,R=T);var g=[];if(typeof m!="object"||m===null)return"";var O=i[v.arrayFormat],_=O==="comma"&&v.commaRoundTrip;R||(R=Object.keys(m)),v.sort&&R.sort(v.sort);for(var q=t(),$=0;$<R.length;++$){var j=R[$];v.skipNulls&&m[j]===null||a(g,y(m[j],j,O,_,v.allowEmptyArrays,v.strictNullHandling,v.skipNulls,v.encodeDotInKeys,v.encode?v.encoder:null,v.filter,v.sort,v.allowDots,v.serializeDate,v.format,v.formatter,v.encodeValuesOnly,v.charset,q))}var J=g.join(v.delimiter),W=v.addQueryPrefix===!0?"?":"";return v.charsetSentinel&&(v.charset==="iso-8859-1"?W+="utf8=%26%2310003%3B&":W+="utf8=%E2%9C%93&"),J.length>0?W+J:""},Pl}function kx(){if(wb)return Ol;wb=!0;var t=vb(),e=Object.prototype.hasOwnProperty,r=Array.isArray,n={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:t.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1},i=function(w){return w.replace(/&#(\d+);/g,function(y,P){return String.fromCharCode(parseInt(P,10))})},s=function(w,y){return w&&typeof w=="string"&&y.comma&&w.indexOf(",")>-1?w.split(","):w},o="utf8=%26%2310003%3B",a="utf8=%E2%9C%93",c=function(y,P){var E={__proto__:null},b=P.ignoreQueryPrefix?y.replace(/^\?/,""):y;b=b.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var m=P.parameterLimit===1/0?void 0:P.parameterLimit,v=b.split(P.delimiter,m),R=-1,T,g=P.charset;if(P.charsetSentinel)for(T=0;T<v.length;++T)v[T].indexOf("utf8=")===0&&(v[T]===a?g="utf-8":v[T]===o&&(g="iso-8859-1"),R=T,T=v.length);for(T=0;T<v.length;++T)if(T!==R){var O=v[T],_=O.indexOf("]="),q=_===-1?O.indexOf("="):_+1,$,j;q===-1?($=P.decoder(O,n.decoder,g,"key"),j=P.strictNullHandling?null:""):($=P.decoder(O.slice(0,q),n.decoder,g,"key"),j=t.maybeMap(s(O.slice(q+1),P),function(W){return P.decoder(W,n.decoder,g,"value")})),j&&P.interpretNumericEntities&&g==="iso-8859-1"&&(j=i(j)),O.indexOf("[]=")>-1&&(j=r(j)?[j]:j);var J=e.call(E,$);J&&P.duplicates==="combine"?E[$]=t.combine(E[$],j):(!J||P.duplicates==="last")&&(E[$]=j)}return E},f=function(w,y,P,E){for(var b=E?y:s(y,P),m=w.length-1;m>=0;--m){var v,R=w[m];if(R==="[]"&&P.parseArrays)v=P.allowEmptyArrays&&(b===""||P.strictNullHandling&&b===null)?[]:[].concat(b);else{v=P.plainObjects?Object.create(null):{};var T=R.charAt(0)==="["&&R.charAt(R.length-1)==="]"?R.slice(1,-1):R,g=P.decodeDotInKeys?T.replace(/%2E/g,"."):T,O=parseInt(g,10);!P.parseArrays&&g===""?v={0:b}:!isNaN(O)&&R!==g&&String(O)===g&&O>=0&&P.parseArrays&&O<=P.arrayLimit?(v=[],v[O]=b):g!=="__proto__"&&(v[g]=b)}b=v}return b},h=function(y,P,E,b){if(y){var m=E.allowDots?y.replace(/\.([^.[]+)/g,"[$1]"):y,v=/(\[[^[\]]*])/,R=/(\[[^[\]]*])/g,T=E.depth>0&&v.exec(m),g=T?m.slice(0,T.index):m,O=[];if(g){if(!E.plainObjects&&e.call(Object.prototype,g)&&!E.allowPrototypes)return;O.push(g)}for(var _=0;E.depth>0&&(T=R.exec(m))!==null&&_<E.depth;){if(_+=1,!E.plainObjects&&e.call(Object.prototype,T[1].slice(1,-1))&&!E.allowPrototypes)return;O.push(T[1])}if(T){if(E.strictDepth===!0)throw new RangeError("Input depth exceeded depth option of "+E.depth+" and strictDepth is true");O.push("["+m.slice(T.index)+"]")}return f(O,P,E,b)}},d=function(y){if(!y)return n;if(typeof y.allowEmptyArrays<"u"&&typeof y.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof y.decodeDotInKeys<"u"&&typeof y.decodeDotInKeys!="boolean")throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(y.decoder!==null&&typeof y.decoder<"u"&&typeof y.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof y.charset<"u"&&y.charset!=="utf-8"&&y.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var P=typeof y.charset>"u"?n.charset:y.charset,E=typeof y.duplicates>"u"?n.duplicates:y.duplicates;if(E!=="combine"&&E!=="first"&&E!=="last")throw new TypeError("The duplicates option must be either combine, first, or last");var b=typeof y.allowDots>"u"?y.decodeDotInKeys===!0?!0:n.allowDots:!!y.allowDots;return{allowDots:b,allowEmptyArrays:typeof y.allowEmptyArrays=="boolean"?!!y.allowEmptyArrays:n.allowEmptyArrays,allowPrototypes:typeof y.allowPrototypes=="boolean"?y.allowPrototypes:n.allowPrototypes,allowSparse:typeof y.allowSparse=="boolean"?y.allowSparse:n.allowSparse,arrayLimit:typeof y.arrayLimit=="number"?y.arrayLimit:n.arrayLimit,charset:P,charsetSentinel:typeof y.charsetSentinel=="boolean"?y.charsetSentinel:n.charsetSentinel,comma:typeof y.comma=="boolean"?y.comma:n.comma,decodeDotInKeys:typeof y.decodeDotInKeys=="boolean"?y.decodeDotInKeys:n.decodeDotInKeys,decoder:typeof y.decoder=="function"?y.decoder:n.decoder,delimiter:typeof y.delimiter=="string"||t.isRegExp(y.delimiter)?y.delimiter:n.delimiter,depth:typeof y.depth=="number"||y.depth===!1?+y.depth:n.depth,duplicates:E,ignoreQueryPrefix:y.ignoreQueryPrefix===!0,interpretNumericEntities:typeof y.interpretNumericEntities=="boolean"?y.interpretNumericEntities:n.interpretNumericEntities,parameterLimit:typeof y.parameterLimit=="number"?y.parameterLimit:n.parameterLimit,parseArrays:y.parseArrays!==!1,plainObjects:typeof y.plainObjects=="boolean"?y.plainObjects:n.plainObjects,strictDepth:typeof y.strictDepth=="boolean"?!!y.strictDepth:n.strictDepth,strictNullHandling:typeof y.strictNullHandling=="boolean"?y.strictNullHandling:n.strictNullHandling}};return Ol=function(w,y){var P=d(y);if(w===""||w===null||typeof w>"u")return P.plainObjects?Object.create(null):{};for(var E=typeof w=="string"?c(w,P):w,b=P.plainObjects?Object.create(null):{},m=Object.keys(E),v=0;v<m.length;++v){var R=m[v],T=h(R,E[R],P,typeof w=="string");b=t.merge(b,T,P)}return P.allowSparse===!0?b:t.compact(b)},Ol}function Cx(){if(mb)return Rl;mb=!0;var t=Rx(),e=kx(),r=Cl();return Rl={formats:r,parse:e,stringify:t},Rl}function Bx(){if(_b)return Hr;_b=!0;var t=hr;function e(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}var r=/^([a-z0-9.+-]+:)/i,n=/:[0-9]*$/,i=/^(\/\/?(?!\/)[^?\s]*)(\?[^\s]*)?$/,s=["<",">",'"',"`"," ","\r",`
`,"	"],o=["{","}","|","\\","^","`"].concat(s),a=["'"].concat(o),c=["%","/","?",";","#"].concat(a),f=["/","?","#"],h=255,d=/^[+a-z0-9A-Z_-]{0,63}$/,w=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,y={javascript:!0,"javascript:":!0},P={javascript:!0,"javascript:":!0},E={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},b=Cx();function m(g,O,_){if(g&&typeof g=="object"&&g instanceof e)return g;var q=new e;return q.parse(g,O,_),q}e.prototype.parse=function(g,O,_){if(typeof g!="string")throw new TypeError("Parameter 'url' must be a string, not "+typeof g);var q=g.indexOf("?"),$=q!==-1&&q<g.indexOf("#")?"?":"#",j=g.split($),J=/\\/g;j[0]=j[0].replace(J,"/"),g=j.join($);var W=g;if(W=W.trim(),!_&&g.split("#").length===1){var H=i.exec(W);if(H)return this.path=W,this.href=W,this.pathname=H[1],H[2]?(this.search=H[2],O?this.query=b.parse(this.search.substr(1)):this.query=this.search.substr(1)):O&&(this.search="",this.query={}),this}var G=r.exec(W);if(G){G=G[0];var K=G.toLowerCase();this.protocol=K,W=W.substr(G.length)}if(_||G||W.match(/^\/\/[^@/]+@[^@/]+/)){var X=W.substr(0,2)==="//";X&&!(G&&P[G])&&(W=W.substr(2),this.slashes=!0)}if(!P[G]&&(X||G&&!E[G])){for(var Q=-1,V=0;V<f.length;V++){var te=W.indexOf(f[V]);te!==-1&&(Q===-1||te<Q)&&(Q=te)}var Y,ee;Q===-1?ee=W.lastIndexOf("@"):ee=W.lastIndexOf("@",Q),ee!==-1&&(Y=W.slice(0,ee),W=W.slice(ee+1),this.auth=decodeURIComponent(Y)),Q=-1;for(var V=0;V<c.length;V++){var te=W.indexOf(c[V]);te!==-1&&(Q===-1||te<Q)&&(Q=te)}Q===-1&&(Q=W.length),this.host=W.slice(0,Q),W=W.slice(Q),this.parseHost(),this.hostname=this.hostname||"";var pe=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!pe)for(var Z=this.hostname.split(/\./),V=0,ne=Z.length;V<ne;V++){var fe=Z[V];if(fe&&!fe.match(d)){for(var ge="",ye=0,Ae=fe.length;ye<Ae;ye++)fe.charCodeAt(ye)>127?ge+="x":ge+=fe[ye];if(!ge.match(d)){var _e=Z.slice(0,V),ve=Z.slice(V+1),Oe=fe.match(w);Oe&&(_e.push(Oe[1]),ve.unshift(Oe[2])),ve.length&&(W="/"+ve.join(".")+W),this.hostname=_e.join(".");break}}}this.hostname.length>h?this.hostname="":this.hostname=this.hostname.toLowerCase(),pe||(this.hostname=t.toASCII(this.hostname));var xe=this.port?":"+this.port:"",st=this.hostname||"";this.host=st+xe,this.href+=this.host,pe&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),W[0]!=="/"&&(W="/"+W))}if(!y[K])for(var V=0,ne=a.length;V<ne;V++){var Re=a[V];if(W.indexOf(Re)!==-1){var Ne=encodeURIComponent(Re);Ne===Re&&(Ne=escape(Re)),W=W.split(Re).join(Ne)}}var Ct=W.indexOf("#");Ct!==-1&&(this.hash=W.substr(Ct),W=W.slice(0,Ct));var Bt=W.indexOf("?");if(Bt!==-1?(this.search=W.substr(Bt),this.query=W.substr(Bt+1),O&&(this.query=b.parse(this.query)),W=W.slice(0,Bt)):O&&(this.search="",this.query={}),W&&(this.pathname=W),E[K]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){var xe=this.pathname||"",xn=this.search||"";this.path=xe+xn}return this.href=this.format(),this};function v(g){return typeof g=="string"&&(g=m(g)),g instanceof e?g.format():e.prototype.format.call(g)}e.prototype.format=function(){var g=this.auth||"";g&&(g=encodeURIComponent(g),g=g.replace(/%3A/i,":"),g+="@");var O=this.protocol||"",_=this.pathname||"",q=this.hash||"",$=!1,j="";this.host?$=g+this.host:this.hostname&&($=g+(this.hostname.indexOf(":")===-1?this.hostname:"["+this.hostname+"]"),this.port&&($+=":"+this.port)),this.query&&typeof this.query=="object"&&Object.keys(this.query).length&&(j=b.stringify(this.query,{arrayFormat:"repeat",addQueryPrefix:!1}));var J=this.search||j&&"?"+j||"";return O&&O.substr(-1)!==":"&&(O+=":"),this.slashes||(!O||E[O])&&$!==!1?($="//"+($||""),_&&_.charAt(0)!=="/"&&(_="/"+_)):$||($=""),q&&q.charAt(0)!=="#"&&(q="#"+q),J&&J.charAt(0)!=="?"&&(J="?"+J),_=_.replace(/[?#]/g,function(W){return encodeURIComponent(W)}),J=J.replace("#","%23"),O+$+_+J+q};function R(g,O){return m(g,!1,!0).resolve(O)}e.prototype.resolve=function(g){return this.resolveObject(m(g,!1,!0)).format()};function T(g,O){return g?m(g,!1,!0).resolveObject(O):O}return e.prototype.resolveObject=function(g){if(typeof g=="string"){var O=new e;O.parse(g,!1,!0),g=O}for(var _=new e,q=Object.keys(this),$=0;$<q.length;$++){var j=q[$];_[j]=this[j]}if(_.hash=g.hash,g.href==="")return _.href=_.format(),_;if(g.slashes&&!g.protocol){for(var J=Object.keys(g),W=0;W<J.length;W++){var H=J[W];H!=="protocol"&&(_[H]=g[H])}return E[_.protocol]&&_.hostname&&!_.pathname&&(_.pathname="/",_.path=_.pathname),_.href=_.format(),_}if(g.protocol&&g.protocol!==_.protocol){if(!E[g.protocol]){for(var G=Object.keys(g),K=0;K<G.length;K++){var X=G[K];_[X]=g[X]}return _.href=_.format(),_}if(_.protocol=g.protocol,!g.host&&!P[g.protocol]){for(var ne=(g.pathname||"").split("/");ne.length&&!(g.host=ne.shift()););g.host||(g.host=""),g.hostname||(g.hostname=""),ne[0]!==""&&ne.unshift(""),ne.length<2&&ne.unshift(""),_.pathname=ne.join("/")}else _.pathname=g.pathname;if(_.search=g.search,_.query=g.query,_.host=g.host||"",_.auth=g.auth,_.hostname=g.hostname||g.host,_.port=g.port,_.pathname||_.search){var Q=_.pathname||"",V=_.search||"";_.path=Q+V}return _.slashes=_.slashes||g.slashes,_.href=_.format(),_}var te=_.pathname&&_.pathname.charAt(0)==="/",Y=g.host||g.pathname&&g.pathname.charAt(0)==="/",ee=Y||te||_.host&&g.pathname,pe=ee,Z=_.pathname&&_.pathname.split("/")||[],ne=g.pathname&&g.pathname.split("/")||[],fe=_.protocol&&!E[_.protocol];if(fe&&(_.hostname="",_.port=null,_.host&&(Z[0]===""?Z[0]=_.host:Z.unshift(_.host)),_.host="",g.protocol&&(g.hostname=null,g.port=null,g.host&&(ne[0]===""?ne[0]=g.host:ne.unshift(g.host)),g.host=null),ee=ee&&(ne[0]===""||Z[0]==="")),Y)_.host=g.host||g.host===""?g.host:_.host,_.hostname=g.hostname||g.hostname===""?g.hostname:_.hostname,_.search=g.search,_.query=g.query,Z=ne;else if(ne.length)Z||(Z=[]),Z.pop(),Z=Z.concat(ne),_.search=g.search,_.query=g.query;else if(g.search!=null){if(fe){_.host=Z.shift(),_.hostname=_.host;var ge=_.host&&_.host.indexOf("@")>0?_.host.split("@"):!1;ge&&(_.auth=ge.shift(),_.hostname=ge.shift(),_.host=_.hostname)}return _.search=g.search,_.query=g.query,(_.pathname!==null||_.search!==null)&&(_.path=(_.pathname?_.pathname:"")+(_.search?_.search:"")),_.href=_.format(),_}if(!Z.length)return _.pathname=null,_.search?_.path="/"+_.search:_.path=null,_.href=_.format(),_;for(var ye=Z.slice(-1)[0],Ae=(_.host||g.host||Z.length>1)&&(ye==="."||ye==="..")||ye==="",_e=0,ve=Z.length;ve>=0;ve--)ye=Z[ve],ye==="."?Z.splice(ve,1):ye===".."?(Z.splice(ve,1),_e++):_e&&(Z.splice(ve,1),_e--);if(!ee&&!pe)for(;_e--;_e)Z.unshift("..");ee&&Z[0]!==""&&(!Z[0]||Z[0].charAt(0)!=="/")&&Z.unshift(""),Ae&&Z.join("/").substr(-1)!=="/"&&Z.push("");var Oe=Z[0]===""||Z[0]&&Z[0].charAt(0)==="/";if(fe){_.hostname=Oe?"":Z.length?Z.shift():"",_.host=_.hostname;var ge=_.host&&_.host.indexOf("@")>0?_.host.split("@"):!1;ge&&(_.auth=ge.shift(),_.hostname=ge.shift(),_.host=_.hostname)}return ee=ee||_.host&&Z.length,ee&&!Oe&&Z.unshift(""),Z.length>0?_.pathname=Z.join("/"):(_.pathname=null,_.path=null),(_.pathname!==null||_.search!==null)&&(_.path=(_.pathname?_.pathname:"")+(_.search?_.search:"")),_.auth=g.auth||_.auth,_.slashes=_.slashes||g.slashes,_.href=_.format(),_},e.prototype.parseHost=function(){var g=this.host,O=n.exec(g);O&&(O=O[0],O!==":"&&(this.port=O.substr(1)),g=g.substr(0,g.length-O.length)),g&&(this.hostname=g)},Hr.parse=m,Hr.resolve=R,Hr.resolveObject=T,Hr.format=v,Hr.Url=e,Hr}function Sb(t){if(typeof t=="string")t=new URL(t);else if(!(t instanceof URL))throw new Deno.errors.InvalidData("invalid argument path , must be a string or URL");if(t.protocol!=="file:")throw new Deno.errors.InvalidData("invalid url scheme");return kl?Jx(t):Xx(t)}function Jx(t){let e=t.hostname,r=t.pathname;for(let n=0;n<r.length;n++)if(r[n]==="%"){let i=r.codePointAt(n+2)||32;if(r[n+1]==="2"&&i===102||r[n+1]==="5"&&i===99)throw new Deno.errors.InvalidData("must not include encoded \\ or / characters")}if(r=r.replace(Vx,"\\"),r=decodeURIComponent(r),e!=="")return`\\\\${e}${r}`;{let n=r.codePointAt(1)|32,i=r[2];if(n<$x||n>Hx||i!==":")throw new Deno.errors.InvalidData("file url path must be absolute");return r.slice(1)}}function Xx(t){if(t.hostname!=="")throw new Deno.errors.InvalidData("invalid file url hostname");let e=t.pathname;for(let r=0;r<e.length;r++)if(e[r]==="%"){let n=e.codePointAt(r+2)||32;if(e[r+1]==="2"&&n===102)throw new Deno.errors.InvalidData("must not include encoded / characters")}return decodeURIComponent(e)}function Eb(t){let e=Sl.resolve(t),r=t.charCodeAt(t.length-1);(r===Wx||kl&&r===Fx)&&e[e.length-1]!==Sl.sep&&(e+="/");let n=new URL("file://");return e.includes("%")&&(e=e.replace(Gx,"%25")),!kl&&e.includes("\\")&&(e=e.replace(zx,"%5C")),e.includes(`
`)&&(e=e.replace(Kx,"%0A")),e.includes("\r")&&(e=e.replace(Qx,"%0D")),e.includes("	")&&(e=e.replace(Yx,"%09")),n.pathname=e,n}var Tx,El,db,Al,Il,pb,xl,yb,Tl,gb,Pl,bb,Ol,wb,Rl,mb,Hr,_b,Ge,Mx,Nx,qx,Lx,Ux,Dx,jx,Fx,Wx,$x,Hx,kl,Vx,Gx,zx,Kx,Qx,Yx,Ib=ze(()=>{A();x();I();Lg();ub();hb();_l();Tx=Object.freeze(Object.create(null)),El={},db=!1,Al=typeof globalThis<"u"?globalThis:typeof self<"u"?self:global;Il={},pb=!1;xl={},yb=!1;Tl={},gb=!1;Pl={},bb=!1;Ol={},wb=!1;Rl={},mb=!1;Hr={},_b=!1;Ge=Bx();Ge.parse;Ge.resolve;Ge.resolveObject;Ge.format;Ge.Url;Mx=typeof Deno<"u"?Deno.build.os==="windows"?"win32":Deno.build.os:void 0;Ge.URL=typeof URL<"u"?URL:null;Ge.pathToFileURL=Eb;Ge.fileURLToPath=Sb;Nx=Ge.Url,qx=Ge.format,Lx=Ge.resolve,Ux=Ge.resolveObject,Dx=Ge.parse,jx=Ge.URL,Fx=92,Wx=47,$x=97,Hx=122,kl=Mx==="win32",Vx=/\//g,Gx=/%/g,zx=/\\/g,Kx=/\n/g,Qx=/\r/g,Yx=/\t/g});var Tb=D((A9,xb)=>{"use strict";A();x();I();xb.exports=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}});var Qo=D(mi=>{"use strict";A();x();I();Object.defineProperty(mi,"__esModule",{value:!0});mi.BufferedDuplex=void 0;mi.writev=Ob;var Zx=or(),Pb=(ke(),me(qe));function Ob(t,e){let r=new Array(t.length);for(let n=0;n<t.length;n++)typeof t[n].chunk=="string"?r[n]=Pb.Buffer.from(t[n].chunk,"utf8"):r[n]=t[n].chunk;this._write(Pb.Buffer.concat(r),"binary",e)}var Bl=class extends Zx.Duplex{socket;proxy;isSocketOpen;writeQueue;constructor(e,r,n){super({objectMode:!0}),this.proxy=r,this.socket=n,this.writeQueue=[],e.objectMode||(this._writev=Ob.bind(this)),this.isSocketOpen=!1,this.proxy.on("data",i=>{!this.destroyed&&this.readable&&this.push(i)})}_read(e){this.proxy.read(e)}_write(e,r,n){this.isSocketOpen?this.writeToProxy(e,r,n):this.writeQueue.push({chunk:e,encoding:r,cb:n})}_final(e){this.writeQueue=[],this.proxy.end(e)}_destroy(e,r){this.writeQueue=[],this.proxy.destroy(),r(e)}socketReady(){this.emit("connect"),this.isSocketOpen=!0,this.processWriteQueue()}writeToProxy(e,r,n){this.proxy.write(e,r)===!1?this.proxy.once("drain",n):n()}processWriteQueue(){for(;this.writeQueue.length>0;){let{chunk:e,encoding:r,cb:n}=this.writeQueue.shift();this.writeToProxy(e,r,n)}}};mi.BufferedDuplex=Bl});var _i=D(gr=>{"use strict";A();x();I();var Nl=gr&&gr.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(gr,"__esModule",{value:!0});gr.streamBuilder=gr.browserStreamBuilder=void 0;var Yo=(ke(),me(qe)),Rb=Nl(Tb()),eT=Nl(At()),tT=or(),rT=Nl(yi()),Ml=Qo(),yr=(0,eT.default)("mqttjs:ws"),nT=["rejectUnauthorized","ca","cert","key","pfx","passphrase"];function kb(t,e){let r=`${t.protocol}://${t.hostname}:${t.port}${t.path}`;return typeof t.transformWsUrl=="function"&&(r=t.transformWsUrl(r,t,e)),r}function Cb(t){let e=t;return t.port||(t.protocol==="wss"?e.port=443:e.port=80),t.path||(e.path="/"),t.wsOptions||(e.wsOptions={}),!rT.default&&!t.forceNativeWebSocket&&t.protocol==="wss"&&nT.forEach(r=>{Object.prototype.hasOwnProperty.call(t,r)&&!Object.prototype.hasOwnProperty.call(t.wsOptions,r)&&(e.wsOptions[r]=t[r])}),e}function iT(t){let e=Cb(t);if(e.hostname||(e.hostname=e.host),!e.hostname){if(typeof document>"u")throw new Error("Could not determine host. Specify host manually.");let r=new URL(document.URL);e.hostname=r.hostname,e.port||(e.port=Number(r.port))}return e.objectMode===void 0&&(e.objectMode=!(e.binary===!0||e.binary===void 0)),e}function oT(t,e,r){yr("createWebSocket"),yr(`protocol: ${r.protocolId} ${r.protocolVersion}`);let n=r.protocolId==="MQIsdp"&&r.protocolVersion===3?"mqttv3.1":"mqtt";yr(`creating new Websocket for url: ${e} and protocol: ${n}`);let i;return r.createWebsocket?i=r.createWebsocket(e,[n],r):i=new Rb.default(e,[n],r.wsOptions),i}function sT(t,e){let r=e.protocolId==="MQIsdp"&&e.protocolVersion===3?"mqttv3.1":"mqtt",n=kb(e,t),i;return e.createWebsocket?i=e.createWebsocket(n,[r],e):i=new WebSocket(n,[r]),i.binaryType="arraybuffer",i}var aT=(t,e)=>{yr("streamBuilder");let r=Cb(e);r.hostname=r.hostname||r.host||"localhost";let n=kb(r,t),i=oT(t,n,r),s=Rb.default.createWebSocketStream(i,r.wsOptions);return s.url=n,i.on("close",()=>{s.destroy()}),s};gr.streamBuilder=aT;var uT=(t,e)=>{yr("browserStreamBuilder");let r,i=iT(e).browserBufferSize||1024*512,s=e.browserBufferTimeout||1e3,o=!e.objectMode,a=sT(t,e),c=h(e,E,b);e.objectMode||(c._writev=Ml.writev.bind(c)),c.on("close",()=>{a.close()});let f=typeof a.addEventListener<"u";a.readyState===a.OPEN?(r=c,r.socket=a):(r=new Ml.BufferedDuplex(e,c,a),f?a.addEventListener("open",d):a.onopen=d),f?(a.addEventListener("close",w),a.addEventListener("error",y),a.addEventListener("message",P)):(a.onclose=w,a.onerror=y,a.onmessage=P);function h(m,v,R){let T=new tT.Transform({objectMode:m.objectMode});return T._write=v,T._flush=R,T}function d(){yr("WebSocket onOpen"),r instanceof Ml.BufferedDuplex&&r.socketReady()}function w(m){yr("WebSocket onClose",m),r.end(),r.destroy()}function y(m){yr("WebSocket onError",m);let v=new Error("WebSocket error");v.event=m,r.destroy(v)}async function P(m){if(!c||!c.readable||!c.writable)return;let{data:v}=m;v instanceof ArrayBuffer?v=Yo.Buffer.from(v):v instanceof Blob?v=Yo.Buffer.from(await new Response(v).arrayBuffer()):v=Yo.Buffer.from(v,"utf8"),c.push(v)}function E(m,v,R){if(a.bufferedAmount>i){setTimeout(E,s,m,v,R);return}o&&typeof m=="string"&&(m=Yo.Buffer.from(m,"utf8"));try{a.send(m)}catch(T){return R(T)}R()}function b(m){a.close(),m()}return r};gr.browserStreamBuilder=uT});var ql={};Kr(ql,{Server:()=>Je,Socket:()=>Je,Stream:()=>Je,_createServerHandle:()=>Je,_normalizeArgs:()=>Je,_setSimultaneousAccepts:()=>Je,connect:()=>Je,createConnection:()=>Je,createServer:()=>Je,default:()=>lT,isIP:()=>Je,isIPv4:()=>Je,isIPv6:()=>Je});function Je(){throw new Error("Node.js net module is not supported by JSPM core outside of Node.js")}var lT,Ll=ze(()=>{A();x();I();lT={_createServerHandle:Je,_normalizeArgs:Je,_setSimultaneousAccepts:Je,connect:Je,createConnection:Je,createServer:Je,isIP:Je,isIPv4:Je,isIPv6:Je,Server:Je,Socket:Je,Stream:Je}});var Ul=D((lH,Bb)=>{A();x();I();Bb.exports={}});var jl=D(vi=>{"use strict";A();x();I();var Dl=vi&&vi.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(vi,"__esModule",{value:!0});var cT=Dl((Ll(),me(ql))),fT=Dl(At()),hT=Dl(Ul()),dT=(0,fT.default)("mqttjs:tcp"),pT=(t,e)=>{if(e.port=e.port||1883,e.hostname=e.hostname||e.host||"localhost",e.socksProxy)return(0,hT.default)(e.hostname,e.port,e.socksProxy,{timeout:e.socksTimeout});let{port:r,path:n}=e,i=e.hostname;return dT("port %d and host %s",r,i),cT.default.createConnection({port:r,host:i,path:n})};vi.default=pT});var Mb={};Kr(Mb,{default:()=>yT});var yT,Nb=ze(()=>{A();x();I();yT={}});var Wl=D(Si=>{"use strict";A();x();I();var Fl=Si&&Si.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Si,"__esModule",{value:!0});var qb=(Nb(),me(Mb)),gT=Fl((Ll(),me(ql))),bT=Fl(At()),wT=Fl(Ul()),mT=(0,bT.default)("mqttjs:tls");function _T(t){let{host:e,port:r,socksProxy:n,...i}=t;if(n!==void 0){let s=(0,wT.default)(e,r,n,{timeout:t.socksTimeout});return(0,qb.connect)({...i,socket:s})}return(0,qb.connect)(t)}var vT=(t,e)=>{e.port=e.port||8883,e.host=e.hostname||e.host||"localhost",gT.default.isIP(e.host)===0&&(e.servername=e.host),e.rejectUnauthorized=e.rejectUnauthorized!==!1,delete e.path,mT("port %d host %s rejectUnauthorized %b",e.port,e.host,e.rejectUnauthorized);let r=_T(e);r.on("secureConnect",()=>{e.rejectUnauthorized&&!r.authorized?r.emit("error",new Error("TLS not authorized")):r.removeListener("error",n)});function n(i){e.rejectUnauthorized&&t.emit("error",i),r.end()}return r.on("error",n),r};Si.default=vT});var Vl=D(Hl=>{"use strict";A();x();I();Object.defineProperty(Hl,"__esModule",{value:!0});var Lb=(ke(),me(qe)),ST=or(),ET=Qo(),kt,$l,Xe;function AT(){let t=new ST.Transform;return t._write=(e,r,n)=>{kt.send({data:e.buffer,success(){n()},fail(i){n(new Error(i))}})},t._flush=e=>{kt.close({success(){e()}})},t}function IT(t){t.hostname||(t.hostname="localhost"),t.path||(t.path="/"),t.wsOptions||(t.wsOptions={})}function xT(t,e){let r=t.protocol==="wxs"?"wss":"ws",n=`${r}://${t.hostname}${t.path}`;return t.port&&t.port!==80&&t.port!==443&&(n=`${r}://${t.hostname}:${t.port}${t.path}`),typeof t.transformWsUrl=="function"&&(n=t.transformWsUrl(n,t,e)),n}function TT(){kt.onOpen(()=>{Xe.socketReady()}),kt.onMessage(t=>{let{data:e}=t;e instanceof ArrayBuffer?e=Lb.Buffer.from(e):e=Lb.Buffer.from(e,"utf8"),$l.push(e)}),kt.onClose(()=>{Xe.emit("close"),Xe.end(),Xe.destroy()}),kt.onError(t=>{let e=new Error(t.errMsg);Xe.destroy(e)})}var PT=(t,e)=>{if(e.hostname=e.hostname||e.host,!e.hostname)throw new Error("Could not determine host. Specify host manually.");let r=e.protocolId==="MQIsdp"&&e.protocolVersion===3?"mqttv3.1":"mqtt";IT(e);let n=xT(e,t);kt=wx.connectSocket({url:n,protocols:[r]}),$l=AT(),Xe=new ET.BufferedDuplex(e,$l,kt),Xe._destroy=(s,o)=>{kt.close({success(){o&&o(s)}})};let i=Xe.destroy;return Xe.destroy=(s,o)=>(Xe.destroy=i,setTimeout(()=>{kt.close({fail(){Xe._destroy(s,o)}})},0),Xe),TT(),Xe};Hl.default=PT});var Kl=D(zl=>{"use strict";A();x();I();Object.defineProperty(zl,"__esModule",{value:!0});var Gl=(ke(),me(qe)),OT=or(),RT=Qo(),Kt,Ei,An,Ub=!1;function kT(){let t=new OT.Transform;return t._write=(e,r,n)=>{Kt.sendSocketMessage({data:e.buffer,success(){n()},fail(){n(new Error)}})},t._flush=e=>{Kt.closeSocket({success(){e()}})},t}function CT(t){t.hostname||(t.hostname="localhost"),t.path||(t.path="/"),t.wsOptions||(t.wsOptions={})}function BT(t,e){let r=t.protocol==="alis"?"wss":"ws",n=`${r}://${t.hostname}${t.path}`;return t.port&&t.port!==80&&t.port!==443&&(n=`${r}://${t.hostname}:${t.port}${t.path}`),typeof t.transformWsUrl=="function"&&(n=t.transformWsUrl(n,t,e)),n}function MT(){Ub||(Ub=!0,Kt.onSocketOpen(()=>{An.socketReady()}),Kt.onSocketMessage(t=>{if(typeof t.data=="string"){let e=Gl.Buffer.from(t.data,"base64");Ei.push(e)}else{let e=new FileReader;e.addEventListener("load",()=>{if(e.result instanceof ArrayBuffer){Ei.push(Gl.Buffer.from(e.result));return}Ei.push(Gl.Buffer.from(e.result,"utf-8"))}),e.readAsArrayBuffer(t.data)}}),Kt.onSocketClose(()=>{An.end(),An.destroy()}),Kt.onSocketError(t=>{An.destroy(t)}))}var NT=(t,e)=>{if(e.hostname=e.hostname||e.host,!e.hostname)throw new Error("Could not determine host. Specify host manually.");let r=e.protocolId==="MQIsdp"&&e.protocolVersion===3?"mqttv3.1":"mqtt";CT(e);let n=BT(e,t);return Kt=e.my,Kt.connectSocket({url:n,protocols:r}),Ei=kT(),An=new RT.BufferedDuplex(e,Ei,Kt),MT(),An};zl.default=NT});var Wb=D(In=>{"use strict";A();x();I();var Jo=In&&In.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(In,"__esModule",{value:!0});In.connectAsync=jT;var qT=Jo(At()),LT=Jo((Ib(),me(Ab))),UT=Jo(zo()),Db=Jo(yi());typeof M?.nextTick!="function"&&(M.nextTick=setImmediate);var jb=(0,qT.default)("mqttjs"),Me=null;function DT(t){let e;if(t.auth)if(e=t.auth.match(/^(.+):(.+)$/),e){let[,r,n]=e;t.username=r,t.password=n}else t.username=t.auth}function Fb(t,e){if(jb("connecting to an MQTT broker..."),typeof t=="object"&&!e&&(e=t,t=""),e=e||{},t&&typeof t=="string"){let i=LT.default.parse(t,!0),s={};if(i.port!=null&&(s.port=Number(i.port)),s.host=i.hostname,s.query=i.query,s.auth=i.auth,s.protocol=i.protocol,s.path=i.path,e={...s,...e},!e.protocol)throw new Error("Missing protocol");e.protocol=e.protocol.replace(/:$/,"")}if(e.unixSocket=e.unixSocket||e.protocol?.includes("+unix"),e.unixSocket?e.protocol=e.protocol.replace("+unix",""):!e.protocol?.startsWith("ws")&&!e.protocol?.startsWith("wx")&&delete e.path,DT(e),e.query&&typeof e.query.clientId=="string"&&(e.clientId=e.query.clientId),Db.default||e.unixSocket?e.socksProxy=void 0:e.socksProxy===void 0&&typeof M<"u"&&(e.socksProxy=M.env.MQTTJS_SOCKS_PROXY),e.cert&&e.key)if(e.protocol){if(["mqtts","wss","wxs","alis"].indexOf(e.protocol)===-1)switch(e.protocol){case"mqtt":e.protocol="mqtts";break;case"ws":e.protocol="wss";break;case"wx":e.protocol="wxs";break;case"ali":e.protocol="alis";break;default:throw new Error(`Unknown protocol for secure connection: "${e.protocol}"!`)}}else throw new Error("Missing secure protocol key");if(Me||(Me={},!Db.default&&!e.forceNativeWebSocket?(Me.ws=_i().streamBuilder,Me.wss=_i().streamBuilder,Me.mqtt=jl().default,Me.tcp=jl().default,Me.ssl=Wl().default,Me.tls=Me.ssl,Me.mqtts=Wl().default):(Me.ws=_i().browserStreamBuilder,Me.wss=_i().browserStreamBuilder,Me.wx=Vl().default,Me.wxs=Vl().default,Me.ali=Kl().default,Me.alis=Kl().default)),!Me[e.protocol]){let i=["mqtts","wss"].indexOf(e.protocol)!==-1;e.protocol=["mqtt","mqtts","ws","wss","wx","wxs","ali","alis"].filter((s,o)=>i&&o%2===0?!1:typeof Me[s]=="function")[0]}if(e.clean===!1&&!e.clientId)throw new Error("Missing clientId for unclean clients");e.protocol&&(e.defaultProtocol=e.protocol);function r(i){return e.servers&&((!i._reconnectCount||i._reconnectCount===e.servers.length)&&(i._reconnectCount=0),e.host=e.servers[i._reconnectCount].host,e.port=e.servers[i._reconnectCount].port,e.protocol=e.servers[i._reconnectCount].protocol?e.servers[i._reconnectCount].protocol:e.defaultProtocol,e.hostname=e.host,i._reconnectCount++),jb("calling streambuilder for",e.protocol),Me[e.protocol](i,e)}let n=new UT.default(r,e);return n.on("error",()=>{}),n}function jT(t,e,r=!0){return new Promise((n,i)=>{let s=Fb(t,e),o={connect:c=>{a(),n(s)},end:()=>{a(),n(s)},error:c=>{a(),s.end(),i(c)}};r===!1&&(o.close=()=>{o.error(new Error("Couldn't connect to server"))});function a(){Object.keys(o).forEach(c=>{s.off(c,o[c])})}Object.keys(o).forEach(c=>{s.on(c,o[c])})})}In.default=Fb});var Ql=D(we=>{"use strict";A();x();I();var $b=we&&we.__createBinding||(Object.create?function(t,e,r,n){n===void 0&&(n=r);var i=Object.getOwnPropertyDescriptor(e,r);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,i)}:function(t,e,r,n){n===void 0&&(n=r),t[n]=e[r]}),FT=we&&we.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),WT=we&&we.__importStar||function(){var t=function(e){return t=Object.getOwnPropertyNames||function(r){var n=[];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[n.length]=i);return n},t(e)};return function(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var n=t(e),i=0;i<n.length;i++)n[i]!=="default"&&$b(r,e,n[i]);return FT(r,e),r}}(),Hb=we&&we.__exportStar||function(t,e){for(var r in t)r!=="default"&&!Object.prototype.hasOwnProperty.call(e,r)&&$b(e,t,r)},Ai=we&&we.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(we,"__esModule",{value:!0});we.ReasonCodes=we.KeepaliveManager=we.UniqueMessageIdProvider=we.DefaultMessageIdProvider=we.Store=we.MqttClient=we.connectAsync=we.connect=we.Client=void 0;var Vb=Ai(zo());we.MqttClient=Vb.default;var $T=Ai(Au());we.DefaultMessageIdProvider=$T.default;var HT=Ai(Ng());we.UniqueMessageIdProvider=HT.default;var VT=Ai(La());we.Store=VT.default;var Gb=WT(Wb());we.connect=Gb.default;Object.defineProperty(we,"connectAsync",{enumerable:!0,get:function(){return Gb.connectAsync}});var GT=Ai(Du());we.KeepaliveManager=GT.default;we.Client=Vb.default;Hb(zo(),we);Hb(Mr(),we);var zT=zn();Object.defineProperty(we,"ReasonCodes",{enumerable:!0,get:function(){return zT.ReasonCodes}})});var XT=D(ot=>{A();x();I();var zb=ot&&ot.__createBinding||(Object.create?function(t,e,r,n){n===void 0&&(n=r);var i=Object.getOwnPropertyDescriptor(e,r);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,i)}:function(t,e,r,n){n===void 0&&(n=r),t[n]=e[r]}),KT=ot&&ot.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),QT=ot&&ot.__importStar||function(){var t=function(e){return t=Object.getOwnPropertyNames||function(r){var n=[];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[n.length]=i);return n},t(e)};return function(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var n=t(e),i=0;i<n.length;i++)n[i]!=="default"&&zb(r,e,n[i]);return KT(r,e),r}}(),YT=ot&&ot.__exportStar||function(t,e){for(var r in t)r!=="default"&&!Object.prototype.hasOwnProperty.call(e,r)&&zb(e,t,r)};Object.defineProperty(ot,"__esModule",{value:!0});var JT=QT(Ql());ot.default=JT;YT(Ql(),ot)});export default XT();
/*! Bundled license information:

@jspm/core/nodelibs/browser/chunk-DtuTasat.js:
  (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)

safe-buffer/index.js:
  (*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> *)

@babel/runtime/helpers/regenerator.js:
  (*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE *)
*/
