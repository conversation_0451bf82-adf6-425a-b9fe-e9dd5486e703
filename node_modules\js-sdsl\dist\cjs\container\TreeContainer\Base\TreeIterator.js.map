{"version": 3, "sources": ["container/TreeContainer/Base/TreeIterator.js", "../../src/container/TreeContainer/Base/TreeIterator.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_ContainerBase", "require", "_throwError", "TreeIterator", "ContainerIterator", "constructor", "node", "header", "iteratorType", "super", "this", "_node", "_header", "pre", "_left", "throwIteratorAccessError", "_pre", "next", "_next", "_right", "index", "root", "_parent", "_subTreeSize", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACHvB,IAAAC,iBAAAC,QAAA;;AAEA,IAAAC,cAAAD,QAAA;;AAEA,MAAeE,qBAA2BC,eAAAA;IAaxCC,YACEC,GACAC,GACAC;QAEAC,MAAMD;QACNE,KAAKC,IAAQL;QACbI,KAAKE,IAAUL;QACf,IAAIG,KAAKF,iBAAY,GAA0B;YAC7CE,KAAKG,MAAM;gBACT,IAAIH,KAAKC,MAAUD,KAAKE,EAAQE,GAAO;qBACrC,GAAAC,YAAAA;ADhBM;gBCkBRL,KAAKC,IAAQD,KAAKC,EAAMK;gBACxB,OAAON;ADhBH;YCmBNA,KAAKO,OAAO;gBACV,IAAIP,KAAKC,MAAUD,KAAKE,GAAS;qBAC/B,GAAAG,YAAAA;ADjBM;gBCmBRL,KAAKC,IAAQD,KAAKC,EAAMO;gBACxB,OAAOR;ADjBH;AACJ,eCkBG;YACLA,KAAKG,MAAM;gBACT,IAAIH,KAAKC,MAAUD,KAAKE,EAAQO,GAAQ;qBACtC,GAAAJ,YAAAA;ADhBM;gBCkBRL,KAAKC,IAAQD,KAAKC,EAAMO;gBACxB,OAAOR;ADhBH;YCmBNA,KAAKO,OAAO;gBACV,IAAIP,KAAKC,MAAUD,KAAKE,GAAS;qBAC/B,GAAAG,YAAAA;ADjBM;gBCmBRL,KAAKC,IAAQD,KAAKC,EAAMK;gBACxB,OAAON;ADjBH;AACJ;AACJ;IC4BEU;QACF,IAAIT,IAAQD,KAAKC;QACjB,MAAMU,IAAOX,KAAKE,EAAQU;QAC1B,IAAIX,MAAUD,KAAKE,GAAS;YAC1B,IAAIS,GAAM;gBACR,OAAOA,EAAKE,KAAe;ADjBvB;YCmBN,OAAO;ADjBL;QCmBJ,IAAIH,IAAQ;QACZ,IAAIT,EAAMG,GAAO;YACfM,KAAUT,EAAMG,EAAoCS;ADjBlD;QCmBJ,OAAOZ,MAAUU,GAAM;YACrB,MAAMC,IAAUX,EAAMW;YACtB,IAAIX,MAAUW,EAAQH,GAAQ;gBAC5BC,KAAS;gBACT,IAAIE,EAAQR,GAAO;oBACjBM,KAAUE,EAAQR,EAAoCS;ADjBhD;AACJ;YCmBNZ,IAAQW;ADjBN;QCmBJ,OAAOF;ADjBP;;;ACuBH,IAAAI,WAEcrB;;AAAYN,QAAAE,UAAAyB", "file": "TreeIterator.js", "sourcesContent": ["import { ContainerIterator } from \"../../ContainerBase\";\nimport { throwIteratorAccessError } from \"../../../utils/throwError\";\nclass TreeIterator extends ContainerIterator {\n    /**\n     * @internal\n     */\n    constructor(node, header, iteratorType) {\n        super(iteratorType);\n        this._node = node;\n        this._header = header;\n        if (this.iteratorType === 0 /* IteratorType.NORMAL */) {\n            this.pre = function () {\n                if (this._node === this._header._left) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._pre();\n                return this;\n            };\n            this.next = function () {\n                if (this._node === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._next();\n                return this;\n            };\n        }\n        else {\n            this.pre = function () {\n                if (this._node === this._header._right) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._next();\n                return this;\n            };\n            this.next = function () {\n                if (this._node === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._pre();\n                return this;\n            };\n        }\n    }\n    /**\n     * @description Get the sequential index of the iterator in the tree container.<br/>\n     *              <strong>Note:</strong>\n     *              This function only takes effect when the specified tree container `enableIndex = true`.\n     * @returns The index subscript of the node in the tree.\n     * @example\n     * const st = new OrderedSet([1, 2, 3], true);\n     * console.log(st.begin().next().index);  // 1\n     */\n    get index() {\n        let _node = this._node;\n        const root = this._header._parent;\n        if (_node === this._header) {\n            if (root) {\n                return root._subTreeSize - 1;\n            }\n            return 0;\n        }\n        let index = 0;\n        if (_node._left) {\n            index += _node._left._subTreeSize;\n        }\n        while (_node !== root) {\n            const _parent = _node._parent;\n            if (_node === _parent._right) {\n                index += 1;\n                if (_parent._left) {\n                    index += _parent._left._subTreeSize;\n                }\n            }\n            _node = _parent;\n        }\n        return index;\n    }\n}\nexport default TreeIterator;\n", "import { TreeNode } from './TreeNode';\nimport type { TreeNodeEnableIndex } from './TreeNode';\nimport { ContainerIterator, IteratorType } from '@/container/ContainerBase';\nimport TreeContainer from '@/container/TreeContainer/Base/index';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nabstract class TreeIterator<K, V> extends ContainerIterator<K | [K, V]> {\n  abstract readonly container: TreeContainer<K, V>;\n  /**\n   * @internal\n   */\n  _node: TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected _header: TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected constructor(\n    node: TreeNode<K, V>,\n    header: TreeNode<K, V>,\n    iteratorType?: IteratorType\n  ) {\n    super(iteratorType);\n    this._node = node;\n    this._header = header;\n    if (this.iteratorType === IteratorType.NORMAL) {\n      this.pre = function () {\n        if (this._node === this._header._left) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre();\n        return this;\n      };\n\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next();\n        return this;\n      };\n    } else {\n      this.pre = function () {\n        if (this._node === this._header._right) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next();\n        return this;\n      };\n\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre();\n        return this;\n      };\n    }\n  }\n  /**\n   * @description Get the sequential index of the iterator in the tree container.<br/>\n   *              <strong>Note:</strong>\n   *              This function only takes effect when the specified tree container `enableIndex = true`.\n   * @returns The index subscript of the node in the tree.\n   * @example\n   * const st = new OrderedSet([1, 2, 3], true);\n   * console.log(st.begin().next().index);  // 1\n   */\n  get index() {\n    let _node = this._node as TreeNodeEnableIndex<K, V>;\n    const root = this._header._parent as TreeNodeEnableIndex<K, V>;\n    if (_node === this._header) {\n      if (root) {\n        return root._subTreeSize - 1;\n      }\n      return 0;\n    }\n    let index = 0;\n    if (_node._left) {\n      index += (_node._left as TreeNodeEnableIndex<K, V>)._subTreeSize;\n    }\n    while (_node !== root) {\n      const _parent = _node._parent as TreeNodeEnableIndex<K, V>;\n      if (_node === _parent._right) {\n        index += 1;\n        if (_parent._left) {\n          index += (_parent._left as TreeNodeEnableIndex<K, V>)._subTreeSize;\n        }\n      }\n      _node = _parent;\n    }\n    return index;\n  }\n  // @ts-ignore\n  pre(): this;\n  // @ts-ignore\n  next(): this;\n}\n\nexport default TreeIterator;\n"]}