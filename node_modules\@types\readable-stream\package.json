{"name": "@types/readable-stream", "version": "4.0.21", "description": "TypeScript definitions for readable-stream", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/readable-stream", "license": "MIT", "contributors": [{"name": "TeamworkGuy2", "githubUsername": "TeamworkGuy2", "url": "https://github.com/TeamworkGuy2"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/markdreyer"}, {"name": "<PERSON>", "githubUsername": "mcollina", "url": "https://github.com/mcollina"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/readable-stream"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "a50117351e3ebe9502d7b93b500876bf0fd2e600beae1d04e514d47997dcf0dc", "typeScriptVersion": "5.1"}