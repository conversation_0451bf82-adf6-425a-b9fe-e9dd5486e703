{"name": "help-me", "version": "5.0.0", "description": "Help command for node, partner of minimist and commist", "main": "help-me.js", "scripts": {"test": "standard && node test.js | tap-spec"}, "repository": {"type": "git", "url": "https://github.com/mcollina/help-me.git"}, "keywords": ["help", "command", "minimist", "commist"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/mcollina/help-me/issues"}, "homepage": "https://github.com/mcollina/help-me", "devDependencies": {"commist": "^2.0.0", "concat-stream": "^2.0.0", "pre-commit": "^1.1.3", "proxyquire": "^2.1.3", "standard": "^16.0.0", "tap-spec": "^5.0.0", "tape": "^5.0.0"}, "dependencies": {}}