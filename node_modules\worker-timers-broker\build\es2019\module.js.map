{"version": 3, "file": "module.js", "sourceRoot": "", "sources": ["../../src/module.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAK3D;;;GAGG;AACH,cAAc,oBAAoB,CAAC;AACnC,cAAc,eAAe,CAAC;AAE9B,2GAA2G;AAC3G,MAAM,uBAAuB,GAA+B,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,+BAA+B;AACjH,MAAM,sBAAsB,GAA+B,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,+BAA+B;AAEhH,MAAM,CAAC,MAAM,IAAI,GAA+B,YAAY,CAA+D;IACvH,aAAa,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;QACxB,OAAO,CAAC,OAAO,EAAE,EAAE;YACf,IAAI,OAAO,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC3D,uBAAuB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAE3C,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oBACxD,uBAAuB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC5C,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC,CAAC;IACN,CAAC;IACD,YAAY,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;QACvB,OAAO,CAAC,OAAO,EAAE,EAAE;YACf,IAAI,OAAO,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC1D,sBAAsB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAE1C,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oBACvD,sBAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC,CAAC;IACN,CAAC;IACD,WAAW,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;QACtB,OAAO,CAAC,IAAc,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,IAAW,EAAE,EAAE;YACjD,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC;YACxB,MAAM,OAAO,GAAG,oBAAoB,CAAC,uBAAuB,CAAC,CAAC;YAE9D,uBAAuB,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAE7C,MAAM,QAAQ,GAAG,GAAG,EAAE,CAClB,IAAI,CAAC,KAAK,EAAE;gBACR,KAAK;gBACL,GAAG,EAAE,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;gBAC/C,OAAO;gBACP,SAAS,EAAE,UAAU;aACxB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBACT,MAAM,KAAK,GAAG,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAEnD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;oBACtB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;gBAC3D,CAAC;gBAED,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;oBACnB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;oBAEd,+GAA+G;oBAC/G,IAAI,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE,CAAC;wBAClD,QAAQ,EAAE,CAAC;oBACf,CAAC;gBACL,CAAC;YACL,CAAC,CAAC,CAAC;YAEP,QAAQ,EAAE,CAAC;YAEX,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC;IACN,CAAC;IACD,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;QACrB,OAAO,CAAC,IAAc,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,IAAW,EAAE,EAAE;YACjD,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC;YACxB,MAAM,OAAO,GAAG,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;YAE7D,sBAAsB,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAE5C,IAAI,CAAC,KAAK,EAAE;gBACR,KAAK;gBACL,GAAG,EAAE,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;gBAC/C,OAAO;gBACP,SAAS,EAAE,SAAS;aACvB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBACT,MAAM,KAAK,GAAG,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAElD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;oBACtB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;gBAC3D,CAAC;gBAED,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;oBACnB,kEAAkE;oBAClE,sBAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAEvC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;gBAClB,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC;IACN,CAAC;CACJ,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,IAAI,GAA8B,CAAC,GAAW,EAAE,EAAE;IAC3D,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;IAE/B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;AACxB,CAAC,CAAC"}