{"version": 3, "sources": ["../../src/utils/checkObject.ts", "utils/checkObject.js"], "names": ["checkObject", "key", "t"], "mappings": "eAMc,SAAUA,YAAeC;IACrC,IAAMC,WAAWD;IACjB,OAAQC,MAAM,YAAYD,MAAQ,QAASC,MAAM;ACCnD", "file": "checkObject.js", "sourcesContent": ["/**\n * @description Determine whether the type of key is `object`.\n * @param key - The key want to check.\n * @returns Whether the type of key is `object`.\n * @internal\n */\nexport default function checkObject<T>(key: T) {\n  const t = typeof key;\n  return (t === 'object' && key !== null) || t === 'function';\n}\n", "/**\n * @description Determine whether the type of key is `object`.\n * @param key - The key want to check.\n * @returns Whether the type of key is `object`.\n * @internal\n */\nexport default function checkObject(key) {\n    var t = typeof key;\n    return (t === 'object' && key !== null) || t === 'function';\n}\n"]}