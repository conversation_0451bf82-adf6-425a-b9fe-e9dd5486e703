{"version": 3, "sources": ["container/TreeContainer/Base/TreeNode.js", "../../src/container/TreeContainer/Base/TreeNode.ts"], "names": ["Object", "defineProperty", "exports", "value", "TreeNodeEnableIndex", "TreeNode", "constructor", "key", "this", "_color", "_key", "undefined", "_value", "_left", "_right", "_parent", "_pre", "preNode", "pre", "_next", "nextNode", "_rotateLeft", "PP", "V", "R", "_rotateRight", "F", "K", "super", "arguments", "_subTreeSize", "parent", "_recount"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,sBAAsBF,QAAQG,gBAAgB;;ACAhD,MAAOA;IAOXC,YAAYC,GAASJ;QANrBK,KAAAC,KAAM;QACND,KAAAE,IAAsBC;QACtBH,KAAAI,IAAwBD;QACxBH,KAAAK,IAAoCF;QACpCH,KAAAM,IAAqCH;QACrCH,KAAAO,KAAsCJ;QAEpCH,KAAKE,IAAOH;QACZC,KAAKI,IAAST;ADJd;ICUFa;QACE,IAAIC,IAA0BT;QAC9B,IACES,EAAQR,OAAM,KACdQ,EAAQF,GAASA,OAAYE,GAC7B;YACAA,IAAUA,EAAQH;ADNhB,eCOG,IAAIG,EAAQJ,GAAO;YACxBI,IAAUA,EAAQJ;YAClB,OAAOI,EAAQH,GAAQ;gBACrBG,IAAUA,EAAQH;ADLd;AACJ,eCMG;YACL,IAAII,IAAMD,EAAQF;YAClB,OAAOG,EAAIL,MAAUI,GAAS;gBAC5BA,IAAUC;gBACVA,IAAMD,EAAQF;ADJV;YCMNE,IAAUC;ADJR;QCMJ,OAAOD;ADJP;ICUFE;QACE,IAAIC,IAA2BZ;QAC/B,IAAIY,EAASN,GAAQ;YACnBM,IAAWA,EAASN;YACpB,OAAOM,EAASP,GAAO;gBACrBO,IAAWA,EAASP;ADJhB;YCMN,OAAOO;ADJL,eCKG;YACL,IAAIF,IAAME,EAASL;YACnB,OAAOG,EAAIJ,MAAWM,GAAU;gBAC9BA,IAAWF;gBACXA,IAAME,EAASL;ADHX;YCKN,IAAIK,EAASN,MAAWI,GAAK;gBAC3B,OAAOA;ADHH,mBCIC,OAAOE;ADDZ;AACJ;ICOFC;QACE,MAAMC,IAAKd,KAAKO;QAChB,MAAMQ,IAAIf,KAAKM;QACf,MAAMU,IAAID,EAAEV;QAEZ,IAAIS,EAAGP,OAAYP,MAAMc,EAAGP,KAAUQ,QACjC,IAAID,EAAGT,MAAUL,MAAMc,EAAGT,IAAQU,QAClCD,EAAGR,IAASS;QAEjBA,EAAER,KAAUO;QACZC,EAAEV,IAAQL;QAEVA,KAAKO,KAAUQ;QACff,KAAKM,IAASU;QAEd,IAAIA,GAAGA,EAAET,KAAUP;QAEnB,OAAOe;ADFP;ICQFE;QACE,MAAMH,IAAKd,KAAKO;QAChB,MAAMW,IAAIlB,KAAKK;QACf,MAAMc,IAAID,EAAEZ;QAEZ,IAAIQ,EAAGP,OAAYP,MAAMc,EAAGP,KAAUW,QACjC,IAAIJ,EAAGT,MAAUL,MAAMc,EAAGT,IAAQa,QAClCJ,EAAGR,IAASY;QAEjBA,EAAEX,KAAUO;QACZI,EAAEZ,IAASN;QAEXA,KAAKO,KAAUW;QACflB,KAAKK,IAAQc;QAEb,IAAIA,GAAGA,EAAEZ,KAAUP;QAEnB,OAAOkB;ADHP;;;ACKHxB,QAAAG,WAAAA;;AAEK,MAAOD,4BAAkCC;IAA/CC;QDHQsB,SAASC;QCIfrB,KAAAsB,KAAe;AA8BjB;IAzBET;QACE,MAAMU,IAASH,MAAMP;QACrBb,KAAKwB;QACLD,EAAOC;QACP,OAAOD;ADDP;ICOFN;QACE,MAAMM,IAASH,MAAMH;QACrBjB,KAAKwB;QACLD,EAAOC;QACP,OAAOD;ADDP;ICGFC;QACExB,KAAKsB,KAAe;QACpB,IAAItB,KAAKK,GAAO;YACdL,KAAKsB,MAAiBtB,KAAKK,EAAoCiB;ADD7D;QCGJ,IAAItB,KAAKM,GAAQ;YACfN,KAAKsB,MAAiBtB,KAAKM,EAAqCgB;ADD9D;AACJ;;;ACGH5B,QAAAE,sBAAAA", "file": "TreeNode.js", "sourcesContent": ["export class TreeNode {\n    constructor(key, value) {\n        this._color = 1 /* TreeNodeColor.RED */;\n        this._key = undefined;\n        this._value = undefined;\n        this._left = undefined;\n        this._right = undefined;\n        this._parent = undefined;\n        this._key = key;\n        this._value = value;\n    }\n    /**\n     * @description Get the pre node.\n     * @returns TreeNode about the pre node.\n     */\n    _pre() {\n        let preNode = this;\n        if (preNode._color === 1 /* TreeNodeColor.RED */ &&\n            preNode._parent._parent === preNode) {\n            preNode = preNode._right;\n        }\n        else if (preNode._left) {\n            preNode = preNode._left;\n            while (preNode._right) {\n                preNode = preNode._right;\n            }\n        }\n        else {\n            let pre = preNode._parent;\n            while (pre._left === preNode) {\n                preNode = pre;\n                pre = preNode._parent;\n            }\n            preNode = pre;\n        }\n        return preNode;\n    }\n    /**\n     * @description Get the next node.\n     * @returns TreeNode about the next node.\n     */\n    _next() {\n        let nextNode = this;\n        if (nextNode._right) {\n            nextNode = nextNode._right;\n            while (nextNode._left) {\n                nextNode = nextNode._left;\n            }\n            return nextNode;\n        }\n        else {\n            let pre = nextNode._parent;\n            while (pre._right === nextNode) {\n                nextNode = pre;\n                pre = nextNode._parent;\n            }\n            if (nextNode._right !== pre) {\n                return pre;\n            }\n            else\n                return nextNode;\n        }\n    }\n    /**\n     * @description Rotate left.\n     * @returns TreeNode about moved to original position after rotation.\n     */\n    _rotateLeft() {\n        const PP = this._parent;\n        const V = this._right;\n        const R = V._left;\n        if (PP._parent === this)\n            PP._parent = V;\n        else if (PP._left === this)\n            PP._left = V;\n        else\n            PP._right = V;\n        V._parent = PP;\n        V._left = this;\n        this._parent = V;\n        this._right = R;\n        if (R)\n            R._parent = this;\n        return V;\n    }\n    /**\n     * @description Rotate right.\n     * @returns TreeNode about moved to original position after rotation.\n     */\n    _rotateRight() {\n        const PP = this._parent;\n        const F = this._left;\n        const K = F._right;\n        if (PP._parent === this)\n            PP._parent = F;\n        else if (PP._left === this)\n            PP._left = F;\n        else\n            PP._right = F;\n        F._parent = PP;\n        F._right = this;\n        this._parent = F;\n        this._left = K;\n        if (K)\n            K._parent = this;\n        return F;\n    }\n}\nexport class TreeNodeEnableIndex extends TreeNode {\n    constructor() {\n        super(...arguments);\n        this._subTreeSize = 1;\n    }\n    /**\n     * @description Rotate left and do recount.\n     * @returns TreeNode about moved to original position after rotation.\n     */\n    _rotateLeft() {\n        const parent = super._rotateLeft();\n        this._recount();\n        parent._recount();\n        return parent;\n    }\n    /**\n     * @description Rotate right and do recount.\n     * @returns TreeNode about moved to original position after rotation.\n     */\n    _rotateRight() {\n        const parent = super._rotateRight();\n        this._recount();\n        parent._recount();\n        return parent;\n    }\n    _recount() {\n        this._subTreeSize = 1;\n        if (this._left) {\n            this._subTreeSize += this._left._subTreeSize;\n        }\n        if (this._right) {\n            this._subTreeSize += this._right._subTreeSize;\n        }\n    }\n}\n", "export const enum TreeNodeColor {\n  RED = 1,\n  BLACK = 0\n}\n\nexport class TreeNode<K, V> {\n  _color = TreeNodeColor.RED;\n  _key: K | undefined = undefined;\n  _value: V | undefined = undefined;\n  _left: TreeNode<K, V> | undefined = undefined;\n  _right: TreeNode<K, V> | undefined = undefined;\n  _parent: TreeNode<K, V> | undefined = undefined;\n  constructor(key?: K, value?: V) {\n    this._key = key;\n    this._value = value;\n  }\n  /**\n   * @description Get the pre node.\n   * @returns TreeNode about the pre node.\n   */\n  _pre() {\n    let preNode: TreeNode<K, V> = this;\n    if (\n      preNode._color === TreeNodeColor.RED &&\n      preNode._parent!._parent === preNode\n    ) {\n      preNode = preNode._right!;\n    } else if (preNode._left) {\n      preNode = preNode._left;\n      while (preNode._right) {\n        preNode = preNode._right;\n      }\n    } else {\n      let pre = preNode._parent!;\n      while (pre._left === preNode) {\n        preNode = pre;\n        pre = preNode._parent!;\n      }\n      preNode = pre;\n    }\n    return preNode;\n  }\n  /**\n   * @description Get the next node.\n   * @returns TreeNode about the next node.\n   */\n  _next() {\n    let nextNode: TreeNode<K, V> = this;\n    if (nextNode._right) {\n      nextNode = nextNode._right;\n      while (nextNode._left) {\n        nextNode = nextNode._left;\n      }\n      return nextNode;\n    } else {\n      let pre = nextNode._parent!;\n      while (pre._right === nextNode) {\n        nextNode = pre;\n        pre = nextNode._parent!;\n      }\n      if (nextNode._right !== pre) {\n        return pre;\n      } else return nextNode;\n    }\n  }\n  /**\n   * @description Rotate left.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateLeft() {\n    const PP = this._parent!;\n    const V = this._right!;\n    const R = V._left;\n\n    if (PP._parent === this) PP._parent = V;\n    else if (PP._left === this) PP._left = V;\n    else PP._right = V;\n\n    V._parent = PP;\n    V._left = this;\n\n    this._parent = V;\n    this._right = R;\n\n    if (R) R._parent = this;\n\n    return V;\n  }\n  /**\n   * @description Rotate right.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateRight() {\n    const PP = this._parent!;\n    const F = this._left!;\n    const K = F._right;\n\n    if (PP._parent === this) PP._parent = F;\n    else if (PP._left === this) PP._left = F;\n    else PP._right = F;\n\n    F._parent = PP;\n    F._right = this;\n\n    this._parent = F;\n    this._left = K;\n\n    if (K) K._parent = this;\n\n    return F;\n  }\n}\n\nexport class TreeNodeEnableIndex<K, V> extends TreeNode<K, V> {\n  _subTreeSize = 1;\n  /**\n   * @description Rotate left and do recount.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateLeft() {\n    const parent = super._rotateLeft() as TreeNodeEnableIndex<K, V>;\n    this._recount();\n    parent._recount();\n    return parent;\n  }\n  /**\n   * @description Rotate right and do recount.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateRight() {\n    const parent = super._rotateRight() as TreeNodeEnableIndex<K, V>;\n    this._recount();\n    parent._recount();\n    return parent;\n  }\n  _recount() {\n    this._subTreeSize = 1;\n    if (this._left) {\n      this._subTreeSize += (this._left as TreeNodeEnableIndex<K, V>)._subTreeSize;\n    }\n    if (this._right) {\n      this._subTreeSize += (this._right as TreeNodeEnableIndex<K, V>)._subTreeSize;\n    }\n  }\n}\n"]}