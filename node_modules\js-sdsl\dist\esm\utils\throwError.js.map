{"version": 3, "sources": ["../../src/utils/throwError.ts", "utils/throwError.js"], "names": ["throwIteratorAccessError", "RangeError"], "mappings": "OAIM,SAAUA;IACd,MAAM,IAAIC,WAAW;ACCvB", "file": "throwError.js", "sourcesContent": ["/**\n * @description Throw an iterator access error.\n * @internal\n */\nexport function throwIteratorAccessError() {\n  throw new RangeError('Iterator access denied!');\n}\n", "/**\n * @description Throw an iterator access error.\n * @internal\n */\nexport function throwIteratorAccessError() {\n    throw new RangeError('Iterator access denied!');\n}\n"]}