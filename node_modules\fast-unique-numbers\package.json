{"author": "<PERSON>", "browser": "build/es5/bundle.js", "bugs": {"url": "https://github.com/chrisguttandin/fast-unique-numbers/issues"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "dependencies": {"@babel/runtime": "^7.28.4", "tslib": "^2.8.1"}, "description": "A module to create a set of unique numbers as fast as possible.", "devDependencies": {"@babel/cli": "^7.28.3", "@babel/core": "^7.28.4", "@babel/plugin-external-helpers": "^7.27.1", "@babel/plugin-transform-runtime": "^7.28.3", "@babel/preset-env": "^7.28.3", "@babel/register": "^7.28.3", "@commitlint/cli": "^19.8.1", "@commitlint/config-angular": "^19.8.1", "@rollup/plugin-babel": "^6.0.4", "chai": "^4.3.10", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-holy-grail": "^61.0.1", "husky": "^9.1.7", "karma": "^6.4.4", "karma-browserstack-launcher": "^1.6.0", "karma-chrome-launcher": "^3.2.0", "karma-firefox-launcher": "^2.1.3", "karma-mocha": "^2.0.1", "karma-sinon-chai": "^2.0.2", "karma-webkit-launcher": "^2.6.0", "karma-webpack": "^5.0.1", "lint-staged": "^16.1.6", "mocha": "^11.7.2", "prettier": "^3.6.2", "rimraf": "^6.0.1", "rollup": "^4.52.0", "sinon": "^17.0.2", "sinon-chai": "^3.7.0", "tinybench": "^4.0.1", "ts-loader": "^9.5.4", "tsconfig-holy-grail": "^15.0.2", "tslint": "^6.1.3", "tslint-config-holy-grail": "^56.0.6", "typescript": "^5.9.2", "webpack": "^5.101.3"}, "engines": {"node": ">=18.2.0"}, "files": ["build/es2019/", "build/es5/", "build/node/", "src/"], "homepage": "https://github.com/chrisguttandin/fast-unique-numbers", "keywords": ["performance", "speed"], "license": "MIT", "main": "build/node/module.js", "module": "build/es2019/module.js", "name": "fast-unique-numbers", "repository": {"type": "git", "url": "https://github.com/chrisguttandin/fast-unique-numbers.git"}, "scripts": {"build": "rimraf build/* && tsc --project src/tsconfig.json && rollup --config config/rollup/bundle.mjs && babel ./build/es2019 --config-file ./config/babel/build.json --out-dir ./build/node", "lint": "npm run lint:config && npm run lint:src && npm run lint:test", "lint:config": "eslint --config config/eslint/config.json --ext .cjs --ext .js --ext .mjs --report-unused-disable-directives config/", "lint:src": "tslint --config config/tslint/src.json --project src/tsconfig.json src/*.ts src/**/*.ts", "lint:test": "eslint --config config/eslint/test.json --ext .js --report-unused-disable-directives test/", "prepare": "husky", "prepublishOnly": "npm run build", "test": "npm run lint && npm run build && npm run test:expectation-chrome && npm run test:expectation-firefox && npm run test:expectation-node && npm run test:unit-browser && npm run test:unit-node", "test:expectation-chrome": "if [ \"$TYPE\" = \"\" -o \"$TYPE\" = \"expectation\" ] && [ \"$TARGET\" = \"\" -o \"$TARGET\" = \"chrome\" ]; then karma start config/karma/config-expectation-chrome.js --single-run; fi", "test:expectation-firefox": "if [ \"$TYPE\" = \"\" -o \"$TYPE\" = \"expectation\" ] && [ \"$TARGET\" = \"\" -o \"$TARGET\" = \"firefox\" ]; then karma start config/karma/config-expectation-firefox.js --single-run; fi", "test:expectation-node": "if [ \"$TYPE\" = \"\" -o \"$TYPE\" = \"expectation\" ] && [ \"$TARGET\" = \"\" -o \"$TARGET\" = \"node\" ]; then mocha --bail --parallel --recursive --require config/mocha/config-expectation.js test/expectation/node; fi", "test:unit-browser": "if [ \"$TYPE\" = \"\" -o \"$TYPE\" = \"unit\" ] && [ \"$TARGET\" = \"\" -o \"$TARGET\" = \"chrome\" -o \"$TARGET\" = \"firefox\" -o \"$TARGET\" = \"safari\" ]; then karma start config/karma/config-unit.js --single-run; fi", "test:unit-node": "if [ \"$TYPE\" = \"\" -o \"$TYPE\" = \"unit\" ] && [ \"$TARGET\" = \"\" -o \"$TARGET\" = \"node\" ]; then mocha --bail --parallel --recursive --require config/mocha/config-unit.js test/unit; fi"}, "types": "build/es2019/module.d.ts", "version": "9.0.24"}