import { Base, initContainer } from "../ContainerBase";
declare class Stack<T> extends Base {
    constructor(container?: initContainer<T>);
    clear(): void;
    /**
     * @description Insert element to stack's end.
     * @description The element you want to push to the back.
     * @returns The container length after erasing.
     */
    push(element: T): number;
    /**
     * @description Removes the end element.
     * @returns The element you popped.
     */
    pop(): T | undefined;
    /**
     * @description Accesses the end element.
     * @returns The last element.
     */
    top(): T | undefined;
}
export default Stack;
