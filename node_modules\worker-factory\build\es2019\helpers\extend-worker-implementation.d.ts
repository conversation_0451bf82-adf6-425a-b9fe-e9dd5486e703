import { IDefaultWorkerDefinition, IReceiver, IWorkerDefinition } from '../interfaces';
import { TDestroyWorkerFunction, TWorkerImplementation } from '../types';
export declare const extendWorkerImplementation: <WorkerDefinition extends IWorkerDefinition>(createWorker: (receiver: <PERSON><PERSON><PERSON><PERSON><PERSON>, workerImplementation: TWorkerImplementation<WorkerDefinition>) => TDestroyWorkerFunction, partialWorkerImplementation: TWorkerImplementation<WorkerDefinition>, isSupportedFunction: () => boolean | Promise<boolean>) => TWorkerImplementation<WorkerDefinition> & TWorkerImplementation<IDefaultWorkerDefinition>;
//# sourceMappingURL=extend-worker-implementation.d.ts.map