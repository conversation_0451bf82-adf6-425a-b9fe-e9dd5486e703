{"version": 3, "sources": ["container/SequentialContainer/LinkList.js", "../../src/container/SequentialContainer/LinkList.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "__generator", "thisArg", "body", "_", "label", "sent", "t", "trys", "ops", "f", "y", "g", "next", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "step", "op", "done", "value", "pop", "length", "push", "e", "SequentialContainer", "ContainerIterator", "throwIteratorAccessError", "LinkListIterator", "_super", "_node", "_header", "container", "iteratorType", "_this", "pre", "_pre", "_next", "defineProperty", "get", "_value", "set", "newValue", "enumerable", "configurable", "copy", "LinkList", "_head", "_tail", "self", "for<PERSON>ach", "el", "pushBack", "_eraseNode", "node", "_length", "_insertNode", "clear", "begin", "end", "rBegin", "rEnd", "front", "back", "getElementByPos", "pos", "RangeError", "curNode", "eraseElementByPos", "eraseElementByValue", "eraseElementByIterator", "iter", "element", "popBack", "pushFront", "popFront", "setElementByPos", "insert", "num", "i", "find", "reverse", "pHead", "pTail", "cnt", "tmp", "unique", "tmpNode", "sort", "cmp", "arr", "merge", "list", "curNode_1", "callback", "index", "_a", "bind"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;AAe7C,IAAIG,cAAejB,QAAQA,KAAKiB,KAAgB,SAAUC,GAASC;IAC/D,IAAIC,IAAI;QAAEC,OAAO;QAAGC,MAAM;YAAa,IAAIC,EAAE,KAAK,GAAG,MAAMA,EAAE;YAAI,OAAOA,EAAE;AAAI;QAAGC,MAAM;QAAIC,KAAK;OAAMC,GAAGC,GAAGJ,GAAGK;IAC/G,OAAOA,IAAI;QAAEC,MAAMC,KAAK;QAAIC,OAASD,KAAK;QAAIE,QAAUF,KAAK;cAAaG,WAAW,eAAeL,EAAEK,OAAOC,YAAY;QAAa,OAAOlC;AAAM,QAAI4B;IACvJ,SAASE,KAAKK;QAAK,OAAO,SAAUC;YAAK,OAAOC,KAAK,EAACF,GAAGC;AAAK;AAAG;IACjE,SAASC,KAAKC;QACV,IAAIZ,GAAG,MAAM,IAAId,UAAU;QAC3B,OAAOQ;YACH,IAAIM,IAAI,GAAGC,MAAMJ,IAAIe,EAAG,KAAK,IAAIX,EAAE,YAAYW,EAAG,KAAKX,EAAE,cAAcJ,IAAII,EAAE,cAAcJ,EAAEZ,KAAKgB;YAAI,KAAKA,EAAEE,WAAWN,IAAIA,EAAEZ,KAAKgB,GAAGW,EAAG,KAAKC,MAAM,OAAOhB;YAC3J,IAAII,IAAI,GAAGJ,GAAGe,IAAK,EAACA,EAAG,KAAK,GAAGf,EAAEiB;YACjC,QAAQF,EAAG;cACP,KAAK;cAAG,KAAK;gBAAGf,IAAIe;gBAAI;;cACxB,KAAK;gBAAGlB,EAAEC;gBAAS,OAAO;oBAAEmB,OAAOF,EAAG;oBAAIC,MAAM;;;cAChD,KAAK;gBAAGnB,EAAEC;gBAASM,IAAIW,EAAG;gBAAIA,IAAK,EAAC;gBAAI;;cACxC,KAAK;gBAAGA,IAAKlB,EAAEK,IAAIgB;gBAAOrB,EAAEI,KAAKiB;gBAAO;;cACxC;gBACI,MAAMlB,IAAIH,EAAEI,MAAMD,IAAIA,EAAEmB,SAAS,KAAKnB,EAAEA,EAAEmB,SAAS,QAAQJ,EAAG,OAAO,KAAKA,EAAG,OAAO,IAAI;oBAAElB,IAAI;oBAAG;AAAU;gBAC3G,IAAIkB,EAAG,OAAO,OAAOf,KAAMe,EAAG,KAAKf,EAAE,MAAMe,EAAG,KAAKf,EAAE,KAAM;oBAAEH,EAAEC,QAAQiB,EAAG;oBAAI;AAAO;gBACrF,IAAIA,EAAG,OAAO,KAAKlB,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIA,IAAIe;oBAAI;AAAO;gBACpE,IAAIf,KAAKH,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIH,EAAEK,IAAIkB,KAAKL;oBAAK;AAAO;gBAClE,IAAIf,EAAE,IAAIH,EAAEK,IAAIgB;gBAChBrB,EAAEI,KAAKiB;gBAAO;;YAEtBH,IAAKnB,EAAKR,KAAKO,GAASE;UAC1B,OAAOwB;YAAKN,IAAK,EAAC,GAAGM;YAAIjB,IAAI;AAAG,UAAC;YAAWD,IAAIH,IAAI;AAAG;QACzD,IAAIe,EAAG,KAAK,GAAG,MAAMA,EAAG;QAAI,OAAO;YAAEE,OAAOF,EAAG,KAAKA,EAAG,UAAU;YAAGC,MAAM;;AAC9E;AACJ;;OCzCOM,yBAAyB;;SACvBC,yBAAgD;;SAEhDC,gCAA0B;;AAQnC,IAAAC,mBAAA,SAAAC;IAAkClD,UAAAiD,kBAAAC;IAahC,SAAAD,iBACEE,GACAC,GACAC,GACAC;QAJF,IAAAC,IAMEL,EAAAtC,KAAAX,MAAMqD,MAAarD;QACnBsD,EAAKJ,IAAQA;QACbI,EAAKH,IAAUA;QACfG,EAAKF,YAAYA;QACjB,IAAIE,EAAKD,iBAAY,GAA0B;YAC7CC,EAAKC,MAAM;gBACT,IAAIvD,KAAKkD,EAAMM,MAASxD,KAAKmD,GAAS;oBACpCJ;ADuBM;gBCrBR/C,KAAKkD,IAAQlD,KAAKkD,EAAMM;gBACxB,OAAOxD;ADuBH;YCrBNsD,EAAKzB,OAAO;gBACV,IAAI7B,KAAKkD,MAAUlD,KAAKmD,GAAS;oBAC/BJ;ADuBM;gBCrBR/C,KAAKkD,IAAQlD,KAAKkD,EAAMO;gBACxB,OAAOzD;ADuBH;AACJ,eCtBG;YACLsD,EAAKC,MAAM;gBACT,IAAIvD,KAAKkD,EAAMO,MAAUzD,KAAKmD,GAAS;oBACrCJ;ADwBM;gBCtBR/C,KAAKkD,IAAQlD,KAAKkD,EAAMO;gBACxB,OAAOzD;ADwBH;YCtBNsD,EAAKzB,OAAO;gBACV,IAAI7B,KAAKkD,MAAUlD,KAAKmD,GAAS;oBAC/BJ;ADwBM;gBCtBR/C,KAAKkD,IAAQlD,KAAKkD,EAAMM;gBACxB,OAAOxD;ADwBH;AACJ;QACA,OAAOsD;AACX;ICvBFlD,OAAAsD,eAAIV,iBAAAvC,WAAA,WAAO;QDyBLkD,KCzBN;YACE,IAAI3D,KAAKkD,MAAUlD,KAAKmD,GAAS;gBAC/BJ;AD0BM;YCxBR,OAAO/C,KAAKkD,EAAMU;AD0Bd;QACAC,KCzBN,SAAYC;YACV,IAAI9D,KAAKkD,MAAUlD,KAAKmD,GAAS;gBAC/BJ;AD0BM;YCxBR/C,KAAKkD,EAAMU,IAASE;AD0BhB;QACAC,YAAY;QACZC,cAAc;;IC1BpBhB,iBAAAvC,UAAAwD,OAAA;QACE,OAAO,IAAIjB,iBAAoBhD,KAAKkD,GAAOlD,KAAKmD,GAASnD,KAAKoD,WAAWpD,KAAKqD;AD6B9E;ICrBJ,OAAAL;AAAA,CA5EA,CAAkCF;;AAgFlC,IAAAoB,WAAA,SAAAjB;IAA0BlD,UAAAmE,UAAAjB;IAaxB,SAAAiB,SAAYd;QAAA,IAAAA,WAAA,GAAA;YAAAA,IAAA;AAAgC;QAA5C,IAAAE,IACEL,EAAAtC,KAAAX,SAAOA;QACPsD,EAAKH,IAAuB,CAAA;QAC5BG,EAAKa,IAAQb,EAAKc,IAAQd,EAAKH,EAAQK,IAAOF,EAAKH,EAAQM,IAAQH,EAAKH;QACxE,IAAMkB,IAAOf;QACbF,EAAUkB,SAAQ,SAAUC;YAC1BF,EAAKG,SAASD;ADWZ;QACA,OAAOjB;AACX;ICPMY,SAAAzD,UAAAgE,IAAR,SAAmBC;QACT,IAAAlB,IAAgBkB,EAAIlB,GAAdC,IAAUiB,EAAIjB;QAC5BD,EAAKC,IAAQA;QACbA,EAAMD,IAAOA;QACb,IAAIkB,MAAS1E,KAAKmE,GAAO;YACvBnE,KAAKmE,IAAQV;ADYX;QCVJ,IAAIiB,MAAS1E,KAAKoE,GAAO;YACvBpE,KAAKoE,IAAQZ;ADYX;QCVJxD,KAAK2E,KAAW;ADYhB;ICPMT,SAAAzD,UAAAmE,IAAR,SAAoBpC,GAAUe;QAC5B,IAAM1B,IAAO0B,EAAIE;QACjB,IAAMiB,IAAO;YACXd,GAAQpB;YACRgB,GAAMD;YACNE,GAAO5B;;QAET0B,EAAIE,IAAQiB;QACZ7C,EAAK2B,IAAOkB;QACZ,IAAInB,MAAQvD,KAAKmD,GAAS;YACxBnD,KAAKmE,IAAQO;ADYX;QCVJ,IAAI7C,MAAS7B,KAAKmD,GAAS;YACzBnD,KAAKoE,IAAQM;ADYX;QCVJ1E,KAAK2E,KAAW;ADYhB;ICVFT,SAAAzD,UAAAoE,QAAA;QACE7E,KAAK2E,IAAU;QACf3E,KAAKmE,IAAQnE,KAAKoE,IAAQpE,KAAKmD,EAAQK,IAAOxD,KAAKmD,EAAQM,IAAQzD,KAAKmD;ADYxE;ICVFe,SAAAzD,UAAAqE,QAAA;QACE,OAAO,IAAI9B,iBAAoBhD,KAAKmE,GAAOnE,KAAKmD,GAASnD;ADYzD;ICVFkE,SAAAzD,UAAAsE,MAAA;QACE,OAAO,IAAI/B,iBAAoBhD,KAAKmD,GAASnD,KAAKmD,GAASnD;ADY3D;ICVFkE,SAAAzD,UAAAuE,SAAA;QACE,OAAO,IAAIhC,iBAAoBhD,KAAKoE,GAAOpE,KAAKmD,GAASnD,MAAI;ADY7D;ICVFkE,SAAAzD,UAAAwE,OAAA;QACE,OAAO,IAAIjC,iBAAoBhD,KAAKmD,GAASnD,KAAKmD,GAASnD,MAAI;ADY/D;ICVFkE,SAAAzD,UAAAyE,QAAA;QACE,OAAOlF,KAAKmE,EAAMP;ADYlB;ICVFM,SAAAzD,UAAA0E,OAAA;QACE,OAAOnF,KAAKoE,EAAMR;ADYlB;ICVFM,SAAAzD,UAAA2E,kBAAA,SAAgBC;QDYV,ICXsBA,IAAG,KAAHA,IAAQrF,KAAK2E,IAAO,GAhKpC;YAAE,MAAU,IAAIW;AD6KtB;QCZJ,IAAIC,IAAUvF,KAAKmE;QACnB,OAAOkB,KAAO;YACZE,IAAUA,EAAQ9B;ADchB;QCZJ,OAAO8B,EAAQ3B;ADcf;ICZFM,SAAAzD,UAAA+E,oBAAA,SAAkBH;QDcZ,ICbsBA,IAAG,KAAHA,IAAQrF,KAAK2E,IAAO,GAxKpC;YAAE,MAAU,IAAIW;ADuLtB;QCdJ,IAAIC,IAAUvF,KAAKmE;QACnB,OAAOkB,KAAO;YACZE,IAAUA,EAAQ9B;ADgBhB;QCdJzD,KAAKyE,EAAWc;QAChB,OAAOvF,KAAK2E;ADgBZ;ICdFT,SAAAzD,UAAAgF,sBAAA,SAAoB7B;QAClB,IAAI2B,IAAUvF,KAAKmE;QACnB,OAAOoB,MAAYvF,KAAKmD,GAAS;YAC/B,IAAIoC,EAAQ3B,MAAWA,GAAQ;gBAC7B5D,KAAKyE,EAAWc;ADgBZ;YCdNA,IAAUA,EAAQ9B;ADgBhB;QCdJ,OAAOzD,KAAK2E;ADgBZ;ICdFT,SAAAzD,UAAAiF,yBAAA,SAAuBC;QACrB,IAAMjB,IAAOiB,EAAKzC;QAClB,IAAIwB,MAAS1E,KAAKmD,GAAS;YACzBJ;ADgBE;QCdJ4C,IAAOA,EAAK9D;QACZ7B,KAAKyE,EAAWC;QAChB,OAAOiB;ADgBP;ICdFzB,SAAAzD,UAAA+D,WAAA,SAASoB;QACP5F,KAAK4E,EAAYgB,GAAS5F,KAAKoE;QAC/B,OAAOpE,KAAK2E;ADgBZ;ICdFT,SAAAzD,UAAAoF,UAAA;QACE,IAAI7F,KAAK2E,MAAY,GAAG;QACxB,IAAMnC,IAAQxC,KAAKoE,EAAMR;QACzB5D,KAAKyE,EAAWzE,KAAKoE;QACrB,OAAO5B;ADiBP;ICVF0B,SAAAzD,UAAAqF,YAAA,SAAUF;QACR5F,KAAK4E,EAAYgB,GAAS5F,KAAKmD;QAC/B,OAAOnD,KAAK2E;ADiBZ;ICXFT,SAAAzD,UAAAsF,WAAA;QACE,IAAI/F,KAAK2E,MAAY,GAAG;QACxB,IAAMnC,IAAQxC,KAAKmE,EAAMP;QACzB5D,KAAKyE,EAAWzE,KAAKmE;QACrB,OAAO3B;ADkBP;IChBF0B,SAAAzD,UAAAuF,kBAAA,SAAgBX,GAAaO;QDkBvB,ICjBsBP,IAAG,KAAHA,IAAQrF,KAAK2E,IAAO,GAjOpC;YAAE,MAAU,IAAIW;ADoPtB;QClBJ,IAAIC,IAAUvF,KAAKmE;QACnB,OAAOkB,KAAO;YACZE,IAAUA,EAAQ9B;ADoBhB;QClBJ8B,EAAQ3B,IAASgC;ADoBjB;IClBF1B,SAAAzD,UAAAwF,SAAA,SAAOZ,GAAaO,GAAYM;QAAA,IAAAA,WAAA,GAAA;YAAAA,IAAA;AAAO;QDqBjC,ICpBsBb,IAAG,KAAHA,IAAQrF,KAAK2E,GAzO7B;YAAE,MAAU,IAAIW;AD+PtB;QCrBJ,IAAIY,KAAO,GAAG,OAAOlG,KAAK2E;QAC1B,IAAIU,MAAQ,GAAG;YACb,OAAOa,KAAOlG,KAAK8F,UAAUF;ADyB3B,eCxBG,IAAIP,MAAQrF,KAAK2E,GAAS;YAC/B,OAAOuB,KAAOlG,KAAKwE,SAASoB;AD2B1B,eC1BG;YACL,IAAIL,IAAUvF,KAAKmE;YACnB,KAAK,IAAIgC,IAAI,GAAGA,IAAId,KAAOc,GAAG;gBAC5BZ,IAAUA,EAAQ9B;AD4Bd;YC1BN,IAAM5B,IAAO0D,EAAQ9B;YACrBzD,KAAK2E,KAAWuB;YAChB,OAAOA,KAAO;gBACZX,EAAQ9B,IAAqB;oBAC3BG,GAAQgC;oBACRpC,GAAM+B;;gBAERA,EAAQ9B,EAAMD,IAAO+B;gBACrBA,IAAUA,EAAQ9B;AD4Bd;YC1BN8B,EAAQ9B,IAAQ5B;YAChBA,EAAK2B,IAAO+B;AD4BV;QC1BJ,OAAOvF,KAAK2E;AD4BZ;IC1BFT,SAAAzD,UAAA2F,OAAA,SAAKR;QACH,IAAIL,IAAUvF,KAAKmE;QACnB,OAAOoB,MAAYvF,KAAKmD,GAAS;YAC/B,IAAIoC,EAAQ3B,MAAWgC,GAAS;gBAC9B,OAAO,IAAI5C,iBAAoBuC,GAASvF,KAAKmD,GAASnD;AD4BlD;YC1BNuF,IAAUA,EAAQ9B;AD4BhB;QC1BJ,OAAOzD,KAAK+E;AD4BZ;IC1BFb,SAAAzD,UAAA4F,UAAA;QACE,IAAIrG,KAAK2E,KAAW,GAAG;QACvB,IAAI2B,IAAQtG,KAAKmE;QACjB,IAAIoC,IAAQvG,KAAKoE;QACjB,IAAIoC,IAAM;QACV,OAAQA,KAAO,IAAKxG,KAAK2E,GAAS;YAChC,IAAM8B,IAAMH,EAAM1C;YAClB0C,EAAM1C,IAAS2C,EAAM3C;YACrB2C,EAAM3C,IAAS6C;YACfH,IAAQA,EAAM7C;YACd8C,IAAQA,EAAM/C;YACdgD,KAAO;AD6BL;AACJ;IC3BFtC,SAAAzD,UAAAiG,SAAA;QACE,IAAI1G,KAAK2E,KAAW,GAAG;YACrB,OAAO3E,KAAK2E;AD6BV;QC3BJ,IAAIY,IAAUvF,KAAKmE;QACnB,OAAOoB,MAAYvF,KAAKmD,GAAS;YAC/B,IAAIwD,IAAUpB;YACd,OACEoB,EAAQlD,MAAUzD,KAAKmD,KACvBwD,EAAQ/C,MAAW+C,EAAQlD,EAAMG,GACjC;gBACA+C,IAAUA,EAAQlD;gBAClBzD,KAAK2E,KAAW;AD2BZ;YCzBNY,EAAQ9B,IAAQkD,EAAQlD;YACxB8B,EAAQ9B,EAAMD,IAAO+B;YACrBA,IAAUA,EAAQ9B;AD2BhB;QCzBJ,OAAOzD,KAAK2E;AD2BZ;ICzBFT,SAAAzD,UAAAmG,OAAA,SAAKC;QACH,IAAI7G,KAAK2E,KAAW,GAAG;QACvB,IAAMmC,IAAW;QACjB9G,KAAKsE,SAAQ,SAAUC;YACrBuC,EAAInE,KAAK4B;AD4BP;QC1BJuC,EAAIF,KAAKC;QACT,IAAItB,IAAuBvF,KAAKmE;QAChC2C,EAAIxC,SAAQ,SAAUsB;YACpBL,EAAQ3B,IAASgC;YACjBL,IAAUA,EAAQ9B;AD4BhB;AACJ;ICjBFS,SAAAzD,UAAAsG,QAAA,SAAMC;QACJ,IAAM3C,IAAOrE;QACb,IAAIA,KAAK2E,MAAY,GAAG;YACtBqC,EAAK1C,SAAQ,SAAUC;gBACrBF,EAAKG,SAASD;AD4BV;AACJ,eC3BG;YACL,IAAI0C,IAAUjH,KAAKmE;YACnB6C,EAAK1C,SAAQ,SAAUC;gBACrB,OACE0C,MAAY5C,EAAKlB,KACjB8D,EAAQrD,KAAUW,GAClB;oBACA0C,IAAUA,EAAQxD;AD2BZ;gBCzBRY,EAAKO,EAAYL,GAAI0C,EAAQzD;AD2BzB;AACJ;QCzBJ,OAAOxD,KAAK2E;AD2BZ;ICzBFT,SAAAzD,UAAA6D,UAAA,SAAQ4C;QACN,IAAI3B,IAAUvF,KAAKmE;QACnB,IAAIgD,IAAQ;QACZ,OAAO5B,MAAYvF,KAAKmD,GAAS;YAC/B+D,EAAS3B,EAAQ3B,GAAQuD,KAASnH;YAClCuF,IAAUA,EAAQ9B;AD2BhB;AACJ;ICzBFS,SAAAzD,UAACwB,OAAOC,YAAR;QACE,OAAO;YD2BC,IAAIqD;YACJ,OAAOtE,YAAYjB,OAAM,SAAUoH;gBAC/B,QAAQA,EAAG/F;kBACP,KAAK;oBC7BnB,IAAIrB,KAAK2E,MAAY,GAAG,OAAA,EAAA;oBACpBY,IAAUvF,KAAKmE;oBDgCDiD,EAAG/F,QAAQ;;kBACf,KAAK;oBACD,MCjCXkE,MAAYvF,KAAKmD,IAAO,OAAA,EAAA,GAAA;oBAC7B,OAAA,EAAA,GAAMoC,EAAQ3B;;kBDkCF,KAAK;oBClCjBwD,EAAA9F;oBACAiE,IAAUA,EAAQ9B;oBDoCF,OAAO,EAAC,GAAa;;kBACzB,KAAK;oBAAG,OAAO,EAAC;;AAExB;AACJ,UCtCF4D,KAAKrH,KAPA;AD8CP;ICrCJ,OAAAkE;AAAA,CA3RA,CAA0BrB;;eA6RXqB", "file": "LinkList.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport SequentialContainer from './Base';\nimport { ContainerIterator } from \"../ContainerBase\";\nimport $checkWithinAccessParams from \"../../utils/checkParams.macro\";\nimport { throwIteratorAccessError } from \"../../utils/throwError\";\nvar LinkListIterator = /** @class */ (function (_super) {\n    __extends(LinkListIterator, _super);\n    /**\n     * @internal\n     */\n    function LinkListIterator(_node, _header, container, iteratorType) {\n        var _this = _super.call(this, iteratorType) || this;\n        _this._node = _node;\n        _this._header = _header;\n        _this.container = container;\n        if (_this.iteratorType === 0 /* IteratorType.NORMAL */) {\n            _this.pre = function () {\n                if (this._node._pre === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._pre;\n                return this;\n            };\n            _this.next = function () {\n                if (this._node === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._next;\n                return this;\n            };\n        }\n        else {\n            _this.pre = function () {\n                if (this._node._next === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._next;\n                return this;\n            };\n            _this.next = function () {\n                if (this._node === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._pre;\n                return this;\n            };\n        }\n        return _this;\n    }\n    Object.defineProperty(LinkListIterator.prototype, \"pointer\", {\n        get: function () {\n            if (this._node === this._header) {\n                throwIteratorAccessError();\n            }\n            return this._node._value;\n        },\n        set: function (newValue) {\n            if (this._node === this._header) {\n                throwIteratorAccessError();\n            }\n            this._node._value = newValue;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    LinkListIterator.prototype.copy = function () {\n        return new LinkListIterator(this._node, this._header, this.container, this.iteratorType);\n    };\n    return LinkListIterator;\n}(ContainerIterator));\nvar LinkList = /** @class */ (function (_super) {\n    __extends(LinkList, _super);\n    function LinkList(container) {\n        if (container === void 0) { container = []; }\n        var _this = _super.call(this) || this;\n        _this._header = {};\n        _this._head = _this._tail = _this._header._pre = _this._header._next = _this._header;\n        var self = _this;\n        container.forEach(function (el) {\n            self.pushBack(el);\n        });\n        return _this;\n    }\n    /**\n     * @internal\n     */\n    LinkList.prototype._eraseNode = function (node) {\n        var _pre = node._pre, _next = node._next;\n        _pre._next = _next;\n        _next._pre = _pre;\n        if (node === this._head) {\n            this._head = _next;\n        }\n        if (node === this._tail) {\n            this._tail = _pre;\n        }\n        this._length -= 1;\n    };\n    /**\n     * @internal\n     */\n    LinkList.prototype._insertNode = function (value, pre) {\n        var next = pre._next;\n        var node = {\n            _value: value,\n            _pre: pre,\n            _next: next\n        };\n        pre._next = node;\n        next._pre = node;\n        if (pre === this._header) {\n            this._head = node;\n        }\n        if (next === this._header) {\n            this._tail = node;\n        }\n        this._length += 1;\n    };\n    LinkList.prototype.clear = function () {\n        this._length = 0;\n        this._head = this._tail = this._header._pre = this._header._next = this._header;\n    };\n    LinkList.prototype.begin = function () {\n        return new LinkListIterator(this._head, this._header, this);\n    };\n    LinkList.prototype.end = function () {\n        return new LinkListIterator(this._header, this._header, this);\n    };\n    LinkList.prototype.rBegin = function () {\n        return new LinkListIterator(this._tail, this._header, this, 1 /* IteratorType.REVERSE */);\n    };\n    LinkList.prototype.rEnd = function () {\n        return new LinkListIterator(this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    };\n    LinkList.prototype.front = function () {\n        return this._head._value;\n    };\n    LinkList.prototype.back = function () {\n        return this._tail._value;\n    };\n    LinkList.prototype.getElementByPos = function (pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        var curNode = this._head;\n        while (pos--) {\n            curNode = curNode._next;\n        }\n        return curNode._value;\n    };\n    LinkList.prototype.eraseElementByPos = function (pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        var curNode = this._head;\n        while (pos--) {\n            curNode = curNode._next;\n        }\n        this._eraseNode(curNode);\n        return this._length;\n    };\n    LinkList.prototype.eraseElementByValue = function (_value) {\n        var curNode = this._head;\n        while (curNode !== this._header) {\n            if (curNode._value === _value) {\n                this._eraseNode(curNode);\n            }\n            curNode = curNode._next;\n        }\n        return this._length;\n    };\n    LinkList.prototype.eraseElementByIterator = function (iter) {\n        var node = iter._node;\n        if (node === this._header) {\n            throwIteratorAccessError();\n        }\n        iter = iter.next();\n        this._eraseNode(node);\n        return iter;\n    };\n    LinkList.prototype.pushBack = function (element) {\n        this._insertNode(element, this._tail);\n        return this._length;\n    };\n    LinkList.prototype.popBack = function () {\n        if (this._length === 0)\n            return;\n        var value = this._tail._value;\n        this._eraseNode(this._tail);\n        return value;\n    };\n    /**\n     * @description Push an element to the front.\n     * @param element - The element you want to push.\n     * @returns The size of queue after pushing.\n     */\n    LinkList.prototype.pushFront = function (element) {\n        this._insertNode(element, this._header);\n        return this._length;\n    };\n    /**\n     * @description Removes the first element.\n     * @returns The element you popped.\n     */\n    LinkList.prototype.popFront = function () {\n        if (this._length === 0)\n            return;\n        var value = this._head._value;\n        this._eraseNode(this._head);\n        return value;\n    };\n    LinkList.prototype.setElementByPos = function (pos, element) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        var curNode = this._head;\n        while (pos--) {\n            curNode = curNode._next;\n        }\n        curNode._value = element;\n    };\n    LinkList.prototype.insert = function (pos, element, num) {\n        if (num === void 0) { num = 1; }\n        if (pos < 0 || pos > this._length) {\n            throw new RangeError();\n        }\n        if (num <= 0)\n            return this._length;\n        if (pos === 0) {\n            while (num--)\n                this.pushFront(element);\n        }\n        else if (pos === this._length) {\n            while (num--)\n                this.pushBack(element);\n        }\n        else {\n            var curNode = this._head;\n            for (var i = 1; i < pos; ++i) {\n                curNode = curNode._next;\n            }\n            var next = curNode._next;\n            this._length += num;\n            while (num--) {\n                curNode._next = {\n                    _value: element,\n                    _pre: curNode\n                };\n                curNode._next._pre = curNode;\n                curNode = curNode._next;\n            }\n            curNode._next = next;\n            next._pre = curNode;\n        }\n        return this._length;\n    };\n    LinkList.prototype.find = function (element) {\n        var curNode = this._head;\n        while (curNode !== this._header) {\n            if (curNode._value === element) {\n                return new LinkListIterator(curNode, this._header, this);\n            }\n            curNode = curNode._next;\n        }\n        return this.end();\n    };\n    LinkList.prototype.reverse = function () {\n        if (this._length <= 1)\n            return;\n        var pHead = this._head;\n        var pTail = this._tail;\n        var cnt = 0;\n        while ((cnt << 1) < this._length) {\n            var tmp = pHead._value;\n            pHead._value = pTail._value;\n            pTail._value = tmp;\n            pHead = pHead._next;\n            pTail = pTail._pre;\n            cnt += 1;\n        }\n    };\n    LinkList.prototype.unique = function () {\n        if (this._length <= 1) {\n            return this._length;\n        }\n        var curNode = this._head;\n        while (curNode !== this._header) {\n            var tmpNode = curNode;\n            while (tmpNode._next !== this._header &&\n                tmpNode._value === tmpNode._next._value) {\n                tmpNode = tmpNode._next;\n                this._length -= 1;\n            }\n            curNode._next = tmpNode._next;\n            curNode._next._pre = curNode;\n            curNode = curNode._next;\n        }\n        return this._length;\n    };\n    LinkList.prototype.sort = function (cmp) {\n        if (this._length <= 1)\n            return;\n        var arr = [];\n        this.forEach(function (el) {\n            arr.push(el);\n        });\n        arr.sort(cmp);\n        var curNode = this._head;\n        arr.forEach(function (element) {\n            curNode._value = element;\n            curNode = curNode._next;\n        });\n    };\n    /**\n     * @description Merges two sorted lists.\n     * @param list - The other list you want to merge (must be sorted).\n     * @returns The size of list after merging.\n     * @example\n     * const linkA = new LinkList([1, 3, 5]);\n     * const linkB = new LinkList([2, 4, 6]);\n     * linkA.merge(linkB);  // [1, 2, 3, 4, 5];\n     */\n    LinkList.prototype.merge = function (list) {\n        var self = this;\n        if (this._length === 0) {\n            list.forEach(function (el) {\n                self.pushBack(el);\n            });\n        }\n        else {\n            var curNode_1 = this._head;\n            list.forEach(function (el) {\n                while (curNode_1 !== self._header &&\n                    curNode_1._value <= el) {\n                    curNode_1 = curNode_1._next;\n                }\n                self._insertNode(el, curNode_1._pre);\n            });\n        }\n        return this._length;\n    };\n    LinkList.prototype.forEach = function (callback) {\n        var curNode = this._head;\n        var index = 0;\n        while (curNode !== this._header) {\n            callback(curNode._value, index++, this);\n            curNode = curNode._next;\n        }\n    };\n    LinkList.prototype[Symbol.iterator] = function () {\n        return function () {\n            var curNode;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (this._length === 0)\n                            return [2 /*return*/];\n                        curNode = this._head;\n                        _a.label = 1;\n                    case 1:\n                        if (!(curNode !== this._header)) return [3 /*break*/, 3];\n                        return [4 /*yield*/, curNode._value];\n                    case 2:\n                        _a.sent();\n                        curNode = curNode._next;\n                        return [3 /*break*/, 1];\n                    case 3: return [2 /*return*/];\n                }\n            });\n        }.bind(this)();\n    };\n    return LinkList;\n}(SequentialContainer));\nexport default LinkList;\n", "import SequentialContainer from './Base';\nimport { ContainerIterator, initContainer, IteratorType } from '@/container/ContainerBase';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\ntype LinkNode<T> = {\n  _value: T;\n  _pre: LinkNode<T>;\n  _next: LinkNode<T>;\n}\n\nclass LinkListIterator<T> extends ContainerIterator<T> {\n  readonly container: LinkList<T>;\n  /**\n   * @internal\n   */\n  _node: LinkNode<T>;\n  /**\n   * @internal\n   */\n  private readonly _header: LinkNode<T>;\n  /**\n   * @internal\n   */\n  constructor(\n    _node: LinkNode<T>,\n    _header: LinkNode<T>,\n    container: LinkList<T>,\n    iteratorType?: IteratorType\n  ) {\n    super(iteratorType);\n    this._node = _node;\n    this._header = _header;\n    this.container = container;\n    if (this.iteratorType === IteratorType.NORMAL) {\n      this.pre = function () {\n        if (this._node._pre === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre;\n        return this;\n      };\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next;\n        return this;\n      };\n    } else {\n      this.pre = function () {\n        if (this._node._next === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next;\n        return this;\n      };\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre;\n        return this;\n      };\n    }\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    return this._node._value;\n  }\n  set pointer(newValue: T) {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    this._node._value = newValue;\n  }\n  copy() {\n    return new LinkListIterator<T>(this._node, this._header, this.container, this.iteratorType);\n  }\n  // @ts-ignore\n  equals(iter: LinkListIterator<T>): boolean;\n  // @ts-ignore\n  pre(): this;\n  // @ts-ignore\n  next(): this;\n}\n\nexport type { LinkListIterator };\n\nclass LinkList<T> extends SequentialContainer<T> {\n  /**\n   * @internal\n   */\n  private _head: LinkNode<T>;\n  /**\n   * @internal\n   */\n  private _tail: LinkNode<T>;\n  /**\n   * @internal\n   */\n  private readonly _header: LinkNode<T>;\n  constructor(container: initContainer<T> = []) {\n    super();\n    this._header = <LinkNode<T>>{};\n    this._head = this._tail = this._header._pre = this._header._next = this._header;\n    const self = this;\n    container.forEach(function (el) {\n      self.pushBack(el);\n    });\n  }\n  /**\n   * @internal\n   */\n  private _eraseNode(node: LinkNode<T>) {\n    const { _pre, _next } = node;\n    _pre._next = _next;\n    _next._pre = _pre;\n    if (node === this._head) {\n      this._head = _next;\n    }\n    if (node === this._tail) {\n      this._tail = _pre;\n    }\n    this._length -= 1;\n  }\n  /**\n   * @internal\n   */\n  private _insertNode(value: T, pre: LinkNode<T>) {\n    const next = pre._next;\n    const node = {\n      _value: value,\n      _pre: pre,\n      _next: next\n    };\n    pre._next = node;\n    next._pre = node;\n    if (pre === this._header) {\n      this._head = node;\n    }\n    if (next === this._header) {\n      this._tail = node;\n    }\n    this._length += 1;\n  }\n  clear() {\n    this._length = 0;\n    this._head = this._tail = this._header._pre = this._header._next = this._header;\n  }\n  begin() {\n    return new LinkListIterator<T>(this._head, this._header, this);\n  }\n  end() {\n    return new LinkListIterator<T>(this._header, this._header, this);\n  }\n  rBegin() {\n    return new LinkListIterator<T>(this._tail, this._header, this, IteratorType.REVERSE);\n  }\n  rEnd() {\n    return new LinkListIterator<T>(this._header, this._header, this, IteratorType.REVERSE);\n  }\n  front(): T | undefined {\n    return this._head._value;\n  }\n  back(): T | undefined {\n    return this._tail._value;\n  }\n  getElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let curNode = this._head;\n    while (pos--) {\n      curNode = curNode._next;\n    }\n    return curNode._value;\n  }\n  eraseElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let curNode = this._head;\n    while (pos--) {\n      curNode = curNode._next;\n    }\n    this._eraseNode(curNode);\n    return this._length;\n  }\n  eraseElementByValue(_value: T) {\n    let curNode = this._head;\n    while (curNode !== this._header) {\n      if (curNode._value === _value) {\n        this._eraseNode(curNode);\n      }\n      curNode = curNode._next;\n    }\n    return this._length;\n  }\n  eraseElementByIterator(iter: LinkListIterator<T>) {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    iter = iter.next();\n    this._eraseNode(node);\n    return iter;\n  }\n  pushBack(element: T) {\n    this._insertNode(element, this._tail);\n    return this._length;\n  }\n  popBack() {\n    if (this._length === 0) return;\n    const value = this._tail._value;\n    this._eraseNode(this._tail);\n    return value;\n  }\n  /**\n   * @description Push an element to the front.\n   * @param element - The element you want to push.\n   * @returns The size of queue after pushing.\n   */\n  pushFront(element: T) {\n    this._insertNode(element, this._header);\n    return this._length;\n  }\n  /**\n   * @description Removes the first element.\n   * @returns The element you popped.\n   */\n  popFront() {\n    if (this._length === 0) return;\n    const value = this._head._value;\n    this._eraseNode(this._head);\n    return value;\n  }\n  setElementByPos(pos: number, element: T) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let curNode = this._head;\n    while (pos--) {\n      curNode = curNode._next;\n    }\n    curNode._value = element;\n  }\n  insert(pos: number, element: T, num = 1) {\n    $checkWithinAccessParams!(pos, 0, this._length);\n    if (num <= 0) return this._length;\n    if (pos === 0) {\n      while (num--) this.pushFront(element);\n    } else if (pos === this._length) {\n      while (num--) this.pushBack(element);\n    } else {\n      let curNode = this._head;\n      for (let i = 1; i < pos; ++i) {\n        curNode = curNode._next;\n      }\n      const next = curNode._next;\n      this._length += num;\n      while (num--) {\n        curNode._next = <LinkNode<T>>{\n          _value: element,\n          _pre: curNode\n        };\n        curNode._next._pre = curNode;\n        curNode = curNode._next;\n      }\n      curNode._next = next;\n      next._pre = curNode;\n    }\n    return this._length;\n  }\n  find(element: T) {\n    let curNode = this._head;\n    while (curNode !== this._header) {\n      if (curNode._value === element) {\n        return new LinkListIterator<T>(curNode, this._header, this);\n      }\n      curNode = curNode._next;\n    }\n    return this.end();\n  }\n  reverse() {\n    if (this._length <= 1) return;\n    let pHead = this._head;\n    let pTail = this._tail;\n    let cnt = 0;\n    while ((cnt << 1) < this._length) {\n      const tmp = pHead._value;\n      pHead._value = pTail._value;\n      pTail._value = tmp;\n      pHead = pHead._next;\n      pTail = pTail._pre;\n      cnt += 1;\n    }\n  }\n  unique() {\n    if (this._length <= 1) {\n      return this._length;\n    }\n    let curNode = this._head;\n    while (curNode !== this._header) {\n      let tmpNode = curNode;\n      while (\n        tmpNode._next !== this._header &&\n        tmpNode._value === tmpNode._next._value\n      ) {\n        tmpNode = tmpNode._next;\n        this._length -= 1;\n      }\n      curNode._next = tmpNode._next;\n      curNode._next._pre = curNode;\n      curNode = curNode._next;\n    }\n    return this._length;\n  }\n  sort(cmp?: (x: T, y: T) => number) {\n    if (this._length <= 1) return;\n    const arr: T[] = [];\n    this.forEach(function (el) {\n      arr.push(el);\n    });\n    arr.sort(cmp);\n    let curNode: LinkNode<T> = this._head;\n    arr.forEach(function (element) {\n      curNode._value = element;\n      curNode = curNode._next;\n    });\n  }\n  /**\n   * @description Merges two sorted lists.\n   * @param list - The other list you want to merge (must be sorted).\n   * @returns The size of list after merging.\n   * @example\n   * const linkA = new LinkList([1, 3, 5]);\n   * const linkB = new LinkList([2, 4, 6]);\n   * linkA.merge(linkB);  // [1, 2, 3, 4, 5];\n   */\n  merge(list: LinkList<T>) {\n    const self = this;\n    if (this._length === 0) {\n      list.forEach(function (el) {\n        self.pushBack(el);\n      });\n    } else {\n      let curNode = this._head;\n      list.forEach(function (el) {\n        while (\n          curNode !== self._header &&\n          curNode._value <= el\n        ) {\n          curNode = curNode._next;\n        }\n        self._insertNode(el, curNode._pre);\n      });\n    }\n    return this._length;\n  }\n  forEach(callback: (element: T, index: number, list: LinkList<T>) => void) {\n    let curNode = this._head;\n    let index = 0;\n    while (curNode !== this._header) {\n      callback(curNode._value, index++, this);\n      curNode = curNode._next;\n    }\n  }\n  [Symbol.iterator]() {\n    return function * (this: LinkList<T>) {\n      if (this._length === 0) return;\n      let curNode = this._head;\n      while (curNode !== this._header) {\n        yield curNode._value;\n        curNode = curNode._next;\n      }\n    }.bind(this)();\n  }\n}\n\nexport default LinkList;\n"]}