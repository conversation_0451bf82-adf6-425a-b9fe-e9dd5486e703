{"version": 3, "file": "sub.js", "sourceRoot": "", "sources": ["../../src/bin/sub.ts"], "names": [], "mappings": ";;;;;;AAaA,wBAgIC;AA3ID,gDAAuB;AACvB,4CAAmB;AACnB,wDAA+B;AAC/B,sDAA0B;AAE1B,kCAAiC;AAEjC,MAAM,MAAM,GAAG,IAAA,iBAAI,EAAC;IACnB,GAAG,EAAE,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC;CAC3C,CAAC,CAAA;AAEF,SAAwB,KAAK,CAAC,IAAc;IAC3C,MAAM,UAAU,GAAG,IAAA,kBAAQ,EAAC,IAAI,EAAE;QACjC,MAAM,EAAE;YACP,UAAU;YACV,UAAU;YACV,UAAU;YACV,KAAK;YACL,MAAM;YACN,IAAI;YACJ,UAAU;YACV,GAAG;YACH,IAAI;SACJ;QACD,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;QAC/C,KAAK,EAAE;YACN,IAAI,EAAE,GAAG;YACT,QAAQ,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC;YACvB,KAAK,EAAE,GAAG;YACV,GAAG,EAAE,GAAG;YACR,KAAK,EAAE,GAAG;YACV,SAAS,EAAE,GAAG;YACd,QAAQ,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC;YACrB,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;YACpB,OAAO,EAAE,GAAG;YACZ,IAAI,EAAE,IAAI;YACV,EAAE,EAAE,QAAQ;SACZ;QACD,OAAO,EAAE;YACR,IAAI,EAAE,WAAW;YACjB,GAAG,EAAE,CAAC;YACN,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,IAAI;YACX,SAAS,EAAE,EAAE;SACb;KACD,CAAC,CAAA;IAEF,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;QACrB,OAAO,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;IACpC,CAAC;IAED,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAA;IAE3D,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAChC,OAAO,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;IACpC,CAAC;IAED,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC;QACpB,UAAU,CAAC,GAAG,GAAG,YAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;IACjD,CAAC;IAED,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;QACrB,UAAU,CAAC,IAAI,GAAG,YAAE,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IACnD,CAAC;IAED,IAAI,UAAU,CAAC,EAAE,EAAE,CAAC;QACnB,UAAU,CAAC,EAAE,GAAG,YAAE,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;IAC/C,CAAC;IAED,IAAI,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QAC/D,UAAU,CAAC,QAAQ,GAAG,OAAO,CAAA;IAC9B,CAAC;IAED,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;QACzB,UAAU,CAAC,kBAAkB,GAAG,KAAK,CAAA;IACtC,CAAC;IAED,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,OAAO,UAAU,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACzC,OAAO,CAAC,IAAI,CACX,0CAA0C,EAC1C,OAAO,UAAU,CAAC,IAAI,CACtB,CAAA;YACD,OAAM;QACP,CAAC;IACF,CAAC;IAED,IAAI,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;QAC9B,UAAU,CAAC,IAAI,GAAG,EAAE,CAAA;QACpB,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,YAAY,CAAC,CAAA;QAChD,UAAU,CAAC,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,cAAc,CAAC,CAAA;QACpD,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC,UAAU,CAAC,CAAA;QAC5C,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,aAAa,CAAC,CAAA;IACnD,CAAC;IAED,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,CAAA;IAE/C,MAAM,MAAM,GAAG,IAAA,cAAO,EAAC,UAA4B,CAAC,CAAA;IAEpD,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;QACzB,MAAM,CAAC,SAAS,CACf,UAAU,CAAC,KAAK,EAChB,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,EACvB,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACf,IAAI,GAAG,EAAE,CAAC;gBACT,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBAClB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YAChB,CAAC;YAED,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBACtB,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;oBACjB,OAAO,CAAC,KAAK,CACZ,yBAAyB,EACzB,GAAG,CAAC,KAAK,EACT,WAAW,EACX,GAAG,CAAC,GAAG,CACP,CAAA;oBACD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBAChB,CAAC;YACF,CAAC,CAAC,CAAA;QACH,CAAC,CACD,CAAA;IACF,CAAC,CAAC,CAAA;IAEF,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;QACvC,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAA;QACvC,CAAC;aAAM,CAAC;YACP,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAA;QAChC,CAAC;IACF,CAAC,CAAC,CAAA;IAEF,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;QAC1B,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACjB,MAAM,CAAC,GAAG,EAAE,CAAA;IACb,CAAC,CAAC,CAAA;AACH,CAAC;AAED,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC7B,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AAC7B,CAAC"}