export declare const renderMethodNotFoundError: (method: string) => Error & {
    status: number;
};
export declare const renderMissingResponseError: (method: string) => Error & {
    status: number;
};
export declare const renderUnexpectedResultError: (method: string) => Error & {
    status: number;
};
export declare const renderUnknownPortIdError: (portId: number) => Error & {
    status: number;
};
//# sourceMappingURL=error-renderers.d.ts.map