{"version": 3, "sources": ["container/OtherContainer/Queue.js", "../../src/container/OtherContainer/Queue.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "Base", "Queue", "_super", "container", "_this", "_first", "_queue", "self", "for<PERSON>ach", "el", "push", "clear", "_length", "element", "capacity", "length", "length_1", "i", "pop", "front"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;SCApCG,YAAqB;;AAO9B,IAAAC,QAAA,SAAAC;IAAuBpB,UAAAmB,OAAAC;IASrB,SAAAD,MAAYE;QAAA,IAAAA,WAAA,GAAA;YAAAA,IAAA;AAAgC;QAA5C,IAAAC,IACEF,EAAAR,KAAAX,SAAOA;QANDqB,EAAAC,IAAS;QAITD,EAAAE,KAAc;QAGpB,IAAMC,IAAOH;QACbD,EAAUK,SAAQ,SAAUC;YAC1BF,EAAKG,KAAKD;ADYR;QACA,OAAOL;AACX;ICXFH,MAAAT,UAAAmB,QAAA;QACE5B,KAAKuB,KAAS;QACdvB,KAAK6B,IAAU7B,KAAKsB,IAAS;ADa7B;ICNFJ,MAAAT,UAAAkB,OAAA,SAAKG;QACH,IAAMC,IAAW/B,KAAKuB,GAAOS;QAC7B,IACGhC,KAAKsB,IAASS,IAAS,MACvB/B,KAAKsB,IAAStB,KAAK6B,KAAYE,KAChCA,IAAQ,MACR;YACA,IAAME,IAASjC,KAAK6B;YACpB,KAAK,IAAIK,IAAI,GAAGA,IAAID,KAAUC,GAAG;gBAC/BlC,KAAKuB,GAAOW,KAAKlC,KAAKuB,GAAOvB,KAAKsB,IAASY;ADWvC;YCTNlC,KAAKsB,IAAS;YACdtB,KAAKuB,GAAOvB,KAAK6B,KAAWC;ADW1B,eCVG9B,KAAKuB,GAAOvB,KAAKsB,IAAStB,KAAK6B,KAAWC;QACjD,SAAS9B,KAAK6B;ADad;ICPFX,MAAAT,UAAA0B,MAAA;QACE,IAAInC,KAAK6B,MAAY,GAAG;QACxB,IAAMH,IAAK1B,KAAKuB,GAAOvB,KAAKsB;QAC5BtB,KAAK6B,KAAW;QAChB,OAAOH;ADcP;ICRFR,MAAAT,UAAA2B,QAAA;QACE,IAAIpC,KAAK6B,MAAY,GAAG;QACxB,OAAO7B,KAAKuB,GAAOvB,KAAKsB;ADexB;ICbJ,OAAAJ;AAAA,CA3DA,CAAuBD;;eA6DRC", "file": "Queue.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { Base } from \"../ContainerBase\";\nvar Queue = /** @class */ (function (_super) {\n    __extends(Queue, _super);\n    function Queue(container) {\n        if (container === void 0) { container = []; }\n        var _this = _super.call(this) || this;\n        /**\n         * @internal\n         */\n        _this._first = 0;\n        /**\n         * @internal\n         */\n        _this._queue = [];\n        var self = _this;\n        container.forEach(function (el) {\n            self.push(el);\n        });\n        return _this;\n    }\n    Queue.prototype.clear = function () {\n        this._queue = [];\n        this._length = this._first = 0;\n    };\n    /**\n     * @description Inserts element to queue's end.\n     * @param element - The element you want to push to the front.\n     * @returns The container length after pushing.\n     */\n    Queue.prototype.push = function (element) {\n        var capacity = this._queue.length;\n        if ((this._first / capacity) > 0.5 /* QUEUE_CONSTANT.ALLOCATE_SIGMA */ &&\n            (this._first + this._length) >= capacity &&\n            capacity > 4096 /* QUEUE_CONSTANT.MIN_ALLOCATE_SIZE */) {\n            var length_1 = this._length;\n            for (var i = 0; i < length_1; ++i) {\n                this._queue[i] = this._queue[this._first + i];\n            }\n            this._first = 0;\n            this._queue[this._length] = element;\n        }\n        else\n            this._queue[this._first + this._length] = element;\n        return ++this._length;\n    };\n    /**\n     * @description Removes the first element.\n     * @returns The element you popped.\n     */\n    Queue.prototype.pop = function () {\n        if (this._length === 0)\n            return;\n        var el = this._queue[this._first++];\n        this._length -= 1;\n        return el;\n    };\n    /**\n     * @description Access the first element.\n     * @returns The first element.\n     */\n    Queue.prototype.front = function () {\n        if (this._length === 0)\n            return;\n        return this._queue[this._first];\n    };\n    return Queue;\n}(Base));\nexport default Queue;\n", "import { Base, initContainer } from '@/container/ContainerBase';\n\nconst enum QUEUE_CONSTANT {\n  ALLOCATE_SIGMA = 0.5,\n  MIN_ALLOCATE_SIZE = (1 << 12)\n}\n\nclass Queue<T> extends Base {\n  /**\n   * @internal\n   */\n  private _first = 0;\n  /**\n   * @internal\n   */\n  private _queue: T[] = [];\n  constructor(container: initContainer<T> = []) {\n    super();\n    const self = this;\n    container.forEach(function (el) {\n      self.push(el);\n    });\n  }\n  clear() {\n    this._queue = [];\n    this._length = this._first = 0;\n  }\n  /**\n   * @description Inserts element to queue's end.\n   * @param element - The element you want to push to the front.\n   * @returns The container length after pushing.\n   */\n  push(element: T) {\n    const capacity = this._queue.length;\n    if (\n      (this._first / capacity) > QUEUE_CONSTANT.ALLOCATE_SIGMA &&\n      (this._first + this._length) >= capacity &&\n      capacity > QUEUE_CONSTANT.MIN_ALLOCATE_SIZE\n    ) {\n      const length = this._length;\n      for (let i = 0; i < length; ++i) {\n        this._queue[i] = this._queue[this._first + i];\n      }\n      this._first = 0;\n      this._queue[this._length] = element;\n    } else this._queue[this._first + this._length] = element;\n    return ++this._length;\n  }\n  /**\n   * @description Removes the first element.\n   * @returns The element you popped.\n   */\n  pop() {\n    if (this._length === 0) return;\n    const el = this._queue[this._first++];\n    this._length -= 1;\n    return el;\n  }\n  /**\n   * @description Access the first element.\n   * @returns The first element.\n   */\n  front(): T | undefined {\n    if (this._length === 0) return;\n    return this._queue[this._first];\n  }\n}\n\nexport default Queue;\n"]}