Usage: mqtt subscribe [opts] [topic]

Available options:

  -h/--hostname HOST    the broker host
  -p/--port PORT        the broker port
  -i/--clientId ID      the client id
  -q/--qos 0/1/2        the QoS of the message
  --no-clean            do not discard any pending message for
                        the given id
  -t/--topic TOPIC      the message topic
  -k/--keepalive SEC    send a ping every SEC seconds
  -u/--username USER    the username
  -P/--password PASS    the password
  -l/--protocol PROTO   the protocol to use, 'mqtt',
                        'mqtts', 'ws' or 'wss'
  --key PATH            path to the key file
  --cert PATH           path to the cert file
  --ca PATH             path to the ca certificate
  --insecure            do not verify the server certificate
  --will-topic TOPIC    the will topic
  --will-message BODY   the will message
  --will-qos 0/1/2      the will qos
  --will-retain         send a will retained message
  -v/--verbose          print the topic before the message
  -H/--help             show this
